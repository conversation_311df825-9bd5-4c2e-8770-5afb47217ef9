package process

import (
	"context"
	"fmt"

	etlConfig "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	"github.com/csee-pm/etl/shared/notify"
)

func NotifyProcessFinished(c context.Context, processName string, logBuffer string, err error) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	var notifier notify.Notifier

	if conf.Get("notifier") != nil {
		if conf.Get("notifier.email") != nil {
			notifyConf, err := etlConfig.GetEmailConfig(conf, "notifier.email")
			if err != nil {
				logger.Error(err.Error())
			}

			notifier = notify.NewEmailNotifier(
				notifyConf.Host,
				notifyConf.Port,
				notifyConf.User,
				notifyConf.Pass,
				notifyConf.SenderAddress,
				notifyConf.SenderName,
				notifyConf.Receiver...)
		}
	}

	// fmt.Printf("config: %+v\n", cfg.AllSettings())
	msg := fmt.Sprintf("%s process finished successfully.", processName)
	subject := fmt.Sprintf("%s Process Success", processName)
	if err != nil {
		subject = fmt.Sprintf("%s Process Failed", processName)
		msg = fmt.Sprintf("%s process failed. %s", processName, err)
		logger.Error(err.Error())
	}

	logger.Info(fmt.Sprintf("%s process finished", processName))
	msg = fmt.Sprintf("%s\n\n%s", msg, logBuffer)
	if err := sendNotif(notifier, subject, msg); err != nil {
		logger.Error(err.Error())
	}

	return nil
}

func sendNotif(nt notify.Notifier, subject string, message string, attachments ...notify.FileAttachment) error {
	if nt != nil {
		return nt.Notify(subject, notify.TextToHTMLString(message), attachments...)
	}

	return nil
}
