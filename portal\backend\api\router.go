package api

import (
	"embed"
	"encoding/json"
	"io/fs"
	"net/http"
	"strconv"
	"strings"

	"github.com/csee-pm/etl/portal/backend/config"
	"github.com/csee-pm/etl/portal/backend/db"
	"github.com/gorilla/mux"
)

// Router handles HTTP requests
type Router struct {
	db     *db.DB
	config *config.Config
	router *mux.Router
	webFS  embed.FS
}

// NewRouter creates a new router
func NewRouter(database *db.DB, cfg *config.Config, webFS embed.FS) *Router {
	r := &Router{
		db:     database,
		config: cfg,
		router: mux.NewRouter(),
		webFS:  webFS,
	}

	// Register routes
	r.registerRoutes()

	return r
}

// ServeHTTP implements the http.Handler interface
func (r *Router) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	// Handle API requests first
	if strings.HasPrefix(req.URL.Path, "/api/") {
		r.router.ServeHTTP(w, req)
		return
	}

	// Serve embedded frontend files for all other requests
	r.serveFrontend(w, req)
}

// serveFrontend serves the embedded frontend files
func (r *Router) serveFrontend(w http.ResponseWriter, req *http.Request) {
	// Get the embedded filesystem
	webFS, err := fs.Sub(r.webFS, "web")
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Create file server for embedded files
	fileServer := http.FileServer(http.FS(webFS))

	// Handle SPA routing - serve index.html for non-asset requests
	path := req.URL.Path
	if path == "/" {
		path = "/index.html"
	}

	// Check if the requested file exists
	if _, err := webFS.Open(strings.TrimPrefix(path, "/")); err != nil {
		// If file doesn't exist and it's not an asset, serve index.html for SPA routing
		if !strings.HasPrefix(path, "/assets/") && !strings.Contains(path, ".") {
			req.URL.Path = "/index.html"
		}
	}

	fileServer.ServeHTTP(w, req)
}

// registerRoutes registers all API routes
func (r *Router) registerRoutes() {
	// API routes
	api := r.router.PathPrefix("/api").Subrouter()

	// Process routes
	api.HandleFunc("/processes", r.handleListProcesses).Methods("GET")
	api.HandleFunc("/processes/{id:[0-9]+}", r.handleGetProcess).Methods("GET")
	api.HandleFunc("/processes", r.handleCreateProcess).Methods("POST")
	api.HandleFunc("/processes/{id:[0-9]+}", r.handleUpdateProcess).Methods("PUT")

	// File routes
	api.HandleFunc("/files", r.handleListFiles).Methods("GET")
	api.HandleFunc("/files/{id:[0-9]+}", r.handleGetFile).Methods("GET")
	api.HandleFunc("/processes/{id:[0-9]+}/files", r.handleListProcessFiles).Methods("GET")
	api.HandleFunc("/files/{id:[0-9]+}", r.handleDeleteFile).Methods("DELETE")

	// ETL control routes
	api.HandleFunc("/etl/start", r.handleStartETL).Methods("POST")
	api.HandleFunc("/etl/stop/{id:[0-9]+}", r.handleStopETL).Methods("POST")
}

// Helper functions for handling requests

// respondJSON sends a JSON response
func respondJSON(w http.ResponseWriter, status int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	if data != nil {
		if err := json.NewEncoder(w).Encode(data); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		}
	}
}

// respondError sends an error response
func respondError(w http.ResponseWriter, status int, message string) {
	respondJSON(w, status, map[string]string{"error": message})
}

// getIDParam extracts the ID parameter from the URL
func getIDParam(r *http.Request, paramName string) (int64, error) {
	vars := mux.Vars(r)
	idStr, ok := vars[paramName]
	if !ok {
		return 0, nil
	}
	return strconv.ParseInt(idStr, 10, 64)
}
