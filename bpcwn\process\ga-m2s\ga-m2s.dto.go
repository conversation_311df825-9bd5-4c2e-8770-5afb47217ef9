package gam2s

import (
	"strconv"
	"time"

	"cloud.google.com/go/bigquery"
)

type Kpi struct {
	GrossAdds int `db:"ga" bigquery:"ga"`
	M2s       int `db:"m2s" bigquery:"m2s"`
}

type GaM2sData struct {
	MonthID   string              `db:"month_id" bigquery:"month_id"`
	Period    string              `db:"period" bigquery:"period"`
	AsofDate  string              `db:"asof_date" bigquery:"asof_date"`
	Circle    bigquery.NullString `db:"circle" bigquery:"circle"`
	Region    bigquery.NullString `db:"region" bigquery:"region"`
	KabuFlag  bigquery.NullString `db:"flag" bigquery:"flag"`
	Brand     string              `db:"brand" bigquery:"brand"`
	KabuCount int                 `db:"kabu_cnt" bigquery:"kabu_cnt"`
	Kpi
}

func (g GaM2sData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "flag", "brand", "kabu_cnt", "ga", "m2s"}
}

func (g GaM2sData) GetRowValues() []string {
	return []string{g.MonthID, g.Period, g.AsofDate, g.Circle.StringVal, g.Region.StringVal, g.KabuFlag.StringVal, g.Brand, strconv.Itoa(g.KabuCount), strconv.Itoa(g.GrossAdds), strconv.Itoa(g.M2s)}
}

type RegionalGaM2sKabuData struct {
	EntityType string
	EntityName string
	KabuCount  int
	MtdMonth   string
	LmtdMonth  string
	MTD        *Kpi
	LMTD       *Kpi
	FM         map[string]*Kpi
}

type GaM2sReportData struct {
	AsofDate        time.Time
	FmList          []string
	CircleMap       map[string]*RegionalGaM2sKabuData
	RegionalMap     map[string]*RegionalGaM2sKabuData
	RegionalKabuMap map[string]map[string]*RegionalGaM2sKabuData
	NationalData    *RegionalGaM2sKabuData
	NationalKabuMap map[string]*RegionalGaM2sKabuData
}

type GaM2sReport struct {
	IOH   *GaM2sReportData
	IM3   *GaM2sReportData
	Three *GaM2sReportData
}
