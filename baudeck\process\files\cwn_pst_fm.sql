WITH kpi_parameter as (
    select
        circle,
        region,
        a.as_of_dt asof_date,
        a.mth month_id,
        a.parameter,
        a.mthf as period,
        a.brand,
        sum(a.amount) amount
    from
        rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
        left join
        (
            select *
            from rdm.bai_tbl_ref_hirarki_territory_ioh_v1
            where
                mth >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMM')
            and mth < strleft(${mtd_dt_id},6)
        ) b
        on
            a.kec_unik = b.kecamatan
        and a.mth = b.mth
        and a.brand = b.brand
    where
        a.dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
    and a.dt_id < from_timestamp(trunc(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), 'month'), 'yyyyMMdd')
    and a.dt_id = from_timestamp(date_add(trunc(date_add(to_timestamp(a.dt_id, 'yyyyMMdd'), interval 1 month), 'month'), interval -1 day), 'yyyyMMdd')
    and a.parameter in
        (
            'Primary', --im3
            'Secondary', --im3
            'secondary', --3id
            'Tertiary B#', --im3
            'tertiary' --3id
        )
    and
        a.mthf = 'mtd'
    group by
        1,2,3,4,5,6,7

    union all
    --3id
    select
        b.circle,
        a.region,
        a.as_of_dt asof_date,
        a.mth month_id,
        a.parameter,
        mthf as period,
        a.brand,
        sum(a.amt) amount
    from
        rdm.bai_tbl_kpi_dashboard_3id_primseco_mtdlmtd_v1 a
        left join
        (
            select
                distinct
                circle, area, branch, cluster, pt_id, partner, pt_type, mth
            from rdm.bai_tbl_ref_hirarki_territory_ioh_v1
            where
                mth >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMM')
            and mth < strleft(${mtd_dt_id},6)
            and brand = '3ID'
        ) b
        on
            a.pt_id = b.pt_id
        and a.mth = b.mth
    where
        a.dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
    and a.dt_id < from_timestamp(trunc(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), 'month'), 'yyyyMMdd')
    and a.dt_id = from_timestamp(date_add(trunc(date_add(to_timestamp(a.dt_id, 'yyyyMMdd'), interval 1 month), 'month'), interval -1 day), 'yyyyMMdd')
    and b.circle is not NULL
    and a.brand = '3ID'
    and a.mthf in ('mtd')
    and parameter = 'primary'
    group by
        1,2,3,4,5,6,7
)
SELECT
    month_id,
    circle,
    region,
    brand,
    sum(case when parameter in ('Primary', 'primary') then amount else null end) `primary`,
    sum(case when parameter in ('Secondary', 'secondary') then amount else null end) `secondary`,
    sum(case when parameter in ('Tertiary B#', 'tertiary') then amount else null end) `tertiary`
FROM
    kpi_parameter
GROUP BY
    1,2,3,4