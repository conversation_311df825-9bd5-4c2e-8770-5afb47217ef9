package channel

import (
	"context"
	"sync"
)

func MergeChan[T any](ctx context.Context, cs ...<-chan T) chan T {
	var wg sync.WaitGroup
	out := make(chan T)

	// Start an output goroutine for each input channel in cs.  output
	// copies va=lues from c to out until c is closed, then calls wg.Done.
	output := func(c <-chan T) {
		defer func() {
			//fmt.Println("wg.Done()")
			wg.Done()
		}()
		for n := range c {
			select {
			case out <- n:
			case <-ctx.Done():
				return
			}
		}
	}

	wg.Add(len(cs))
	for _, c := range cs {
		go output(c)
	}

	// Start a goroutine to close out once all the output goroutines are
	// done.  This must start after the wg.Add call.
	go func() {
		wg.Wait()
		close(out)
	}()
	return out
}
