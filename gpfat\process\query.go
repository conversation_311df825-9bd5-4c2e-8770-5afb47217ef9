package process

import (
	"context"
	"encoding/csv"
	"fmt"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
)

func RunQuery(c context.Context, query string, args []any, out *csv.Writer, noHeader bool) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	tunnel := conf.Get("tunnel")
	var useTunnel = false
	if tunnel != nil {
		useTunnel = true
	}

	if useTunnel {
		tunConfig, err := cfg.GetTunnelConfig(conf, "tunnel")
		if err != nil {
			return err
		}
		// fmt.Printf("tunConfig: %+v\n", tunConfig)
		tun, err := etlProc.CreateTunnel(tunConfig)
		if err != nil {
			return err
		}

		err = tun.Start()
		if err != nil {
			return err
		}
		defer tun.Close()

		go func() {
			var logf func(string, ...any)
			for msg := range tun.ConnState() {
				strMsg := msg.Msg
				logf = logger.Info
				if msg.Err != nil {
					strMsg = msg.Err.Error()
					logf = logger.Error
				}
				logf(strMsg)
			}
		}()
	}

	gpConfig, err := cfg.GetGPConfig(conf, "gpfat")
	if err != nil {
		return err
	}
	// fmt.Printf("gpConfig: %+v\n", gpConfig)
	db, err := connectToGP(gpConfig)
	if err != nil {
		return err
	}

	err = db.Ping()
	if err != nil {
		return err
	}

	query = db.Rebind(query)
	rows, err := db.Query(query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	header, err := rows.Columns()
	if err != nil {
		return err
	}

	if !noHeader {
		out.Write(header)
	}

	values := make([]any, len(header))
	scanArgs := make([]any, len(values))

	for i := range values {
		scanArgs[i] = &values[i]
	}

	n := 0
	for rows.Next() {
		err = rows.Scan(scanArgs...)
		if err != nil {
			return err
		}

		n++
		record := make([]string, len(values))
		for i, value := range values {
			var val any = value
			switch tval := value.(type) {
			case []byte:
				val = string(tval)
			case nil:
				val = ""
			}

			record[i] = fmt.Sprint(val)
		}

		if err := out.Write(record); err != nil {
			return err
		}

		if n%1000 == 0 {
			fmt.Printf("Exported %d rows\r", n)
		}
	}

	fmt.Printf("Exported %d rows\n", n)
	if err := rows.Err(); err != nil {
		return err
	}

	return nil
}

func connectToGP(cf cfg.GPConfig) (*sqlx.DB, error) {
	connStr := fmt.Sprintf("user=%s password=%s host=%s port=%d dbname=%s sslmode=disable", cf.User, cf.Password, cf.Host, cf.Port, cf.Database)
	return sqlx.Open("postgres", connStr)
}
