package topKpi

import (
	"context"
	"fmt"
	"io/fs"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type TopKpiProcess struct {
	procFS fs.ReadFileFS
}

func NewTopKpiProcess(procFS fs.ReadFileFS) TopKpiProcess {
	return TopKpiProcess{procFS}
}

func (topKpi TopKpiProcess) GetReportData(c context.Context, workDate time.Time) (TopKpiReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return TopKpiReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	data, err := topKpi.getData(c, workDate)
	if err != nil {
		return TopKpiReport{}, err
	}

	workDir := ctx.ExtractWorkDir(c)

	csvFilePath := fmt.Sprintf("%s/top_kpi_%s.csv", workDir, time.Now().Format("20060102150405"))
	err = utils.WriteToCsv(csvFilePath, data)
	if err != nil {
		logger.Error("failed to write Top KPI CSV file", "path", csvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", csvFilePath)
	}

	return topKpi.postProcessData(c, data)
}

func (topKpi TopKpiProcess) GetKabuReportData(c context.Context, workDate time.Time) (TopKpiKabuReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return TopKpiKabuReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	data, err := topKpi.getKabuData(c, workDate)
	if err != nil {
		return TopKpiKabuReport{}, err
	}

	workDir := ctx.ExtractWorkDir(c)

	csvFilePath := fmt.Sprintf("%s/top_kpi_kabu_%s.csv", workDir, time.Now().Format("20060102150405"))
	err = utils.WriteToCsv(csvFilePath, data)
	if err != nil {
		logger.Error("failed to write Top KPI Kabu CSV file", "path", csvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", csvFilePath)
	}

	return topKpi.processKabuData(c, data)
}

func (topKpi TopKpiProcess) getData(c context.Context, mtdDate time.Time) ([]MtdTopKpiData, error) {
	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	logger := ctx.ExtractLogger(c)

	var im3Data []MtdTopKpiData
	var threeData []MtdTopKpiData
	var osaData []MtdTopKpiData
	var err error

	wg.Add(1)
	im3Result := channel.RunAsyncContext(cCancel, func() ([]MtdTopKpiData, error) {
		return topKpi.getIm3Data(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range im3Result {
			res.Map(func(data []MtdTopKpiData) {
				im3Data = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get Top KPI IM3 data. %s", er)
				logger.Debug("calling cancel", "caller", "topKpi.getIm3Data", "error", err)
				cancel()
			})
		}
	}()

	wg.Add(1)
	threeResult := channel.RunAsyncContext(cCancel, func() ([]MtdTopKpiData, error) {
		return topKpi.get3idData(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range threeResult {
			res.Map(func(data []MtdTopKpiData) {
				threeData = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get Top KPI 3ID data. %s", er)
				logger.Debug("calling cancel", "caller", "topKpi.get3idData", "error", err)
				cancel()
			})
		}
	}()

	wg.Add(1)
	osaResult := channel.RunAsyncContext(cCancel, func() ([]MtdTopKpiData, error) {
		return topKpi.getOsa(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range osaResult {
			res.Map(func(data []MtdTopKpiData) {
				osaData = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get OSA data. %s", er)
				logger.Debug("calling cancel", "caller", "topKpi.getOsa", "error", err)
				cancel()
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, err
	}

	kpiData := append(im3Data, threeData...)
	osaMap := make(map[string]float64)

	for _, d := range osaData {
		key := d.Brand + "|" + d.Circle.String + "|" + d.Region.String + "|" + d.Period
		osaMap[key] = d.OSA
	}

	for i := range kpiData {
		key := kpiData[i].Brand + "|" + kpiData[i].Circle.String + "|" + kpiData[i].Region.String + "|" + kpiData[i].Period
		kpiData[i].OSA = osaMap[key]
	}

	return kpiData, nil
}

func (topKpi TopKpiProcess) getIm3Data(c context.Context, mtdDate time.Time) ([]MtdTopKpiData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/im3_top_kpi_mtd.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format("20060102")},
	}

	logger.Info("Getting IM3 TopKPI data")
	return etlProc.QueryImpalaData[MtdTopKpiData](c, string(buf), params)
}

func (topKpi TopKpiProcess) get3idData(c context.Context, mtdDate time.Time) ([]MtdTopKpiData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/3id_top_kpi_mtd.sql")
	if err != nil {
		return nil, err
	}

	mtdDtInt, err := strconv.Atoi(mtdDate.Format("20060102"))
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDtInt},
	}

	logger.Info("Getting 3ID TopKpi data")
	return etlProc.QueryGreenplumData[MtdTopKpiData](c, string(buf), params)
}

func (topKpi TopKpiProcess) getOsa(c context.Context, mtdDate time.Time) ([]MtdTopKpiData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/osa_kpi_mtd.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format("20060102")},
	}

	logger.Info("Getting OSA TopKpi data")
	return etlProc.QueryImpalaData[MtdTopKpiData](c, string(buf), params)
}

func (topKpi TopKpiProcess) getKabuData(c context.Context, mtdDate time.Time) ([]MtdTopKpiKabuData, error) {
	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	logger := ctx.ExtractLogger(c)

	var im3Data []MtdTopKpiKabuData
	var threeData []MtdTopKpiKabuData
	var osaData []MtdTopKpiKabuData
	var err error

	kabumap, err := etlProc.GetMicroMktMap(c)
	if err != nil {
		return nil, err
	}

	wg.Add(1)
	im3Result := channel.RunAsyncContext(cCancel, func() ([]MtdTopKpiKabuData, error) {
		return topKpi.getIm3KabuData(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range im3Result {
			res.Map(func(data []MtdTopKpiKabuData) {
				im3Data = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get IM3 Top Kpi Kabu data. %s", er)
				logger.Debug("calling cancel", "caller", "topKpi.getIm3KabuData", "error", err)
				cancel()
			})
		}
	}()

	wg.Add(1)
	threeResult := channel.RunAsyncContext(cCancel, func() ([]MtdTopKpiKabuData, error) {
		return topKpi.get3idKabuData(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range threeResult {
			res.Map(func(data []MtdTopKpiKabuData) {
				threeData = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get 3ID Top Kpi Kabu data. %s", er)
				logger.Debug("calling cancel", "caller", "topKpi.get3idKabuData", "error", err)
				cancel()
			})
		}
	}()

	wg.Add(1)
	osaResult := channel.RunAsyncContext(cCancel, func() ([]MtdTopKpiKabuData, error) {
		return topKpi.getOsaKabuData(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range osaResult {
			res.Map(func(data []MtdTopKpiKabuData) {
				osaData = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get OSA Top Kpi Kabu data. %s", er)
				logger.Debug("calling cancel", "caller", "topKpi.getOsaKabuData", "error", err)
				cancel()
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, err
	}

	kpiData := append(im3Data, threeData...)
	osaMap := make(map[string]float64)

	for _, d := range osaData {
		key := d.Brand + "|" + d.Circle.String + "|" + d.Region.String + "|" + d.Kabupaten.String + "|" + d.Period
		osaMap[key] = d.OSA
	}

	for i := range kpiData {
		kabu := strings.TrimSpace(strings.ToUpper(kpiData[i].Kabupaten.String))
		if segment, ok := kabumap[kabu]; ok {
			kpiData[i].KabuFlag = null.StringFrom(segment)
		}
		key := kpiData[i].Brand + "|" + kpiData[i].Circle.String + "|" + kpiData[i].Region.String + "|" + kpiData[i].Kabupaten.String + "|" + kpiData[i].Period
		kpiData[i].OSA = osaMap[key]
	}

	return kpiData, nil
}

func (topKpi TopKpiProcess) getIm3KabuData(c context.Context, mtdDate time.Time) ([]MtdTopKpiKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/im3_top_kpi_mtd_kabu.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format("20060102")},
	}

	logger.Info("Getting IM3 TopKPI Kabu data")

	return etlProc.QueryImpalaData[MtdTopKpiKabuData](c, string(buf), params)
}

func (topKpi TopKpiProcess) get3idKabuData(c context.Context, mtdDate time.Time) ([]MtdTopKpiKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/3id_top_kpi_mtd_kabu.sql")
	if err != nil {
		return nil, err
	}

	mtdDtInt, err := strconv.Atoi(mtdDate.Format("20060102"))
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDtInt},
	}

	logger.Info("Getting 3ID TopKpi Kabu data")
	return etlProc.QueryGreenplumData[MtdTopKpiKabuData](c, string(buf), params)
}

func (topKpi TopKpiProcess) getOsaKabuData(c context.Context, mtdDate time.Time) ([]MtdTopKpiKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/osa_kpi_mtd_kabu.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format("20060102")},
	}

	logger.Info("Getting OSA TopKpi Kabu data")
	return etlProc.QueryImpalaData[MtdTopKpiKabuData](c, string(buf), params)
}

//func (topKpi TopKpiProcess) getKabuMapping(c context.Context) (map[string]string, error) {
//	logger := ctx.ExtractLogger(c)
//	rootDir := ctx.ExtractRootDir(c)
//
//	fPath := filepath.Join(rootDir, "kabu_mapping.csv")
//
//	logger.Debug("reading kabu mapping data", "path", fPath)
//
//	// check if file exists
//	if !utils.FileExists(fPath) {
//		return nil, fmt.Errorf("file not found: %s", fPath)
//	}
//
//	f, err := os.Open(fPath)
//	if err != nil {
//		return nil, err
//	}
//	defer f.Close()
//
//	cr := csv.NewReader(f)
//	cr.Comma = ','
//	cr.LazyQuotes = true
//
//	cr.Read() // skip header
//	kabuMap := make(map[string]string)
//	for {
//		record, err := cr.Read()
//		if err == io.EOF {
//			break
//		}
//
//		if err != nil {
//			return nil, err
//		}
//
//		kabu := strings.TrimSpace(strings.ToUpper(record[0]))
//		segment := strings.TrimSpace(strings.ToUpper(record[4]))
//		kabuMap[kabu] = segment
//	}
//
//	return kabuMap, nil
//}

func (topKpi TopKpiProcess) postProcessData(c context.Context, data []MtdTopKpiData) (TopKpiReport, error) {
	brandMap := make(map[string]*TopKpiReportData)
	iohMtdData := &TopKpiReportData{
		Brand: "IOH",
		MTD: &MtdTopKpi{
			Brand:  "IOH",
			Period: "MTD",
			TopKpi: TopKpi{
				Vlr: null.IntFrom(0),
			},
		},
		LMTD: &MtdTopKpi{
			Brand:  "IOH",
			Period: "LMTD",
			TopKpi: TopKpi{
				Vlr: null.IntFrom(0),
			},
		},
	}

	for i := range data {
		d := data[i]
		brand := d.Brand
		if _, ok := brandMap[brand]; !ok {
			brandMap[brand] = &TopKpiReportData{
				Brand:    brand,
				AsofDate: d.AsofDate,
				MTD: &MtdTopKpi{
					Brand:  brand,
					Period: "MTD",
					TopKpi: TopKpi{
						Vlr: null.IntFrom(0),
					},
				},
				LMTD: &MtdTopKpi{
					Brand:  brand,
					Period: "LMTD",
					TopKpi: TopKpi{
						Vlr: null.IntFrom(0),
					},
				},
			}
		}

		var brandData *MtdTopKpi
		var iohData *MtdTopKpi

		if d.Period == "MTD" {
			brandData = brandMap[brand].MTD
			iohData = iohMtdData.MTD
			brandMap[brand].AsofDate = d.AsofDate
			iohMtdData.AsofDate = d.AsofDate
		} else {
			brandData = brandMap[brand].LMTD
			iohData = iohMtdData.LMTD
		}

		brandData.MonthID = d.MonthID
		iohData.MonthID = d.MonthID

		brandData.Period = d.Period
		iohData.Period = d.Period

		brandData.AsofDate = d.AsofDate
		iohData.AsofDate = d.AsofDate

		brandData.RevenueGrossMn += d.RevenueGrossMn
		iohData.RevenueGrossMn += d.RevenueGrossMn

		brandData.TrafficGb += d.TrafficGb
		iohData.TrafficGb += d.TrafficGb

		brandData.Rgu90 += d.Rgu90
		iohData.Rgu90 += d.Rgu90

		brandData.Rgu30 += d.Rgu30
		iohData.Rgu30 += d.Rgu30

		brandData.Vlr.Int64 += d.Vlr.Int64
		iohData.Vlr.Int64 += d.Vlr.Int64

		brandData.GrossAdds += d.GrossAdds
		iohData.GrossAdds += d.GrossAdds

		brandData.PackSubs += d.PackSubs
		iohData.PackSubs += d.PackSubs

		brandData.Quro += d.Quro
		iohData.Quro += d.Quro

		brandData.Qsso += d.Qsso
		iohData.Qsso += d.Qsso

		brandData.OSA += d.OSA
		iohData.OSA += d.OSA

		brandData.DseCount += d.DseCount
		iohData.DseCount += d.DseCount

	}

	im3data := brandMap["IM3"]
	if im3data == nil {
		im3data = &TopKpiReportData{
			Brand: "IM3",
		}
	}

	threeData := brandMap["3ID"]
	if threeData == nil {
		threeData = &TopKpiReportData{
			Brand: "3ID",
		}
	}

	iohData := iohMtdData
	if iohData == nil {
		iohData = &TopKpiReportData{
			Brand: "IOH",
		}
	}

	return TopKpiReport{
		IM3:   *im3data,
		Three: *threeData,
		IOH:   *iohData,
	}, nil
}

func (topKpi TopKpiProcess) processKabuData(c context.Context, data []MtdTopKpiKabuData) (TopKpiKabuReport, error) {
	brandMap := make(map[string]map[string]*TopKpiKabuReportData)
	iohMap := make(map[string]*TopKpiKabuReportData)

	for i := range data {
		d := data[i]
		brand := d.Brand
		flag := strings.ToUpper(d.KabuFlag.String)
		period := d.Period

		var brandData *MtdTopKpiKabuFlag
		var iohData *MtdTopKpiKabuFlag

		if _, ok := brandMap[brand]; !ok {
			brandMap[brand] = make(map[string]*TopKpiKabuReportData)
		}

		if _, ok := brandMap[brand][flag]; !ok {
			brandMap[brand][flag] = &TopKpiKabuReportData{
				Brand:    brand,
				AsofDate: d.AsofDate,
				MTD: &MtdTopKpiKabuFlag{
					Brand:    brand,
					Flag:     flag,
					Period:   "MTD",
					AsofDate: d.AsofDate,
					TopKpi: TopKpi{
						Vlr: null.IntFrom(0),
					},
				},
				LMTD: &MtdTopKpiKabuFlag{
					Brand:    brand,
					Flag:     flag,
					Period:   "LMTD",
					AsofDate: d.AsofDate,
					TopKpi: TopKpi{
						Vlr: null.IntFrom(0),
					},
				},
			}
		}

		if _, ok := iohMap[flag]; !ok {
			iohMap[flag] = &TopKpiKabuReportData{
				Brand:    "IOH",
				AsofDate: d.AsofDate,
				MTD: &MtdTopKpiKabuFlag{
					Brand:    "IOH",
					Flag:     flag,
					Period:   "MTD",
					AsofDate: d.AsofDate,
					TopKpi: TopKpi{
						Vlr: null.IntFrom(0),
					},
				},
				LMTD: &MtdTopKpiKabuFlag{
					Brand:    "IOH",
					Flag:     flag,
					Period:   "LMTD",
					AsofDate: d.AsofDate,
					TopKpi: TopKpi{
						Vlr: null.IntFrom(0),
					},
				},
			}
		}

		if period == "MTD" {
			brandData = brandMap[brand][flag].MTD
			brandMap[brand][flag].AsofDate = d.AsofDate
			iohData = iohMap[flag].MTD
			iohMap[flag].AsofDate = d.AsofDate
		} else {
			brandData = brandMap[brand][flag].LMTD
			iohData = iohMap[flag].LMTD
		}

		brandData.MonthID = d.MonthID
		iohData.MonthID = d.MonthID

		brandData.Period = d.Period
		iohData.Period = d.Period

		brandData.AsofDate = d.AsofDate
		iohData.AsofDate = d.AsofDate

		brandData.RevenueGrossMn += d.RevenueGrossMn
		iohData.RevenueGrossMn += d.RevenueGrossMn

		brandData.TrafficGb += d.TrafficGb
		iohData.TrafficGb += d.TrafficGb

		brandData.Rgu90 += d.Rgu90
		iohData.Rgu90 += d.Rgu90

		brandData.Rgu30 += d.Rgu30
		iohData.Rgu30 += d.Rgu30

		brandData.Vlr.Int64 += d.Vlr.Int64
		iohData.Vlr.Int64 += d.Vlr.Int64

		brandData.GrossAdds += d.GrossAdds
		iohData.GrossAdds += d.GrossAdds

		brandData.PackSubs += d.PackSubs
		iohData.PackSubs += d.PackSubs

		brandData.Quro += d.Quro
		iohData.Quro += d.Quro

		brandData.Qsso += d.Qsso
		iohData.Qsso += d.Qsso

		brandData.OSA += d.OSA
		iohData.OSA += d.OSA

		brandData.DseCount += d.DseCount
		iohData.DseCount += d.DseCount
	}

	im3data := brandMap["IM3"]
	if im3data == nil {
		im3data = make(map[string]*TopKpiKabuReportData)
	}

	threeData := brandMap["3ID"]
	if threeData == nil {
		threeData = make(map[string]*TopKpiKabuReportData)
	}

	return TopKpiKabuReport{
		IM3:   im3data,
		Three: threeData,
		IOH:   iohMap,
	}, nil
}

var (
	TopKpiCol       = 2
	TopMtdCol       = 5
	TopLmtdCol      = 4
	TopGrowthPctCol = 6
	TopGrowthAbsCol = 7

	kabuSegments       = []string{"MUST WIN 50", "SUPER 88", "REST MUST WIN", "WINNING"}
	topKpiKabuColStart = 11
	topKpiKabuKpiCol   = 9
)

func (topKpi TopKpiProcess) WriteReport(xl *excelize.File, data TopKpiReport) error {
	shName := "MTD"
	asof, err := time.Parse("20060102", data.IM3.AsofDate)
	if err != nil {
		asof = time.Now()
	}

	xl.SetCellStr(shName, "C2", asof.Format("02-Jan-2006"))

	if err := topKpi.writeReport(xl, data.IM3, 6, 16); err != nil {
		return err
	}

	if err := topKpi.writeReport(xl, data.Three, 20, 30); err != nil {
		return err
	}

	if err := topKpi.writeReport(xl, data.IOH, 34, 44); err != nil {
		return err
	}

	return nil
}

func (topKpi TopKpiProcess) writeReport(xl *excelize.File, data TopKpiReportData, startRow, endRow int) error {
	shName := "MTD"

	for i := startRow; i <= endRow; i++ {
		kpi, err := xl.GetCellValue(shName, xlutil.Cell(i, TopKpiCol).Address())
		if err != nil {
			return err
		}

		mtdVal, err := topKpi.getKpiValue(kpi, data.MTD)
		if err != nil {
			return err
		}
		xl.SetCellValue(shName, xlutil.Cell(i, TopMtdCol).Address(), mtdVal)

		lmtdVal, err := topKpi.getKpiValue(kpi, data.LMTD)
		if err != nil {
			return err
		}
		xl.SetCellValue(shName, xlutil.Cell(i, TopLmtdCol).Address(), lmtdVal)

		growthPct := (mtdVal - lmtdVal) / lmtdVal
		xl.SetCellValue(shName, xlutil.Cell(i, TopGrowthPctCol).Address(), growthPct)

		growthAbs := mtdVal - lmtdVal
		xl.SetCellValue(shName, xlutil.Cell(i, TopGrowthAbsCol).Address(), growthAbs)
	}
	return nil
}

func (topKpi TopKpiProcess) WriteKabuReport(xl *excelize.File, data TopKpiKabuReport) error {
	if err := topKpi.writeKabuReport(xl, data.IM3, 6, 16); err != nil {
		return err
	}

	if err := topKpi.writeKabuReport(xl, data.Three, 20, 30); err != nil {
		return err
	}

	if err := topKpi.writeKabuReport(xl, data.IOH, 34, 44); err != nil {
		return err
	}

	return nil
}

func (topKpi TopKpiProcess) writeKabuReport(xl *excelize.File, data map[string]*TopKpiKabuReportData, startRow, endRow int) error {
	shName := "MTD"

	for i := startRow; i <= endRow; i++ {
		kpi, err := xl.GetCellValue(shName, xlutil.Cell(i, topKpiKabuKpiCol).Address())
		if err != nil {
			return err
		}

		for j, seg := range kabuSegments {
			_, ok := data[seg]
			if !ok {
				continue
			}

			mtdCol := topKpiKabuColStart + ((j * 4) + 1)
			lmtdCol := topKpiKabuColStart + ((j * 4) + 0)
			growthPctCol := topKpiKabuColStart + ((j * 4) + 2)
			growthAbsCol := topKpiKabuColStart + ((j * 4) + 3)

			mtdVal, err := topKpi.getKabuKpiValue(kpi, data[seg].MTD)
			if err != nil {
				return err
			}
			xl.SetCellValue(shName, xlutil.Cell(i, mtdCol).Address(), mtdVal)

			lmtdVal, err := topKpi.getKabuKpiValue(kpi, data[seg].LMTD)
			if err != nil {
				return err
			}
			xl.SetCellValue(shName, xlutil.Cell(i, lmtdCol).Address(), lmtdVal)

			growthPct := (mtdVal - lmtdVal) / lmtdVal
			xl.SetCellValue(shName, xlutil.Cell(i, growthPctCol).Address(), growthPct)

			growthAbs := mtdVal - lmtdVal
			xl.SetCellValue(shName, xlutil.Cell(i, growthAbsCol).Address(), growthAbs)

		}
	}

	return nil
}

func (topKpi TopKpiProcess) getKpiValue(kpiName string, data *MtdTopKpi) (float64, error) {
	if data == nil {
		return 0, fmt.Errorf("data is nil")
	}

	kpiName = strings.ToLower(kpiName)
	switch kpiName {
	case "rev":
		return data.RevenueGrossMn / 1000, nil
	case "traffic":
		return data.TrafficGb / 1000000, nil
	case "rgu 90":
		return float64(data.Rgu90) / 1000_000, nil
	case "rgu 30":
		return float64(data.Rgu30) / 1000_000, nil
	case "vlr":
		return float64(data.Vlr.Int64) / 1000_000, nil
	case "gross adds":
		return float64(data.GrossAdds) / 1000_000, nil
	case "pack subs":
		return float64(data.PackSubs / 1000_000), nil
	case "quro":
		return float64(data.Quro) / 1000, nil
	case "qsso":
		return float64(data.Qsso) / 1000, nil
	case "ga/dse":
		val := float64(data.GrossAdds) / float64(data.DseCount)
		return val, nil
	case "osa/dse":
		return float64(data.OSA/1000_000) / float64(data.DseCount), nil
	default:
		return 0, fmt.Errorf("invalid kpi name: %s", kpiName)
	}
}

func (topKpi TopKpiProcess) getKabuKpiValue(kpiName string, data *MtdTopKpiKabuFlag) (float64, error) {
	if data == nil {
		return 0, fmt.Errorf("data is nil")
	}

	kpiName = strings.ToLower(kpiName)
	switch kpiName {
	case "rev":
		return data.RevenueGrossMn / 1000, nil
	case "traffic":
		return data.TrafficGb / 1000000, nil
	case "rgu 90":
		return float64(data.Rgu90) / 1000_000, nil
	case "rgu 30":
		return float64(data.Rgu30) / 1000_000, nil
	case "vlr":
		return float64(data.Vlr.Int64) / 1000_000, nil
	case "gross adds":
		return float64(data.GrossAdds) / 1000_000, nil
	case "pack subs":
		return float64(data.PackSubs / 1000_000), nil
	case "quro":
		return float64(data.Quro) / 1000, nil
	case "qsso":
		return float64(data.Qsso) / 1000, nil
	case "ga/dse":
		val := float64(data.GrossAdds) / float64(data.DseCount)
		return val, nil
	case "osa/dse":
		return float64(data.OSA/1000_000) / float64(data.DseCount), nil
	default:
		return 0, fmt.Errorf("invalid kpi name: %s", kpiName)
	}
}
