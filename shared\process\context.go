package process

import (
	"context"
	ctx "github.com/csee-pm/etl/shared/context"
	log "github.com/csee-pm/etl/shared/logger"
	"github.com/spf13/viper"
	"os"
	"path/filepath"
)

func CreateProcessContext(c context.Context, conf *viper.Viper, workDir string, logger log.Logger) context.Context {
	exePath, err := os.Executable()
	if err != nil {
		logger.Error(err.Error())
		exePath = "./distrib"
	}
	exeDir := filepath.Dir(exePath)

	c = context.WithValue(c, ctx.ContextKeyWorkDir, workDir)
	c = context.WithValue(c, ctx.ContextKeyLogger, logger)
	c = context.WithValue(c, ctx.ContextKeyConfig, conf)
	c = context.WithValue(c, ctx.ContextKeyRootDir, exeDir)
	return c
}
