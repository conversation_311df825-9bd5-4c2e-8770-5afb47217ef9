package http

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/fs"
	"mime"
	"net/http"
	"os"
	"path/filepath"

	"github.com/csee-pm/etl/portal/backend/api"
	"github.com/csee-pm/etl/portal/backend/pkg/errs"
	"github.com/go-chi/chi/v5"
	md "github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	log "github.com/likearthian/apikit/logger"
	"github.com/likearthian/apikit/transport"
	htx "github.com/likearthian/apikit/transport/http"
	"golang.org/x/time/rate"
)

var limiter = rate.NewLimiter(10, 20)

func rateLimit(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if !limiter.Allow() {
			http.Error(w, http.StatusText(http.StatusTooManyRequests), http.StatusTooManyRequests)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func NewHTTPServer(endpoints api.APIEndpoints, fe fs.FS, logger log.Logger) chi.Router {
	var allowHeader = []string{"Authorization", "Origin", "Accept", "Content-Type", "Accept-Encoding", "X-Requested-With", "Access-Control-Allow-Origin"}
	var allowMethods = []string{http.MethodGet, http.MethodPost, http.MethodDelete, http.MethodOptions}
	allowCredential := false

	r := chi.NewRouter()
	r.Use(cors.Handler(cors.Options{
		// AllowedOrigins:   allowOrigin,
		AllowedMethods:   allowMethods,
		AllowedHeaders:   allowHeader,
		ExposedHeaders:   nil,
		AllowCredentials: allowCredential,
		//Debug:            true,
	}))

	r.Use(md.Recoverer)

	options := []htx.ServerOption{
		htx.ServerErrorEncoder(errorEncoder),
		// htx.ServerErrorHandler(transport.NewLogErrorHandler(errLogger)),
		htx.ServerErrorHandler(createHttpLogErrorHandler(logger)),
		htx.ServerBefore(htx.PopulateRequestContext, htx.ChiURLParamIntoContext),
		htx.ServerBefore(htx.JWTHTTPRequestToContext, htx.APIKeyRequestToContext),
	}

	version := os.Getenv("VERSION")
	if version == "" {
		version = "pong"
	}

	hostname, err := os.Hostname()
	if err != nil || hostname == "" {
		hostname = "localhost"
	}

	r.HandleFunc("/ping", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(version))
	})

	r.With(md.Logger).With(md.RequestID).With(rateLimit).Mount("/api", createApiRoutes(endpoints, options...))

	r.Handle("/*", http.StripPrefix("/", createAssetHandler(fe)))

	return r
}

func createAssetHandler(assets fs.FS) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// fmt.Println("serve file:", r.URL.Path)
		err := tryRead(assets, r.URL.Path, w)
		if err == nil {
			return
		}
		// 見つからなければindex.htmlを返す
		err = tryRead(assets, "index.html", w)
		if err != nil {
			panic(err)
		}
	}
}

func tryRead(fs fs.FS, requestedPath string, w http.ResponseWriter) error {
	f, err := fs.Open(requestedPath)
	if err != nil {
		return err
	}
	defer f.Close()

	stat, _ := f.Stat()
	if stat.IsDir() {
		return fmt.Errorf("path is dir")
	}

	contentType := mime.TypeByExtension(filepath.Ext(requestedPath))
	w.Header().Set("Content-Type", contentType)
	_, err = io.Copy(w, f)
	return err
}

func createHttpLogErrorHandler(logger log.Logger) transport.ErrorHandlerFunc {
	return func(ctx context.Context, err error) {
		code := errs.Err2Code(err)
		if code == 500 {
			logger.Error(err.Error(), "event", "http request error")
		}
	}
}

func ReqIDFromContext(ctx context.Context) (string, bool) {
	reqID, ok := ctx.Value(md.RequestIDKey).(string)
	if !ok {
		return "", false
	}
	return reqID, ok
}

func errorEncoder(ctx context.Context, err error, w http.ResponseWriter) {
	code := errs.Err2Code(err)
	w.Header().Set(htx.HeaderContentType, htx.HttpContentTypeJson)
	w.WriteHeader(code)
	reqid, ok := ReqIDFromContext(ctx)
	if !ok {
		reqid = ""
	}

	response := errorWrapper{Error: err.Error(), ReqID: reqid, Code: code}
	json.NewEncoder(w).Encode(response)
}

type errorWrapper struct {
	ReqID string `json:"request_id"`
	Code  int    `json:"code"`
	Error string `json:"error"`
}
