package process

import (
	"context"
	"io/fs"
	"time"

	"cloud.google.com/go/bigquery"
	ctxpkg "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
)

type GaProdProcess struct {
	procFS fs.ReadFileFS
}

func NewGaProdProcess(procFS fs.ReadFileFS) *GaProdProcess {
	return &GaProdProcess{
		procFS: procFS,
	}
}

func (g GaProdProcess) StartProcess(c context.Context, startDate time.Time, endDateExc time.Time) error {
	logger := ctxpkg.ExtractLogger(c)

	logger.Info("Starting GA Prod Process")

	loadedDataDate, err := g.LoadDataToStaging(c, startDate, endDateExc)
	if err != nil {
		return err
	}

	return g.UpsertData(c, loadedDataDate)
}

func (g GaProdProcess) LoadDataToStaging(c context.Context, startDate time.Time, endDateExc time.Time) (loadedDataDate map[time.Time]struct{}, err error) {
	logger := ctxpkg.ExtractLogger(c)

	dataChan := make(chan BQGaProdData)
	loadedDataDate = make(map[time.Time]struct{})

	go func() {
		defer close(dataChan)

		buf, err := g.procFS.ReadFile("files/ga_prod.sql")
		if err != nil {
			logger.Error("failed to read ga_prod.sql", "error", err)
			return
		}

		params := map[string]*etlDb.ParamValue{
			"start_dt":   {Name: "start_dt", Value: startDate.Format("20060102")},
			"end_dt_exc": {Name: "end_dt_exc", Value: endDateExc.Format("20060102")},
		}

		logger.Info("Getting GA Prod data")
		iter, err := etlProc.IterImpalaQueryResult[ImpalaGaProdData](c, string(buf), params)
		if err != nil {
			logger.Error("failed to iterate over query result", "error", err)
			return
		}

		for i, data := range iter.Enumerate() {
			bqData, err := data.ToBQGaProdData()
			loadedDataDate[bqData.GaDate] = struct{}{}
			if err != nil {
				logger.Error("failed to convert to BQ data", "error", err, "row", i)
				return
			}

			dataChan <- bqData
		}
	}()

	datasetID := "csee_pm"
	tableID := "stg_as_ga_prod"

	logger.Info("Starting GA Prod Loader")
	err = etlProc.BigQueryStreamBulkLoader(c, datasetID, tableID, dataChan, etlProc.WithBatchSize(10000), etlProc.WithWriteDisposition(bigquery.WriteTruncate))
	return
}

func (g GaProdProcess) UpsertData(c context.Context, loadedDataDate map[time.Time]struct{}) error {
	dateList := utils.MapToList(loadedDataDate, func(t time.Time, _ struct{}) time.Time {
		return t
	})

	if len(dateList) == 0 {
		return nil
	}

	return nil
}
