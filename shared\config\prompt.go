package config

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/csee-pm/etl/shared/enc"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/spf13/viper"
)

func PromptNewImpalaConfig(v *viper.Viper, key string) (ImpalaConfig, error) {
	cf := ImpalaConfig{
		Host:     "udc2-impala-lb.office.corp.indosat.com",
		Port:     21051,
		Database: "default",
	}

	if err := ParseImpalaConfig(v, &cf, key); err != nil {
		return cf, err
	}

	impalaHostPrompt := fmt.Sprintf("Impala Host [%s]: ", cf.Host)
	impalaPortPrompt := fmt.Sprintf("Impala Port [%d]: ", cf.Port)
	impalaDbPrompt := fmt.Sprintf("Impala Database Name [%s]: ", cf.Database)
	impalaCertPrompt := "Impala CA Certificate Path: "
	if cf.CaCert != "" {
		impalaCertPrompt = fmt.Sprintf("Impala CA Certificate Path [%s]: ", cf.CaCert)
	}

	cf.Host = GetPromptValue(impalaHostPrompt, cf.Host, NotEmptyValidator)
	cf.Port = int(utils.StringToInt(GetPromptValue(impalaPortPrompt, fmt.Sprintf("%d", cf.Port), ShouldBeIntValidator)))
	cf.Database = GetPromptValue(impalaDbPrompt, cf.Database, NotEmptyValidator)
	cf.User = GetPromptValue("Impala User: ", "", NotEmptyValidator)
	cf.Password = GetPromptValue("Impala Password: ", "", NotEmptyValidator)
	cf.CaCert = GetPromptValue(impalaCertPrompt, cf.CaCert, NotEmptyValidator)

	creds, err := enc.Encrypt(fmt.Sprintf("%s:%s", cf.User, cf.Password))
	if err != nil {
		return cf, err
	}

	cf.Credential = creds

	return cf, nil
}

func PromptNewHdfsConfig() (HdfsConfig, error) {
	cf := HdfsConfig{}

	cf.HdfsNode = GetPromptValue("HDFS Node and Port[hdp2-ams0002:8020]: ", "hdp2-ams0002:8020", NotEmptyValidator)
	cf.HdfsPrincipalName = GetPromptValue("HDFS Principal Name: ", "", NotEmptyValidator)
	cf.Realm = GetPromptValue("HDFS Realm[OFFICE.CORP.INDOSAT.COM]: ", "OFFICE.CORP.INDOSAT.COM", NotEmptyValidator)
	cf.KeytabFile = GetPromptValue("HDFS Keytab File Path: ", "", NotEmptyValidator)
	cf.Kdc = GetPromptValue("HDFS KDC[poaddc02w.office.corp.indosat.com]: ", "poaddc02w.office.corp.indosat.com", NotEmptyValidator)

	return cf, nil
}

func PromptNewGPConfig(v *viper.Viper, key string) (GPConfig, error) {
	cf := GPConfig{
		Host:     "localhost",
		Port:     2345,
		Database: "pdwh",
	}

	hostPrompt := fmt.Sprintf("Greenplum Host [%s]: ", cf.Host)
	portPrompt := fmt.Sprintf("Greenplum Port [%d]: ", cf.Port)
	dbPrompt := fmt.Sprintf("Greenplum Database Name [%s]: ", cf.Database)

	cf.Host = GetPromptValue(hostPrompt, cf.Host, NotEmptyValidator)
	cf.Port = int(utils.StringToInt(GetPromptValue(portPrompt, fmt.Sprintf("%d", cf.Port), ShouldBeIntValidator)))
	cf.Database = GetPromptValue(dbPrompt, cf.Database, NotEmptyValidator)
	cf.User = GetPromptValue("Greenplum User: ", "", NotEmptyValidator)
	cf.Password = GetPromptValue("Greenplum Password: ", "", NotEmptyValidator)

	creds, err := enc.Encrypt(fmt.Sprintf("%s:%s", cf.User, cf.Password))
	if err != nil {
		return cf, err
	}

	cf.Credential = creds

	return cf, nil
}

func PromptNewTunnelConfig(v *viper.Viper, key string) (*TunnelConfig, error) {
	var cf = TunnelConfig{
		Ssh: SSHConfig{
			Port: 22,
		},
	}

	if err := ParseTunnelConfig(v, &cf, key); err != nil {
		return &cf, err
	}

	sshHostPrompt := "SSH Host: "
	if cf.Ssh.Host != "" {
		sshHostPrompt = fmt.Sprintf("SSH Host [%s]: ", cf.Ssh.Host)
	}

	sshPortPrompt := fmt.Sprintf("SSH Port [%d]: ", cf.Ssh.Port)
	localPortPrompt := fmt.Sprintf("Local Port [%d]: ", cf.LocalPort)
	destinationPrompt := "Destination :"
	if cf.Destination != "" {
		destinationPrompt = fmt.Sprintf("Destination [%s]: ", cf.Destination)
	}

	cf.Ssh.Host = GetPromptValue(sshHostPrompt, cf.Ssh.Host, NotEmptyValidator)
	cf.Ssh.User = GetPromptValue("SSH User: ", cf.Ssh.User, NotEmptyValidator)
	cf.Ssh.Port = int(utils.StringToInt(GetPromptValue(sshPortPrompt, fmt.Sprintf("%d", cf.Ssh.Port), ShouldBeIntValidator)))
	cf.Ssh.PrivateKeyFile = GetPromptValue("SSH Private Key File: ", "", NotEmptyValidator)
	cf.LocalPort = int(utils.StringToInt(GetPromptValue(localPortPrompt, fmt.Sprintf("%d", cf.LocalPort), ShouldBeIntValidator)))
	cf.Destination = GetPromptValue(destinationPrompt, cf.Destination, NotEmptyValidator)

	return &cf, nil
}

func PromptNewSSHConfig(v *viper.Viper, key string) (SSHConfig, error) {
	cf := SSHConfig{
		Port: 22,
	}

	if err := ParseSSHConfig(v, &cf, key); err != nil {
		return cf, err
	}

	hostPrompt := "SSH Host: "
	if cf.Host != "" {
		hostPrompt = fmt.Sprintf("SSH Host [%s]: ", cf.Host)
	}

	portPrompt := fmt.Sprintf("SSH Port [%d]: ", cf.Port)

	cf.Host = GetPromptValue(hostPrompt, cf.Host, NotEmptyValidator)
	cf.User = GetPromptValue("SSH User: ", "", NotEmptyValidator)
	cf.Port = int(utils.StringToInt(GetPromptValue(portPrompt, fmt.Sprintf("%d", cf.Port), ShouldBeIntValidator)))
	cf.PrivateKeyFile = GetPromptValue("SSH Private Key File: ", "", NotEmptyValidator)

	return cf, nil
}

func PromptNewEmailConfig(v *viper.Viper, key string) (EmailConfig, error) {
	cf := EmailConfig{
		Host:          "************",
		Port:          25,
		SenderAddress: "<EMAIL>",
		SenderName:    "CSEE Admin",
	}

	if err := ParseEmailConfig(v, &cf, key); err != nil {
		return cf, err
	}

	hostPrompt := "Email Server Host: "
	if cf.Host != "" {
		hostPrompt = fmt.Sprintf("Email Server Host [%s]: ", cf.Host)
	}

	portPrompt := fmt.Sprintf("Email Server Port [%d]: ", cf.Port)
	senderPrompt := fmt.Sprintf("Email Sender Address [%s]: ", cf.SenderAddress)
	senderNamePrompt := fmt.Sprintf("Email Sender Name [%s]: ", cf.SenderName)

	cf.Host = GetPromptValue(hostPrompt, cf.Host, NotEmptyValidator)
	cf.Port = int(utils.StringToInt(GetPromptValue(portPrompt, fmt.Sprintf("%d", cf.Port), ShouldBeIntValidator)))
	cf.User = GetPromptValue("User: ", "", NotEmptyValidator)
	cf.Pass = GetPromptValue("Password: ", "", NotEmptyValidator)
	cf.SenderAddress = GetPromptValue(senderPrompt, cf.SenderAddress, NotEmptyValidator)
	cf.SenderName = GetPromptValue(senderNamePrompt, cf.SenderName, NotEmptyValidator)

	receivers := GetPromptValue("Email Receivers (separate by semicolon \";\" for multiple receivers): ", "", NotEmptyValidator)
	cf.Receiver = strings.Split(receivers, ";")

	creds, err := enc.Encrypt(fmt.Sprintf("%s:%s", cf.User, cf.Pass))
	if err != nil {
		return cf, err
	}

	cf.Credential = creds
	return cf, nil
}

func PromptNewEmailServerConfig(v *viper.Viper, key string) (EmailServer, error) {
	cf := EmailServer{
		Host:          "************",
		Port:          25,
		SenderAddress: "<EMAIL>",
		SenderName:    "CSEE Admin",
	}

	if err := ParseConfig(v, &cf, key); err != nil {
		return cf, err
	}

	hostPrompt := "Email Server Host: "
	if cf.Host != "" {
		hostPrompt = fmt.Sprintf("Email Server Host [%s]: ", cf.Host)
	}

	portPrompt := fmt.Sprintf("Email Server Port [%d]: ", cf.Port)
	senderPrompt := fmt.Sprintf("Email Sender Address [%s]: ", cf.SenderAddress)
	senderNamePrompt := fmt.Sprintf("Email Sender Name [%s]: ", cf.SenderName)

	cf.Host = GetPromptValue(hostPrompt, cf.Host, NotEmptyValidator)
	cf.Port = int(utils.StringToInt(GetPromptValue(portPrompt, fmt.Sprintf("%d", cf.Port), ShouldBeIntValidator)))
	cf.User = GetPromptValue("User: ", "", NotEmptyValidator)
	cf.Pass = GetPromptValue("Password: ", "", NotEmptyValidator)
	cf.SenderAddress = GetPromptValue(senderPrompt, cf.SenderAddress, NotEmptyValidator)
	cf.SenderName = GetPromptValue(senderNamePrompt, cf.SenderName, NotEmptyValidator)

	creds, err := enc.Encrypt(fmt.Sprintf("%s:%s", cf.User, cf.Pass))
	if err != nil {
		return cf, err
	}

	cf.Credential = creds
	return cf, nil
}

func GetPromptValue(prompt string, def string, validateFn func(string) error) string {
	var value string
	fmt.Print(prompt)
	fmt.Scanln(&value)

	if value == "" {
		value = def
	}

	if validateFn != nil {
		err := validateFn(value)
		if err != nil {
			fmt.Println(err)
			return GetPromptValue(prompt, def, validateFn)
		}
	}

	return value
}

func NotEmptyValidator(value string) error {
	if value == "" {
		return fmt.Errorf("value cannot be empty")
	}
	return nil
}

func ShouldBeIntValidator(value string) error {
	_, err := strconv.Atoi(value)
	if err != nil {
		return fmt.Errorf("value should be an integer")
	}
	return nil
}

func ShouldBeYesOrNo(value string) error {
	value = strings.ToLower(value)
	if value != "y" && value != "n" {
		return fmt.Errorf("value should be y or n")
	}
	return nil
}

func NoValidation(value string) error {
	return nil
}
