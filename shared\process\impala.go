package process

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strconv"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	"github.com/jmoiron/sqlx"
)

func QueryImpalaDataFromSQLFile[T any](c context.Context, filePath string, params map[string]*etlDb.ParamValue) ([]T, error) {
	buf, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	return QueryImpalaData[T](c, string(buf), params)
}

func QueryImpalaData[T any](c context.Context, query string, params map[string]*etlDb.ParamValue) ([]T, error) {
	logger := ctx.ExtractLogger(c)

	iplClient, err := createImpalaClient(c)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Impala DB. %s", err)
	}

	if err := iplClient.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping impala. %s", err)
	}

	logger.Debug("successfully connected to impala")

	query, args, err := etlDb.ParseSqlWithParams(query, params)
	if err != nil {
		return nil, err
	}

	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}

	// logger.Debug("executing impala query", "query", query, "args", args)

	var data []T
	if err := iplClient.Select(&data, query, args...); err != nil {
		return nil, fmt.Errorf("failed to execute query. %s", err)
	}

	return data, nil
}

func IterImpalaQueryResult[T any](c context.Context, query string, params map[string]*etlDb.ParamValue) (*RowIterator[T], error) {
	iplClient, err := createImpalaClient(c)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Impala DB. %s", err)
	}

	query, args, err := etlDb.ParseSqlWithParams(query, params)
	if err != nil {
		return nil, err
	}

	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}

	// logger.Debug("executing impala query", "query", query, "args", args)

	rows, err := iplClient.QueryxContext(c, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query. %s", err)
	}

	return newRowIterator[T](rows), nil
}

func createImpalaClient(c context.Context) (*sqlx.DB, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	iplConfig, err := cfg.GetConfig[cfg.ImpalaConfig](conf, "impala")
	if err != nil {
		return nil, fmt.Errorf("failed to get impala config. %s", err)
	}

	cacert := iplConfig.CaCert
	if filepath.Dir(cacert) == "." {
		cacert = filepath.Join(ctx.ExtractRootDir(c), cacert)
	}

	impalaConf := etlDb.ImpalaConfig{
		User:     iplConfig.User,
		Password: iplConfig.Password,
		Host:     iplConfig.Host,
		Port:     strconv.Itoa(iplConfig.Port),
		Cacerts:  cacert,
	}

	iplClient, err := etlDb.CreateImpalaSqlClient(impalaConf, etlDb.UseInsecureTLS())
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Impala DB. %s", err)
	}

	if err := iplClient.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping impala. %s", err)
	}

	logger.Debug("successfully connected to impala")

	return iplClient, nil
}
