package pst

import (
	"strconv"
	"time"

	"gopkg.in/guregu/null.v4"
)

type PSTKpi struct {
	Primary   null.Float `db:"primary"`
	Secondary null.Float `db:"secondary"`
	Tertiary  null.Float `db:"tertiary"`
}

type RegionPSTKpi struct {
	Circle null.String `db:"circle"`
	Region null.String `db:"region"`
	Brand  string      `db:"brand"`
	PSTKpi
}

type MtdPSTData struct {
	MonthID  string `db:"month_id"`
	Period   string `db:"period"`
	AsofDate string `db:"asof_date"`
	RegionPSTKpi
}

func (mt MtdPSTData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "brand", "primary", "secondary", "tertiary"}
}

func (mt MtdPSTData) GetRowValues() []string {
	primary := ""
	if mt.Primary.Valid {
		primary = strconv.FormatFloat(mt.Primary.ValueOrZero(), 'f', -1, 64)
	}

	secondary := ""
	if mt.Secondary.Valid {
		secondary = strconv.FormatFloat(mt.Secondary.ValueOrZero(), 'f', -1, 64)
	}

	tertiary := ""
	if mt.Tertiary.Valid {
		tertiary = strconv.FormatFloat(mt.Tertiary.ValueOrZero(), 'f', -1, 64)
	}

	return []string{mt.MonthID, mt.Period, mt.AsofDate, mt.Circle.String, mt.Region.String, mt.Brand, primary, secondary, tertiary}
}

type FmPSTData struct {
	MonthID string `db:"month_id"`
	RegionPSTKpi
}

func (fm FmPSTData) GetColumns() []string {
	return []string{"month_id", "circle", "region", "brand", "primary", "secondary", "tertiary"}
}

func (fm FmPSTData) GetRowValues() []string {
	primary := ""
	if fm.Primary.Valid {
		primary = strconv.FormatFloat(fm.Primary.ValueOrZero(), 'f', -1, 64)
	}

	secondary := ""
	if fm.Secondary.Valid {
		secondary = strconv.FormatFloat(fm.Secondary.ValueOrZero(), 'f', -1, 64)
	}

	tertiary := ""
	if fm.Tertiary.Valid {
		tertiary = strconv.FormatFloat(fm.Tertiary.ValueOrZero(), 'f', -1, 64)
	}

	return []string{fm.MonthID, fm.Circle.String, fm.Region.String, fm.Brand, primary, secondary, tertiary}
}

type MtdPSTKpi struct {
	MTD  MtdPSTData
	LMTD MtdPSTData
}

type MtdPSTReportData struct {
	CircleMtdIm3   map[string]*MtdPSTKpi
	CircleMtd3id   map[string]*MtdPSTKpi
	CircleMtdIOH   map[string]*MtdPSTKpi
	RegionalMtdIm3 map[string]*MtdPSTKpi
	RegionalMtd3id map[string]*MtdPSTKpi
	RegionalMtdIOH map[string]*MtdPSTKpi
}

type FmPSTReportData struct {
	CircleFmIm3   map[string]map[string]*FmPSTData
	CircleFm3id   map[string]map[string]*FmPSTData
	CircleFmIOH   map[string]map[string]*FmPSTData
	RegionalFmIm3 map[string]map[string]*FmPSTData
	RegionalFm3id map[string]map[string]*FmPSTData
	RegionalFmIOH map[string]map[string]*FmPSTData
}

type ProcessedPSTReportData struct {
	FmList    []string
	MTDMonth  time.Time
	LMTDMonth time.Time
	MTD       MtdPSTReportData
	Fm        FmPSTReportData
}

type PSTReportData struct {
	FmList      []string
	MTDMonth    time.Time
	LMTDMonth   time.Time
	CircleMtd   map[string]*MtdPSTKpi
	RegionalMtd map[string]*MtdPSTKpi
	CircleFm    map[string]map[string]*FmPSTData
	RegionalFm  map[string]map[string]*FmPSTData
}

type PSTReport struct {
	MtdDate time.Time
	IM3     *PSTReportData
	Three   *PSTReportData
	IOH     *PSTReportData
}
