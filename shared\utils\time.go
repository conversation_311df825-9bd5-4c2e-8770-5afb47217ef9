package utils

import (
	"sort"
	"strings"
)

var daysInMonth = []int{
	31, // January
	28, // February (nned to calculate for leap years in function IsLeapYear below)
	31, // March
	30, // April
	31, // May
	30, // June
	31, // July
	31, // August
	30, // September
	31, // October
	30, // November
	31, // December
}

func ToGoDateFormat(format string) string {
	// Map common date and time components to Go's reference date
	replacements := map[string]string{
		// Year
		"YYYY": "2006", // 4-digit year
		"YY":   "06",   // 2-digit year

		// Month
		"MMMM": "January", // full month name
		"MMM":  "Jan",     // abbreviated month name
		"MM":   "01",      // 2-digit month
		"M":    "1",       // 1-digit month

		// Day
		"DD": "02", // 2-digit day
		"D":  "2",  // 1-digit day

		// Time - 24 hour
		"HH": "15", // 2-digit hour (24-hour)
		"H":  "15", // 1-digit hour (24-hour)

		// Time - 12 hour
		"hh": "03", // 2-digit hour (12-hour)
		"h":  "3",  // 1-digit hour (12-hour)

		// Minutes & Seconds
		"mm": "04", // 2-digit minute
		"m":  "4",  // 1-digit minute
		"ss": "05", // 2-digit second
		"s":  "5",  // 1-digit second

		// Milliseconds/Microseconds
		"SSS":    "000",    // milliseconds
		"SSSSSS": "000000", // microseconds

		// AM/PM
		"A": "PM", // uppercase AM/PM
		"a": "pm", // lowercase am/pm

		// Timezone
		"Z":  "-0700",  // UTC offset
		"ZZ": "-07:00", // UTC offset with colon
		"z":  "MST",    // timezone abbreviation
	}

	result := format

	// Sort replacements by length (descending) to avoid partial replacements
	type replacement struct {
		from, to string
	}
	var replaceList []replacement
	for from, to := range replacements {
		replaceList = append(replaceList, replacement{from, to})
	}
	sort.Slice(replaceList, func(i, j int) bool {
		return len(replaceList[i].from) > len(replaceList[j].from)
	})

	// Apply replacements
	for _, r := range replaceList {
		result = strings.Replace(result, r.from, r.to, -1)
	}

	return result
}

func IsLeapYear(year int) bool {
	return year%4 == 0 && (year%100 != 0 || year%400 == 0)
}

func NumberOfDayInMonth(year int, month int) int {
	if month == 2 {
		if IsLeapYear(year) {
			return 29
		}
		return 28
	}

	return daysInMonth[month-1]
}
