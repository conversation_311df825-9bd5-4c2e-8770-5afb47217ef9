package register

import (
	utype "github.com/likearthian/types"
	"gopkg.in/guregu/null.v4"
)

type GetEtlProcessRequestDTO struct {
	ID        utype.SliceOfString `query:"id"`
	IsRunning utype.Boolean       `query:"is_running"`
}

type GetJobRequestDTO struct {
	ID        utype.SliceOfInt64  `query:"id"`
	IsRunning utype.Boolean       `query:"is_running"`
	Status    utype.SliceOfString `query:"status"`
	EtlID     utype.SliceOfString `query:"etl_id"`
}

type PostRegisterJobStartRequestDTO struct {
	EtlID   string         `json:"etl_id"`
	Options map[string]any `json:"options"`
	Output  null.String    `json:"output"`
}

type PostRegisterJobStartResponseDTO struct {
	JobID int64 `json:"job_id"`
}

type UpdateJobStatusRequestDTO struct {
	JobID    int64       `query:"job_id"`
	Status   null.String `json:"status"`
	EndTime  null.Int    `json:"end_time"`
	Output   null.String `json:"output"`
	ExitCode null.Int    `json:"exit_code"`
}
