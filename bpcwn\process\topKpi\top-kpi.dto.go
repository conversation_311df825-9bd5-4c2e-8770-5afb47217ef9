package topKpi

import (
	"strconv"

	"cloud.google.com/go/bigquery"
	"gopkg.in/guregu/null.v4"
)

type TopKpi struct {
	RevenueGrossMn float64  `db:"total_rev_gross_mn"`
	DataRevGrossMn float64  `db:"data_rev_gross_mn"`
	TrafficGb      float64  `db:"traffic_gb"`
	Rgu90          int      `db:"rgs90d"`
	Rgu30          int      `db:"rgs30d"`
	Vlr            null.Int `db:"vlr_daily"`
	GrossAdds      int      `db:"rgu_ga"`
	PackSubs       int      `db:"packsub"`
	Quro           int      `db:"quro"`
	Qsso           int      `db:"qsso"`
	OSA            float64  `db:"osa"`
	DseCount       int      `db:"dse_cnt"`
}

type MtdTopKpiData struct {
	MonthID  string      `db:"month_id"`
	Period   string      `db:"period"`
	AsofDate string      `db:"asof_date"`
	Circle   null.String `db:"circle"`
	Region   null.String `db:"region"`
	Brand    string      `db:"brand"`
	TopKpi
}

func (mt MtdTopKpiData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "brand", "total_rev_gross_mn", "traffic_gb", "rgs90d", "rgs30d", "vlr_daily", "rgu_ga", "packsub", "quro", "qsso", "osa", "dse_cnt"}
}

func (mt MtdTopKpiData) GetRowValues() []string {
	revenue := strconv.FormatFloat(mt.RevenueGrossMn, 'f', -1, 64)
	traffic := strconv.FormatFloat(mt.TrafficGb, 'f', -1, 64)
	rgs90 := strconv.Itoa(mt.Rgu90)
	rgs30 := strconv.Itoa(mt.Rgu30)
	vlr := strconv.Itoa(int(mt.Vlr.Int64))
	ga := strconv.Itoa(mt.GrossAdds)
	pack := strconv.Itoa(mt.PackSubs)
	quro := strconv.Itoa(mt.Quro)
	qsso := strconv.Itoa(mt.Qsso)
	osa := strconv.FormatFloat(mt.OSA, 'f', -1, 64)
	dse := strconv.Itoa(mt.DseCount)

	return []string{mt.MonthID, mt.Period, mt.AsofDate, mt.Circle.String, mt.Region.String, mt.Brand, revenue, traffic, rgs90, rgs30, vlr, ga, pack, quro, qsso, osa, dse}
}

type MtdTopKpiKabuData struct {
	MtdTopKpiData
	Kabupaten null.String `db:"kabupaten"`
	KabuFlag  null.String `db:"flag"`
}

func (mt MtdTopKpiKabuData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "brand", "kabupaten", "flag", "total_rev_gross_mn", "traffic_gb", "rgs90d", "rgs30d", "vlr_daily", "rgu_ga", "packsub", "quro", "qsso", "osa", "dse_cnt"}
}

func (mt MtdTopKpiKabuData) GetRowValues() []string {
	revenue := strconv.FormatFloat(mt.RevenueGrossMn, 'f', -1, 64)
	traffic := strconv.FormatFloat(mt.TrafficGb, 'f', -1, 64)
	rgs90 := strconv.Itoa(mt.Rgu90)
	rgs30 := strconv.Itoa(mt.Rgu30)
	vlr := strconv.Itoa(int(mt.Vlr.Int64))
	ga := strconv.Itoa(mt.GrossAdds)
	pack := strconv.Itoa(mt.PackSubs)
	quro := strconv.Itoa(mt.Quro)
	qsso := strconv.Itoa(mt.Qsso)
	osa := strconv.FormatFloat(mt.OSA, 'f', -1, 64)
	dse := strconv.Itoa(mt.DseCount)

	return []string{mt.MonthID, mt.Period, mt.AsofDate, mt.Circle.String, mt.Region.String, mt.Brand, mt.Kabupaten.String, mt.KabuFlag.String, revenue, traffic, rgs90, rgs30, vlr, ga, pack, quro, qsso, osa, dse}
}

type MtdTopKpiDataBQ struct {
	MonthID        string              `bigquery:"month_id"`
	Period         string              `bigquery:"period"`
	AsofDate       string              `bigquery:"asof_date"`
	Circle         bigquery.NullString `bigquery:"circle"`
	Region         bigquery.NullString `bigquery:"region"`
	Brand          string              `bigquery:"brand"`
	Kabupaten      bigquery.NullString `bigquery:"kabupaten"`
	KabuFlag       bigquery.NullString `bigquery:"flag"`
	RevenueGrossMn float64             `bigquery:"total_rev_gross_mn"`
	DataRevGrossMn float64             `bigquery:"data_rev_gross_mn"`
	TrafficGb      float64             `bigquery:"traffic_gb"`
	Rgu90          int                 `bigquery:"rgs90d"`
	Rgu30          int                 `bigquery:"rgs30d"`
	Vlr            bigquery.NullInt64  `bigquery:"vlr_daily"`
	GrossAdds      int                 `bigquery:"rgu_ga"`
	PackSubs       int                 `bigquery:"packsub"`
	Quro           int                 `bigquery:"quro"`
	Qsso           int                 `bigquery:"qsso"`
	OSA            float64             `bigquery:"osa"`
	DseCount       int                 `bigquery:"dse_cnt"`
}

func (tpbq MtdTopKpiDataBQ) ToMtdTopKpiKabuData() MtdTopKpiKabuData {
	return MtdTopKpiKabuData{
		MtdTopKpiData: MtdTopKpiData{
			MonthID:  tpbq.MonthID,
			Period:   tpbq.Period,
			AsofDate: tpbq.AsofDate,
			Circle:   null.NewString(tpbq.Circle.StringVal, tpbq.Circle.Valid),
			Region:   null.NewString(tpbq.Region.StringVal, tpbq.Region.Valid),
			Brand:    tpbq.Brand,
			TopKpi: TopKpi{
				RevenueGrossMn: tpbq.RevenueGrossMn,
				DataRevGrossMn: tpbq.DataRevGrossMn,
				TrafficGb:      tpbq.TrafficGb,
				Rgu90:          tpbq.Rgu90,
				Rgu30:          tpbq.Rgu30,
				Vlr:            null.NewInt(tpbq.Vlr.Int64, tpbq.Vlr.Valid),
				GrossAdds:      tpbq.GrossAdds,
				PackSubs:       tpbq.PackSubs,
				Quro:           tpbq.Quro,
				Qsso:           tpbq.Qsso,
				OSA:            tpbq.OSA,
				DseCount:       tpbq.DseCount,
			},
		},
		Kabupaten: null.NewString(tpbq.Kabupaten.StringVal, tpbq.Kabupaten.Valid),
		KabuFlag:  null.NewString(tpbq.KabuFlag.StringVal, tpbq.KabuFlag.Valid),
	}
}

type MtdTopKpi struct {
	MonthID  string
	Brand    string
	Period   string
	AsofDate string
	TopKpi
}

type MtdTopKpiKabuFlag struct {
	MonthID  string
	Brand    string
	Flag     string
	Period   string
	AsofDate string
	TopKpi
}

type TopKpiReportData struct {
	Brand    string
	AsofDate string
	MTD      *MtdTopKpi
	LMTD     *MtdTopKpi
}

type TopKpiReport struct {
	IM3   TopKpiReportData
	Three TopKpiReportData
	IOH   TopKpiReportData
}

type TopKpiKabuReportData struct {
	Brand    string
	AsofDate string
	MTD      *MtdTopKpiKabuFlag
	LMTD     *MtdTopKpiKabuFlag
}

type TopKpiKabuReport struct {
	IM3   map[string]*TopKpiKabuReportData
	Three map[string]*TopKpiKabuReportData
	IOH   map[string]*TopKpiKabuReportData
}

type RegionalTopKpiData struct {
	RegionType string
	RegionName string
	AsofDate   string
	MTD        *TopKpi
	LMTD       *TopKpi
}

func NewRegionalTopKpiData(regTYpe, regName string) *RegionalTopKpiData {
	return &RegionalTopKpiData{
		RegionType: regTYpe,
		RegionName: regName,
		MTD: &TopKpi{
			Vlr: null.IntFrom(0),
		},
		LMTD: &TopKpi{
			Vlr: null.IntFrom(0),
		},
	}
}

type TopKpiReportMap struct {
	KabuFlagMap map[string]*RegionalTopKpiData
	NationalMap *RegionalTopKpiData
}

func NewTopKpiReportMap() *TopKpiReportMap {
	return &TopKpiReportMap{
		KabuFlagMap: make(map[string]*RegionalTopKpiData),
		NationalMap: NewRegionalTopKpiData("NATIONAL", "INDONESIA"),
	}
}

type NewTopKpiReport struct {
	IM3   *TopKpiReportMap
	Three *TopKpiReportMap
	IOH   *TopKpiReportMap
}
