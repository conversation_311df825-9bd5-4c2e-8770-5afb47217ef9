package process

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"io"
	"time"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	"github.com/csee-pm/etl/shared/notify"
)

type Attachment struct {
	FileName string
	Content  io.Reader
}

func SendReportEmail(c context.Context, attachments ...Attachment) error {
	conf := ctx.ExtractConfig(c)

	mailConfig, err := cfg.GetConfig[cfg.EmailServer](conf, "email_server")
	if err != nil {
		return fmt.Errorf("failed to get email server config. %s", err)
	}

	receivers := conf.GetStringSlice("report_recipients")

	mailer := notify.NewEmailNotifier(mailConfig.Host, mailConfig.Port, mailConfig.User, mailConfig.Pass, mailConfig.SenderAddress, mailConfig.SenderName, receivers...)

	title := "Distribution Tracker Report"

	// open template distrib_report.html file from the embedded files and render it
	t, err := template.ParseFS(procFS, "files/distrib_report.html")
	if err != nil {
		return fmt.Errorf("failed to parse template. %s", err)
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("02-Jan-2006 15:04:05"),
	}); err != nil {
		return fmt.Errorf("failed to execute template. %s", err)
	}

	htmlMsg := buf.String()

	var attachs []notify.FileAttachment
	for _, a := range attachments {
		attachs = append(attachs, notify.FileAttachment{
			FileName: a.FileName,
			Content:  a.Content,
		})
	}

	return mailer.Notify(title, htmlMsg, attachs...)
}
