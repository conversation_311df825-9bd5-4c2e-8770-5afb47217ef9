package config

type HdfsConfig struct {
	HdfsPrincipalName string `yaml:"hdfs_principal_name"`
	Realm             string `yaml:"realm"`
	KeytabFile        string `yaml:"keytab_file"`
	Kdc               string `yaml:"kdc"`
	HdfsNode          string `yaml:"hdfs_node"`
}

func (hc HdfsConfig) ToMap() map[string]any {
	var cfMap = make(map[string]interface{})
	cfMap["hdfs_principal_name"] = hc.HdfsPrincipalName
	cfMap["realm"] = hc.Realm
	cfMap["keytab_file"] = hc.KeytabFile
	cfMap["kdc"] = hc.Kdc
	cfMap["hdfs_node"] = hc.HdfsNode
	return cfMap
}
