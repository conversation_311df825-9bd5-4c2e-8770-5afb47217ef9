package salmobo

import (
	"gopkg.in/guregu/null.v4"
	"strconv"
)

type SalmoboData struct {
	MonthID       string      `db:"month_id" dataframe:"month_id"`
	Wk            int         `db:"wk" dataframe:"wk"`
	Circle        null.String `db:"circle" dataframe:"circle"`
	Region        null.String `db:"region" dataframe:"region"`
	Kabupaten     null.String `db:"kabupaten" dataframe:"kabupaten"`
	Brand         string      `db:"brand" dataframe:"brand"`
	Slab0         int         `db:"slab_0" dataframe:"slab_0"`
	Slab10k       int         `db:"slab_10k" dataframe:"slab_10k"`
	Slab10k_50k   int         `db:"slab_10k_50k" dataframe:"slab_10k_50k"`
	Slab50k_100k  int         `db:"slab_50k_100k" dataframe:"slab_50k_100k"`
	Slab100k_200k int         `db:"slab_100k_200k" dataframe:"slab_100k_200k"`
	Slab200k_500k int         `db:"slab_200k_500k" dataframe:"slab_200k_500k"`
	Slab500k_1m   int         `db:"slab_500k_1m" dataframe:"slab_500k_1m"`
	Slab1m        int         `db:"slab_1m" dataframe:"slab_1m"`
}

func (s SalmoboData) GetColumns() []string {
	return []string{"month_id", "wk", "circle", "region", "kabupaten", "brand", "slab_0", "slab_10k", "slab_10k_50k", "slab_50k_100k", "slab_100k_200k", "slab_200k_500k", "slab_500k_1m", "slab_1m"}
}

func (s SalmoboData) GetRowValues() []string {
	return []string{s.MonthID, strconv.Itoa(s.Wk), s.Circle.String, s.Region.String, s.Kabupaten.String, s.Brand, strconv.Itoa(s.Slab0), strconv.Itoa(s.Slab10k), strconv.Itoa(s.Slab10k_50k), strconv.Itoa(s.Slab50k_100k), strconv.Itoa(s.Slab100k_200k), strconv.Itoa(s.Slab200k_500k), strconv.Itoa(s.Slab500k_1m), strconv.Itoa(s.Slab1m)}
}

func (s SalmoboData) GetValues() []any {
	return []any{s.MonthID, s.Wk, s.Circle.String, s.Region.String, s.Kabupaten.String, s.Brand, s.Slab0, s.Slab10k, s.Slab10k_50k, s.Slab50k_100k, s.Slab100k_200k, s.Slab200k_500k, s.Slab500k_1m, s.Slab1m}
}
