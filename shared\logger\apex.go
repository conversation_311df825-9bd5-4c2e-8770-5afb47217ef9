package logger

import (
	"fmt"

	"github.com/apex/log"
)

type apexLoger struct {
	logger *log.Logger
}

func NewApexLogger(logger *log.Logger) Logger {
	return &apexLoger{logger: logger}
}

func (a *apexLoger) Info(msg string, keyvals ...interface{}) {
	var logger *log.Entry
	num := len(keyvals)
	for i := 0; i < num; i += 2 {
		key := fmt.Sprintf("%v", keyvals[i])
		var val interface{} = nil
		if num > i+1 {
			val = keyvals[i+1]
		}

		if logger == nil {
			logger = a.logger.WithField(key, val)
		} else {
			logger = logger.WithField(key, val)
		}
	}

	if logger == nil {
		a.logger.Info(msg)
	} else {
		logger.Info(msg)
	}
}

func (a *apexLoger) Debug(msg string, keyvals ...interface{}) {
	var logger *log.Entry
	num := len(keyvals)
	for i := 0; i < num; i += 2 {
		key := fmt.Sprintf("%v", keyvals[i])
		var val interface{} = nil
		if num > i+1 {
			val = keyvals[i+1]
		}

		if logger == nil {
			logger = a.logger.WithField(key, val)
		} else {
			logger = logger.WithField(key, val)
		}
	}

	if logger == nil {
		a.logger.Debug(msg)
	} else {
		logger.Debug(msg)
	}
}

func (a *apexLoger) Warn(msg string, keyvals ...interface{}) {
	var logger *log.Entry
	num := len(keyvals)
	for i := 0; i < num; i += 2 {
		key := fmt.Sprintf("%v", keyvals[i])
		var val interface{} = nil
		if num > i+1 {
			val = keyvals[i+1]
		}

		if logger == nil {
			logger = a.logger.WithField(key, val)
		} else {
			logger = logger.WithField(key, val)
		}
	}

	if logger == nil {
		a.logger.Warn(msg)
	} else {
		logger.Warn(msg)
	}
}

func (a *apexLoger) Error(msg string, keyvals ...interface{}) {
	var logger *log.Entry
	num := len(keyvals)
	for i := 0; i < num; i += 2 {
		key := fmt.Sprintf("%v", keyvals[i])
		var val interface{} = nil
		if num > i+1 {
			val = keyvals[i+1]
		}

		if logger == nil {
			logger = a.logger.WithField(key, val)
		} else {
			logger = logger.WithField(key, val)
		}
	}

	if logger == nil {
		a.logger.Error(msg)
	} else {
		logger.Error(msg)
	}
}

func (a *apexLoger) SetLevel(level Level) {
	switch level {
	case DebugLevel:
		a.logger.Level = log.DebugLevel
	case InfoLevel:
		a.logger.Level = log.InfoLevel
	case WarnLevel:
		a.logger.Level = log.WarnLevel
	case ErrorLevel:
		a.logger.Level = log.ErrorLevel
	default:
		a.logger.Level = log.InfoLevel
	}
}
