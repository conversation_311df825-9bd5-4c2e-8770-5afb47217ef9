package process

import (
	"compress/gzip"
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
)

func RunETLProcess(c context.Context) error {
	conf := ctx.ExtractConfig(c)
	workDate := time.Now().AddDate(0, 0, -2)
	startDt, _ := strconv.Atoi(workDate.AddDate(0, 0, -30).Format("20060102"))
	endDt, _ := strconv.Atoi(workDate.AddDate(0, 0, 1).Format("20060102"))

	if conf.Get("etl_config.start_dt") != nil {
		startDt = conf.GetInt("etl_config.start_dt")
	}

	if conf.Get("etl_config.end_dt") != nil {
		endDt = conf.GetInt("etl_config.end_dt")
	}

	logger := ctx.ExtractLogger(c)

	workDir, ok := conf.Get("work_dir").(string)
	if !ok {
		workDir = ""
	}

	tunnel := conf.Get("tunnel")
	var useTunnel = false
	if tunnel != nil {
		useTunnel = true
	}

	if useTunnel {
		tunConfig, err := cfg.GetTunnelConfig(conf, "tunnel")
		if err != nil {
			return fmt.Errorf("failed to get tunnel config. %s", err)
		}

		if err := etlProc.StartTunnel(tunConfig, logger); err != nil {
			return fmt.Errorf("failed to start tunnel. %s", err)
		}
	}

	csvFilePath := filepath.Join(workDir, "3id_site_perf_"+time.Now().Format("20060102150405")+".csv.gz")
	csvFile, err := os.Create(csvFilePath)
	if err != nil {
		return fmt.Errorf("failed to create csv file. %s", err)
	}

	gw := gzip.NewWriter(csvFile)
	out := csv.NewWriter(gw)

	logger.Info("Starting ETL process", "startDt", startDt, "endDt", endDt)
	logger.Info("Getting 3ID data from greenplum", "start_date", startDt, "end_date", endDt)

	if err := get3idData(c, startDt, endDt, out); err != nil {
		return fmt.Errorf("failed to get 3ID data. %s", err)
	}

	out.Flush()
	gw.Flush()
	gw.Close()
	csvFile.Close()

	hdfsPath := "/data/mktreg/ziska/3id_site_perf_dly_stg"
	logger.Debug("Uploading to HDFS", "csvFilePath", csvFilePath, "hdfsPath", hdfsPath)
	if err := uploadToHadoop(c, csvFilePath, hdfsPath); err != nil {
		return fmt.Errorf("failed to upload to HDFS. %s", err)
	}

	return nil
}

func uploadToHadoop(c context.Context, fpath string, hdfsPath string) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	iplConfig, err := cfg.GetImpalaConfig(conf, "impala")
	if err != nil {
		return fmt.Errorf("failed to get impala config. %s", err)
	}

	sshConfig, err := cfg.GetSSHConfig(conf, "impala_landing_ssh")
	if err != nil {
		return fmt.Errorf("failed to get ssh config. %s", err)
	}

	cl, err := utils.NewSSHClient(sshConfig.Host, sshConfig.Port, sshConfig.User, sshConfig.PrivateKeyFile)
	if err != nil {
		return fmt.Errorf("failed to create ssh client. %s", err)
	}
	defer cl.Close()

	file, err := os.Open(fpath)
	if err != nil {
		return fmt.Errorf("failed to open file %q. %s", fpath, err)
	}
	defer file.Close()

	fname := filepath.Base(file.Name())
	ext := filepath.Ext(file.Name())
	remotePath := filepath.Join("/home", sshConfig.User+"@office.corp.indosat.com", fname)

	logger.Info("Sending file to HDFS", "file", fpath, "hdfsPath", hdfsPath)

	if err := cl.SendFile(file, remotePath); err != nil {
		return fmt.Errorf("failed to send file %q to remote path %q. %s", file.Name(), remotePath, err)
	}

	if ext == ".gz" {
		_, err = cl.RunCommand(fmt.Sprintf("gzip -d %s", remotePath))
		if err != nil {
			return fmt.Errorf("failed to run gzip. %s", err)
		}
		remotePath = remotePath[:len(remotePath)-3]
	}

	defer func() {
		_, err := cl.RunCommand(fmt.Sprintf("rm -f %s", remotePath))
		if err != nil {
			logger.Error("failed to delete file", "file", remotePath, "err", err)
		}
	}()

	_, err = cl.RunCommand(fmt.Sprintf("kinit %s -k -t %s", sshConfig.User+"@OFFICE.CORP.INDOSAT.COM", sshConfig.User+".keytab"))
	if err != nil {
		return fmt.Errorf("failed to authenticate to kerberos. %s", err)
	}

	if _, err = cl.RunCommand(fmt.Sprintf("hdfs dfs -put -f %s %s", remotePath, hdfsPath+"/3id_site_perf.csv")); err != nil {
		return fmt.Errorf("failed to put file into hdfs. %s", err)
	}

	impalaConf := etlDb.ImpalaConfig{
		User:     iplConfig.User,
		Password: iplConfig.Password,
		Host:     iplConfig.Host,
		Port:     strconv.Itoa(iplConfig.Port),
		Cacerts:  iplConfig.CaCert,
	}

	iplClient, err := etlDb.CreateImpalaSqlClient(impalaConf, etlDb.UseInsecureTLS())
	if err != nil {
		return fmt.Errorf("failed to connect to Impala DB. %s", err)
	}

	logger.Debug("Invalidating metadata", "table", "vbt.zz_3id_site_perf_dly_stg")
	if _, err = iplClient.Exec(fmt.Sprintf("INVALIDATE METADATA %s", "vbt.zz_3id_site_perf_dly_stg")); err != nil {
		return fmt.Errorf("failed to invalidate metadata. %s", err)
	}

	logger.Debug("Inserting data into vbt.zz_3id_site_perf_dly")
	if _, err = iplClient.Query(h3iSitePerfUpdateSql); err != nil {
		return fmt.Errorf("failed to update vbt.zz_3id_site_perf_dly. %s", err)
	}

	return nil
}

func get3idData(c context.Context, startdt int, enddt int, out *csv.Writer) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	cf, err := cfg.GetGPConfig(conf, "gpfat")
	if err != nil {
		return err
	}

	params := map[string]*etlDb.ParamValue{
		"start_dt":   {Name: "start_dt", Value: startdt},
		"end_dt_exc": {Name: "end_dt_exc", Value: enddt},
	}

	logger.Debug("connecting to greenplum")
	db, err := etlDb.ConnectToGP(cf.Host, cf.Port, cf.User, cf.Password, cf.Database)
	if err != nil {
		return err
	}

	err = db.Ping()
	if err != nil {
		return err
	}

	query, args, err := etlDb.ParseSqlWithParams(h3iSitePerfSql, params)
	if err != nil {
		return err
	}

	query = db.Rebind(query)

	logger.Debug("Executing query", "query", query, "args", args)
	rows, err := db.Query(query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	header, err := rows.Columns()
	if err != nil {
		return err
	}

	out.Write(header)

	values := make([]any, len(header))
	scanArgs := make([]any, len(values))

	for i := range values {
		scanArgs[i] = &values[i]
	}

	defer out.Flush()

	logger.Debug("Start Processing incoming rows")
	n := 0
	for rows.Next() {
		err = rows.Scan(scanArgs...)
		if err != nil {
			return err
		}

		n++
		record := make([]string, len(values))
		for i, value := range values {
			var val any = value
			switch tval := value.(type) {
			case []byte:
				val = string(tval)
			case nil:
				val = ""
			}

			record[i] = fmt.Sprint(val)
		}

		if err := out.Write(record); err != nil {
			return err
		}

		if n%1024 == 0 {
			out.Flush()
			fmt.Printf("Processed %d records\r", n)
		}
	}

	fmt.Printf("Processed %d records\n", n)

	return nil
}
