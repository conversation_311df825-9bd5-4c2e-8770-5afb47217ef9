package process

import (
	"fmt"
	"iter"

	"github.com/jmoiron/sqlx"
)

type RowIterator[T any] struct {
	err  error
	rows *sqlx.Rows
}

func newRowIterator[T any](rows *sqlx.Rows) *RowIterator[T] {
	return &RowIterator[T]{rows: rows}
}

func (ri *RowIterator[T]) setErr(err error) {
	ri.err = err
}

func (ri *RowIterator[T]) Err() error {
	return ri.err
}

func (ri *RowIterator[T]) Enumerate() iter.Seq2[int, T] {
	return func(yield func(int, T) bool) {
		defer ri.rows.Close()
		i := 0
		for {
			var data T
			if !ri.rows.Next() {
				break
			}

			if err := ri.rows.StructScan(&data); err != nil {
				ri.setErr(fmt.Errorf("failed to scan row: %d. %s", i, err))
				return
			}

			if !yield(i, data) {
				break
			}
			i++
		}

		if ri.rows.Err() != nil {
			ri.setErr(fmt.Errorf("failed to iterate over query result. %s", ri.rows.Err()))
		}
	}
}
