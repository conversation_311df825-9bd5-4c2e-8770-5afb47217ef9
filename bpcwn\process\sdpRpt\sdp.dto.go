package sdpRpt

import (
	"strconv"
	"time"

	"gopkg.in/guregu/null.v4"
)

type DseMtdData struct {
	MonthID        string      `db:"month_id" csv:"month_id"`
	Period         string      `db:"period" csv:"period"`
	AsofDate       string      `db:"asof_date" csv:"asof_date"`
	Brand          string      `db:"brand" csv:"brand"`
	Circle         null.String `db:"circle" csv:"circle"`
	Region         null.String `db:"region" csv:"region"`
	Dse            string      `db:"dse" csv:"dse"`
	GrossAdds      int         `db:"ga" csv:"ga"`
	NetSecondaryMn float64     `db:"net_secondary_mn" csv:"net_secondary_mn"`
	DayNum         int         `db:"day_no" csv:"day_no"`
}

func (d DseMtdData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "brand", "circle", "region", "dse", "ga", "net_secondary_mn", "day_no"}
}

func (d DseMtdData) GetRowValues() []string {
	return []string{d.MonthID, d.Period, d.AsofDate, d.Brand, d.Circle.String, d.Region.String, d.Dse, strconv.Itoa(d.GrossAdds), strconv.FormatFloat(d.NetSecondaryMn, 'f', -1, 64), strconv.Itoa(d.DayNum)}
}

type SdpMtdData struct {
	MonthID        string      `db:"month_id" csv:"month_id"`
	Period         string      `db:"period" csv:"period"`
	AsofDate       string      `db:"asof_date" csv:"asof_date"`
	Brand          string      `db:"brand" csv:"brand"`
	Circle         null.String `db:"circle" csv:"circle"`
	Region         null.String `db:"region" csv:"region"`
	SdpFlag        string      `db:"flag" csv:"flag"`
	Sdp            string      `db:"dist_id" csv:"dist_id"`
	GrossAdds      int         `db:"ga" csv:"ga"`
	NetSecondaryMn float64     `db:"net_secondary_mn" csv:"net_secondary_mn"`
	DayNum         int         `db:"day_no" csv:"day_no"`
}

func (d SdpMtdData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "brand", "circle", "region", "flag", "dist_id", "ga", "net_secondary_mn", "day_no"}
}

func (d SdpMtdData) GetRowValues() []string {
	return []string{d.MonthID, d.Period, d.AsofDate, d.Brand, d.Circle.String, d.Region.String, d.SdpFlag, d.Sdp, strconv.Itoa(d.GrossAdds), strconv.FormatFloat(d.NetSecondaryMn, 'f', -1, 64), strconv.Itoa(d.DayNum)}
}

type PartnerKpiData struct {
	MonthID        string
	GrossAdds      int
	NetSecondaryMn float64
	DayNum         int
}

type RegionalMtdKpiData struct {
	MonthID string
	Period  string
	//RegionType          string
	//RegionName          string
	//Brand               string
	GrossAdds           int
	NetSecondaryMn      float64
	GaSlabs             map[string]int
	GaDailySlabs        map[string]int
	SecondarySlabs      map[string]int
	SecondaryDailySlabs map[string]int
	PartnerCount        int
}

type RegionalFmKpiData struct {
	MonthID string
	//RegionType     string
	//RegionName     string
	//Brand          string
	GrossAdds      int
	NetSecondaryMn float64
}

type RegionalReportData struct {
	RegionType string
	RegionName string
	Brand      string
	MTD        *RegionalMtdKpiData
	LMTD       *RegionalMtdKpiData
	FM         map[string]*RegionalFmKpiData
}

type PartnerReportData struct {
	FmList       []string
	CircleData   map[string]*RegionalReportData
	RegionalData map[string]*RegionalReportData
	NationalData *RegionalReportData
}

type SdpReport struct {
	AsofDate time.Time
	IM3      *PartnerReportData
	Three    *PartnerReportData
	IOH      *PartnerReportData
}

type DseReport struct {
	AsofDate time.Time
	IM3      *PartnerReportData
	Three    *PartnerReportData
	IOH      *PartnerReportData
}

type SdpDseReport struct {
	Sdp               *SdpReport
	Dse               *DseReport
	RawDataZipContent []byte
}
