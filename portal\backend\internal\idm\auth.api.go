package idm

import (
	"context"
	"errors"
	"fmt"
	"github.com/csee-pm/etl/shared/utils/apiutil"
	"time"

	"github.com/csee-pm/etl/portal/backend/pkg/dto"
	"github.com/csee-pm/etl/portal/backend/pkg/errs"
	"github.com/dgrijalva/jwt-go/v4"
	"github.com/likearthian/apikit"
	"github.com/spf13/viper"
	"gopkg.in/guregu/null.v4"
)

type AuthAPI struct {
	idm         *IdmAPI
	ldapService *LdapService
}

func CreateAuthAPI(conf *viper.Viper, idmApi *IdmAPI, ldapService *LdapService) (*AuthAPI, error) {
	return &AuthAPI{idm: idmApi, ldapService: ldapService}, nil
}

func (api *AuthAPI) AuthenticateEndpoint(c context.Context, req dto.AuthenticateRequestDTO) (*dto.AuthResponseDTO, error) {
	user, err := api.idm.GetUserEndpoint(c, dto.GetUserRequestDTO{Username: null.StringFrom(req.Username)})
	if err != nil && !errors.Is(err, errs.ErrNoRows) {
		return nil, err
	}

	if user == nil {
		return nil, errs.ErrInvalidUserPassword
	}

	if user.IsLdap {
		_, err := api.ldapService.Authenticate(c, req.Username, req.Password)
		if err != nil {
			return nil, fmt.Errorf("%w: %s", errs.ErrInvalidUserPassword, err)
		}

	} else {
		if err := apiutil.PasswordCompare(user.HashedPassword.ValueOrZero(), req.Password); err != nil {
			return nil, errs.ErrInvalidUserPassword
		}
	}

	expiryDuration := 24 * time.Hour
	expiry := time.Now().Add(expiryDuration).UnixMilli()

	token, err := apikit.CreateToken(
		apiutil.MakeClaimFactory(
			"bpcs-portal",
			req.Username,
			user.Fullname,
			user.Email,
			user.IsAdmin,
			jwt.ClaimStrings{"bpcs-portal"},
			expiryDuration),
		apikit.DefaultKeys,
	)

	if err != nil {
		return nil, err
	}

	return &dto.AuthResponseDTO{
		Username: user.Username,
		FullName: user.Fullname,
		Email:    user.Email,
		IsAdmin:  user.IsAdmin,
		Token:    token,
		Expiry:   expiry,
	}, nil
}

func (a *AuthAPI) ValidateTokenEndpoint(ctx context.Context, req any) (*dto.AuthResponseDTO, error) {
	claims, err := apiutil.CheckAuthFromContext(ctx, false)
	if err != nil {
		return nil, err
	}

	token := ctx.Value(apikit.ContextKeyJWTToken).(string)

	auth := &dto.AuthResponseDTO{
		Username: claims.Username,
		FullName: claims.FullName,
		Email:    claims.Email,
		Expiry:   claims.ExpiresAt.UnixMilli(),
		IsAdmin:  claims.IsAdmin,
		Token:    token,
	}

	return auth, nil
}

func (a *AuthAPI) RefreshTokenEndpoint(ctx context.Context, req any) (*dto.AuthResponseDTO, error) {
	claims, err := apiutil.CheckAuthFromContext(ctx, false)
	if err != nil {
		return nil, err
	}

	expiryDuration := 24 * time.Hour
	expiry := time.Now().Add(expiryDuration).UnixMilli()

	newToken, err := apikit.CreateToken(
		apiutil.MakeClaimFactory(
			"bpcs-portal",
			claims.Username,
			claims.FullName,
			claims.Email,
			claims.IsAdmin,
			jwt.ClaimStrings{"bpcs-portal"},
			expiryDuration),
		apikit.DefaultKeys,
	)

	if err != nil {
		return nil, err
	}

	auth := &dto.AuthResponseDTO{
		Username: claims.Username,
		FullName: claims.FullName,
		Email:    claims.Email,
		Expiry:   expiry,
		IsAdmin:  claims.IsAdmin,
		Token:    newToken,
	}

	return auth, nil
}
