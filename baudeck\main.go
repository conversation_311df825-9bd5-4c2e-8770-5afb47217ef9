package main

import (
	"bytes"
	"context"
	"fmt"
	"os"

	cfg "github.com/csee-pm/etl/baudeck/config"
	"github.com/csee-pm/etl/baudeck/process"
	etlcmd "github.com/csee-pm/etl/shared/cmd"
	etlcfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/spf13/cobra"
)

var logBuffer *bytes.Buffer
var executedCmd *cobra.Command

func main() {
	logBuffer = new(bytes.Buffer)

	root := CreateRootCmd()

	err := root.Execute()
	process := ""
	if executedCmd != nil {
		process = executedCmd.Name()
	}

	err = etlProc.NotifyProcessFinished(root.Context(), process, logBuffer.String(), err)
	if err != nil {
		fmt.Println(err.Error())
		os.Exit(1)
	}
}

func CreateRootCmd() *cobra.Command {
	root := etlcmd.InitCommonRootCommand(
		"baudeck",
		cfg.SetNewConfig,
		etlcmd.WithRootProcess("baudeck", process.RunETL),
		etlcmd.WithDescription("ETL job for getting baudeck report"),
		etlcmd.WithLogBuffer(logBuffer),
	)

	root.Flags().StringVar(&cfg.UsePstMtdFromFile, "pst-mtd-from-file", "", "Use PST MTD data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UsePstFmFromFile, "pst-fm-from-file", "", "Use PST FM data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UsePstKabuFromFile, "pst-kabu-from-file", "", "Use PST Kabu data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseSDPFromFile, "sdp-from-file", "", "Use SDP data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseDSEFromFile, "dse-from-file", "", "Use DSE data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseGaM2sFromFile, "ga-m2s-from-file", "", "Use GA M2S data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UsePstQssoFromFile, "pst-qsso-from-file", "", "Use PST QSSO data from file instead of querying from database")

	root.PersistentFlags().BoolVar(&cfg.UseNoMailer, "no-mailer", false, "Do not send email")
	root.PersistentFlags().BoolVar(&cfg.NotInteractive, "not-interactive", false, "Do not prompt for configuration")

	root.PreRunE = createPrerun(func() bool {
		return true
	})

	return root
}

func createPrerun(useTunnelCheckFn func() bool) func(*cobra.Command, []string) error {
	return func(cmd *cobra.Command, args []string) error {
		executedCmd = cmd
		conf := ctx.ExtractConfig(cmd.Context())
		logger := ctx.ExtractLogger(cmd.Context())
		tunnel := conf.Get("tunnel")
		var useTunnel = false
		if tunnel != nil {
			useTunnel = true
			if useTunnelCheckFn != nil {
				useTunnel = useTunnelCheckFn()
			}
		}

		if useTunnel {
			tunConfig, err := etlcfg.GetTunnelConfig(conf, "tunnel")
			if err != nil {
				return fmt.Errorf("failed to get tunnel config. %s", err)
			}

			if err := etlProc.StartTunnel(tunConfig, logger); err != nil {
				return fmt.Errorf("failed to start tunnel. %s", err)
			}
		}

		workDir := conf.GetString("work_dir")
		if workDir == "" {
			workDir = "workdir"
		}

		exepath := ctx.ExtractRootDir(cmd.Context())
		workDir = exepath + "/" + workDir
		conf.Set("work_dir", workDir)
		c := context.WithValue(cmd.Context(), ctx.ContextKeyWorkDir, workDir)
		cmd.SetContext(c)

		if err := os.MkdirAll(workDir, os.ModePerm); err != nil {
			return fmt.Errorf("failed to create work dir. %s", err)
		}

		return nil
	}
}
