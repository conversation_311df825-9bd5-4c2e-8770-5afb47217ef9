/*
Copyright © 2024 <PERSON>isk<PERSON> <<EMAIL>>
*/
package cmd

import (
	"fmt"
	"strings"

	etlCfg "github.com/csee-pm/etl/shared/config"
	"github.com/csee-pm/etl/shared/enc"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// configSetCmd represents the cmd command
var _ = &cobra.Command{
	Use:   "config-set",
	Short: "set config value",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("config-set called", args)
	},
}

func NewConfigSetCmd(v *viper.Viper, newConfigFn func(*viper.Viper) error) *cobra.Command {
	return &cobra.Command{
		Use:   "config-set <key>=<value>",
		Short: "set config value",
		Run:   makeConfigSetAction(v, newConfigFn),
	}
}

func makeConfigSetAction(v *viper.Viper, newConfigFn func(*viper.Viper) error) func(cmd *cobra.Command, args []string) {
	return func(cmd *cobra.Command, args []string) {
		if len(args) == 0 {
			if err := newConfigFn(v); err != nil {
				fmt.Println("failed to create new config", err)
			}

			if err := v.WriteConfig(); err != nil {
				fmt.Println("failed to write config", err)
			}

			return
		}

		key, strVal, err := parseConfigSetArgs(args)
		if err != nil {
			fmt.Println("failed to parse args", err)
			return
		}

		exVal := v.Get(key)

		val, err := utils.ConvertStringValue(strVal, exVal)
		if err != nil {
			fmt.Println("failed to convert value", err)
			return
		}

		if exVal == nil {
			fmt.Printf("key %q not found\n", key)
			return
		}

		v.Set(key, val)
		if err := v.WriteConfig(); err != nil {
			fmt.Println("failed to write config", err)
		}
	}
}

func parseConfigSetArgs(args []string) (string, string, error) {
	key := args[0]
	strVal := ""
	if len(args) > 1 {
		strVal = strings.Join(args[1:], " ")
	}

	if strings.Contains(key, "=") {
		keyArr := strings.Split(key, "=")
		key = strings.TrimSpace(keyArr[0])
		if len(strVal) > 0 {
			strVal = " " + strVal
		}
		strVal = strings.TrimSpace(keyArr[1]) + strVal
	}

	isCredential := isCredentialKey(key)
	if isCredential {
		return parseCredentialConfig(key)
	}

	if strVal == "" {
		strVal = etlCfg.GetPromptValue("enter value: ", "", etlCfg.NotEmptyValidator)
	}

	return key, strVal, nil
}

func parseCredentialConfig(credentialKey string) (key string, value string, err error) {
	user := etlCfg.GetPromptValue("User: ", "", etlCfg.NotEmptyValidator)
	pass := etlCfg.GetPromptValue("Password: ", "", etlCfg.NotEmptyValidator)

	value, err = enc.Encrypt(fmt.Sprintf("%s:%s", user, pass))
	if err != nil {
		return "", "", err
	}

	return credentialKey, value, nil
}

func isCredentialKey(key string) bool {
	return strings.HasSuffix(key, ".credential")
}
