WITH kpi_parameter as (
    select
        b.circle,
        b.region,
        a.as_of_dt asof_date,
        a.mth month_id,
        a.parameter,
        a.mthf as period,
        a.brand,
        sum(a.amount) amount
    from
        `data-nationalslsdist-prd-986g`.datamart.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
        left join
        `data-bi-prd-935c`.bi_dm.ref_kecamatan b
        on
            a.kec_unik = b.kec_kabkot
    where
        (
            (
                a.dt_id >= format_date('%Y%m%d', date_trunc(date_add(date(${mtd_dt}), interval -3 month), month))
            and a.dt_id < format_date('%Y%m%d', date_trunc(date(${mtd_dt}), month))
            and a.dt_id = format_date('%Y%m%d', date_add(date_trunc(date_add(parse_date('%Y%m%d', a.dt_id), interval 1 month), month), interval -1 day))
            )
            or a.dt_id = format_date('%Y%m%d', date(${mtd_dt}))
        )
    and a.parameter in
        (
            'Primary', --im3
            'Secondary', --im3
            'secondary', --3id
            'Tertiary B#', --im3
            'tertiary', --3id
            'Site 3-QSSO M0', --im3
            'site_3qsso_new' --3id
        )
    and a.mthf in ('mtd', 'lmtd')
    group by
        1,2,3,4,5,6,7

    union all

    select
        b.circle,
        a.region,
        a.as_of_dt,
        a.mth month_id,
        a.parameter,
        mthf as period,
        a.brand,
        sum(a.amt) amount
    from
        rdm.bai_tbl_kpi_dashboard_3id_primseco_mtdlmtd_v1 a
        left join
        (
            select
                distinct
                circle, area, branch, cluster, pt_id, partner, pt_type, mth
            from rdm.bai_tbl_ref_hirarki_territory_ioh_v1
            where
                mth = strleft(${mtd_dt_id},6)
            and brand = '3ID'
        ) b
        on
            a.pt_id = b.pt_id
    where
        (
            (
                a.dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
            and a.dt_id < from_timestamp(trunc(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), 'month'), 'yyyyMMdd')
            and a.dt_id = from_timestamp(date_add(trunc(date_add(to_timestamp(a.dt_id, 'yyyyMMdd'), interval 1 month), 'month'), interval -1 day), 'yyyyMMdd')
            )
            or a.dt_id = ${mtd_dt_id}
        )
    and a.brand = '3ID'
    and a.mthf in ('mtd', 'lmtd')
    and parameter = 'primary'
    group by
        1,2,3,4,5,6,7
)
SELECT
    month_id,
    period,
    asof_date,
    circle,
    region,
    brand,
    sum(case when parameter in ('Primary', 'primary') then amount else null end) `primary`,
    sum(case when parameter in ('Secondary', 'secondary') then amount else null end) `secondary`,
    sum(case when parameter in ('Tertiary B#', 'tertiary') then amount else null end) `tertiary`,
    sum(case when parameter in ('Site 3-QSSO M0', 'site_3qsso_new') then amount else null end) `site_3qsso`
FROM
    kpi_parameter
WHERE
    month_id = substr(${mtd_dt_id}, 1, 6)
GROUP BY
    1,2,3,4,5,6

UNION ALL

SELECT
    month_id,
    'FM' period,
    asof_date,
    circle,
    region,
    brand,
    sum(case when parameter in ('Primary', 'primary') then amount else null end) `primary`,
    sum(case when parameter in ('Secondary', 'secondary') then amount else null end) `secondary`,
    sum(case when parameter in ('Tertiary B#', 'tertiary') then amount else null end) `tertiary`,
    sum(case when parameter in ('Site 3-QSSO M0', 'site_3qsso_new') then amount else null end) `site_3qsso`
FROM
    kpi_parameter
WHERE
    month_id < substr(${mtd_dt_id},1,6)
and period = 'mtd'
GROUP BY
    1,2,3,4,5,6