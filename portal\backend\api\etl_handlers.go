package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"github.com/csee-pm/etl/portal/backend/db"
)

// ETLStartRequest represents a request to start an ETL process
type ETLStartRequest struct {
	Name    string            `json:"name"`
	Binary  string            `json:"binary"`
	Command string            `json:"command"`
	Args    map[string]string `json:"args"`
}

// handleStartETL handles POST /api/etl/start
func (r *Router) handleStartETL(w http.ResponseWriter, req *http.Request) {
	var startReq ETLStartRequest
	if err := json.NewDecoder(req.Body).Decode(&startReq); err != nil {
		respondError(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	// Check if the binary exists
	binaryPath, ok := r.config.ETLBinaries[startReq.Binary]
	if !ok {
		respondError(w, http.StatusBadRequest, "Unknown ETL binary: "+startReq.Binary)
		return
	}

	// Prepare arguments
	var args []string
	for k, v := range startReq.Args {
		args = append(args, fmt.Sprintf("--%s=%s", k, v))
	}

	// Create a process record
	process := &db.Process{
		Name:      startReq.Name,
		Status:    "running",
		StartTime: time.Now(),
		Command:   binaryPath,
		Args:      fmt.Sprintf("%s %s", startReq.Command, formatArgs(args)),
	}

	if err := r.db.CreateProcess(process); err != nil {
		respondError(w, http.StatusInternalServerError, "Failed to create process record: "+err.Error())
		return
	}

	// Start the ETL process in a goroutine
	go func() {
		// Prepare the command
		cmd := exec.Command(binaryPath, append([]string{startReq.Command}, args...)...)

		// Set up working process
		workDir := filepath.Join(r.config.WorkDir, fmt.Sprintf("%s_%d", startReq.Name, process.ID))
		if err := os.MkdirAll(workDir, 0755); err != nil {
			updateProcessError(r.db, process, err)
			return
		}
		cmd.Dir = workDir

		// Capture output
		output, err := cmd.CombinedOutput()

		// Update process record
		process.Output = string(output)
		if err != nil {
			process.Status = "failed"
			process.ExitCode = 1
			if exitErr, ok := err.(*exec.ExitError); ok {
				process.ExitCode = exitErr.ExitCode()
			}
		} else {
			process.Status = "completed"
			process.ExitCode = 0
		}
		process.EndTime = time.Now()

		if err := r.db.UpdateProcess(process); err != nil {
			// Log the error, but we can't do much about it here
			fmt.Printf("Failed to update process record: %v\n", err)
		}

		// Scan for output files
		if err := r.db.ScanForFiles(workDir, process.ID); err != nil {
			fmt.Printf("Failed to scan for output files: %v\n", err)
		}
	}()

	respondJSON(w, http.StatusAccepted, process)
}

// handleStopETL handles POST /api/etl/stop/{id}
func (r *Router) handleStopETL(w http.ResponseWriter, req *http.Request) {
	id, err := getIDParam(req, "id")
	if err != nil {
		respondError(w, http.StatusBadRequest, "Invalid process ID: "+err.Error())
		return
	}

	process, err := r.db.GetProcess(id)
	if err != nil {
		respondError(w, http.StatusNotFound, "Process not found: "+err.Error())
		return
	}

	// Can only stop running processes
	if process.Status != "running" {
		respondError(w, http.StatusBadRequest, "Process is not running")
		return
	}

	// In a real implementation, you would need to find the process by PID and kill it
	// For now, we'll just update the status
	process.Status = "stopped"
	process.EndTime = time.Now()

	if err := r.db.UpdateProcess(process); err != nil {
		respondError(w, http.StatusInternalServerError, "Failed to update process: "+err.Error())
		return
	}

	respondJSON(w, http.StatusOK, process)
}

// Helper functions

// formatArgs formats command line arguments for display
func formatArgs(args []string) string {
	result := ""
	for _, arg := range args {
		result += " " + arg
	}
	return result
}

// updateProcessError updates a process record with an error
func updateProcessError(db *db.DB, process *db.Process, err error) {
	process.Status = "failed"
	process.Output = err.Error()
	process.ExitCode = 1
	process.EndTime = time.Now()

	if err := db.UpdateProcess(process); err != nil {
		fmt.Printf("Failed to update process record: %v\n", err)
	}
}
