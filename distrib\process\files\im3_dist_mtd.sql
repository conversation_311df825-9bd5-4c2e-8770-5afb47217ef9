select a.brand
		,circle
			,region
			,sum(case when mthf='lmtd' then a.amount else 0 end) lmtd
			,sum(case when mthf='mtd' then a.amount else 0 end) mtd
			,concat(a.mth,dt) dt_id
			,case
				when a.parameter='Site 3-QSSO M0'then 'Site 3-QSSO'
				when a.parameter='RGUGA-Trad' then 'RGU GA'
				when a.parameter='site_3qsso_new'then 'Site 3-QSSO'
				when a.parameter='site_5quro'then 'Site 5-QURO'
				when a.parameter='secondary'then 'Secondary'
				when a.parameter='RGUGA-Trad'then 'RGU GA'
				else a.parameter
			end parameter
from 		(
			select 	a.kec_unik
					,lpad(cast(a.as_of_dt as string),2,'0') dt
					,a.mth
					,a.parameter
					,a.mthf
					,brand
					,sum(a.amount) amount
			from 	rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
			where 	as_of_dt = cast(strright(${dt_id},2) as int)
			and 	mth = strleft(${dt_id},6)
			and (	(brand='IM3' and parameter in ('Site 3-QSSO M0','Site 5-QURO','Secondary','RGUGA-Trad'))
					or 
					(brand='3ID' and parameter in ('site_3qsso_new','site_5quro','RGUGA-Trad','secondary')
					)
				)
			and 	mthf in ('mtd','lmtd')
			group by 1,2,3,4,5,6
			) a
left join   rdm.bai_tbl_ref_hirarki_territory_ioh_v1 kec on a.kec_unik=kec.kecamatan and kec.mth=strleft(${dt_id},6) and kec.brand=a.brand
where 		a.parameter is not null
group by 	1,2,3,6,7

union all  
--GAD AVG<1
select 		'IM3' brand
			,kec.circle
			,kec.region
			,count(distinct case when mthf='lmtd' and (coalesce(a.rgu_ga,0)/cast(strright(${dt_id},2) as int))<1 then a.site_id else null end) lmtd
			,count(distinct case when mthf='mtd' and(coalesce(a.rgu_ga,0)/cast(strright(${dt_id},2) as int))<1 then a.site_id else null end) mtd
			,${dt_id} dt_id
			,'Site w/ <1 GAD/Day' param
from 		(
			select 	distinct site_id, mth
			from 	rdm.nshim3_tbl_ref_addrsite_gtm_mth site_ref
			where 	site_ref.mth >=from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') --site_ref.mth =strleft(${dt_id},6)
			and 	trim(addressable_type) = 'ADDRESSABLE SITE'
			and 	list_im3 = 'Y'
			) site_ref
left join 	(
			select 		a.site_id
				,case when strleft(a.dt_id,6)=strleft(${dt_id},6) then 'mtd'
					when strleft(a.dt_id,6)= from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') then 'lmtd'
					end mthf
				,strleft(a.dt_id,6)mth
						,count(a.msisdn) rgu_ga
			from 		biadm.umr_rgs_ga_90d_dly_govt a
			join    	biadm.umr_rgs_ga_channel_mth_govt b on a.msisdn = b.msisdn and a.dt_id = b.ga_dt
			where   --    strleft(a.dt_id,6)=strleft(${dt_id},6)
			dt_id between from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMMdd') and ${dt_id}
				and right(dt_id,2)<=right(${dt_id},2)
			and         (churn_back = 'NO' OR recycled = 'YES')
			group by 	1,2,3
			) a on site_ref.site_id=a.site_id and site_ref.mth=a.mth
left join 	biadm.ref_site site on a.site_id = site.site_id
left join   rdm.bai_tbl_ref_hirarki_territory_ioh_v1 kec on concat(site.kecamatan_nm,'|',site.kabkot_nm)=kec.kecamatan and kec.mth=strleft(${dt_id},6) and kec.brand='IM3'
group by 	1,2,3,6,7
 
union all

--DSE WITH <12 GAD/DAY
select 		'IM3' brand
			,b.circle
			,case
				when dse.region='NORTHERN SUMATERA' then 'NORTH SUMATERA'
				when dse.region='SOUTHERN SUMATERA' then 'SOUTH SUMATERA'
				else dse.region
			end region
			,count(distinct case when mthf='lmtd' and (coalesce(a.rgu_ga,0)/cast(strright(${dt_id},2) as int))<12 then dse.username else null end) lmtd
			,count(distinct case when mthf='mtd' and (coalesce(a.rgu_ga,0)/cast(strright(${dt_id},2) as int))<12 then dse.username else null end) mtd
			,${dt_id} dt_id
			,'DSE w/ <12 GAD' parameter
from 		(
			select 		distinct username
						,region
						,month
			from 		stg.outlet_cso_mapping a
			where 		a.month >=from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') --a.month =strleft(${dt_id},6)
			) dse
left join 	(
			select 		c.username
						,case when strleft(a.dt_id,6)=strleft(${dt_id},6) then 'mtd'
						when strleft(a.dt_id,6)= from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') then 'lmtd'
						end mthf
						,left(a.dt_id,6)mth
						,count(a.msisdn) rgu_ga
			from 		biadm.umr_rgs_ga_90d_dly_govt a
			join    	biadm.umr_rgs_ga_channel_mth_govt b on a.msisdn = b.msisdn and a.dt_id = b.ga_dt
			left join 	stg.outlet_cso_mapping c on b.organization_id=c.id_outlet and c.month=strleft(${dt_id},6)
			where       dt_id between from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMMdd') and ${dt_id}
							and right(dt_id,2)<=right(${dt_id},2)
			and         (churn_back = 'NO' OR recycled = 'YES')
			group by 	1,2,3
			) a on dse.username=a.username and dse.month=a.mth
left join 	(
			select 	distinct region
					,circle
					,mth
			from 	rdm.bai_tbl_ref_hirarki_territory_ioh_v1
			where 	brand='IM3'
			and 	mth >=from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM')  --mth=strleft(${dt_id},6)
			) b on case
				when dse.region='NORTHERN SUMATERA' then 'NORTH SUMATERA'
				when dse.region='SOUTHERN SUMATERA' then 'SOUTH SUMATERA'
				else dse.region
			end=b.region and dse.month=b.mth 
group by 	1,2,3,6,7
 
union all
 
 
--DSE WITH <5 MN/DAY
select 		'IM3' brand
			,b.circle
			,case
				when dse.region='NORTHERN SUMATERA' then 'NORTH SUMATERA'
				when dse.region='SOUTHERN SUMATERA' then 'SOUTH SUMATERA'
				else dse.region
			end region
			,count(distinct case when mthf='lmtd' and (coalesce(a.amount,0)/cast(strright(${dt_id},2) as int))<5000000 then dse.username else null end) lmtd
			,count(distinct case when mthf='mtd' and (coalesce(a.amount,0)/cast(strright(${dt_id},2) as int))<5000000 then dse.username else null end) mtd
			,${dt_id} dt_id
			,'DSE w/ <5 Mn Secondary' parameter
from 		(
			select 		distinct username
						,region
						,month
			from 		stg.outlet_cso_mapping a
			where 		a.month >=from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') --=strleft(${dt_id} ,6)
			) dse
left join 	(
			select 		username
						,case when strleft(a.dt_id,6)=strleft(${dt_id},6) then 'mtd'
            				when strleft(a.dt_id,6)= from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') then 'lmtd'
            end mthf
            ,left(dt_id,6)mth
						,sum(a.amount)/1.11 amount
			from 		biadm.omn_secondary a
			left join 	stg.outlet_cso_mapping c on a.credit_party_id=c.id_outlet and c.month=strleft(${dt_id},6)
			where 		a.dt_id between from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMMdd') and ${dt_id}
							and right(dt_id,2)<=right(${dt_id},2)
			group by 	1,2,3
			) a on dse.username=a.username and dse.month = a.mth
left join 	(
			select 	distinct region
					,circle
					,mth
			from 	rdm.bai_tbl_ref_hirarki_territory_ioh_v1
			where 	brand='IM3'
			and 	mth >=from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') --=strleft(${dt_id} ,6)
			) b on case
				when dse.region='NORTHERN SUMATERA' then 'NORTH SUMATERA'
				when dse.region='SOUTHERN SUMATERA' then 'SOUTH SUMATERA'
				else dse.region
			end=b.region and dse.month=b.mth
group by 	1,2,3,6,7
 
union all
 
--TOTAL DSE
select 		'IM3' brand
			,b.circle
			,case
				when dse.region='NORTHERN SUMATERA' then 'NORTH SUMATERA'
				when dse.region='SOUTHERN SUMATERA' then 'SOUTH SUMATERA'
				else dse.region
			end region
			,count(distinct case when month= from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') then username else null end) lmtd
			,count(distinct case when month=strleft(${dt_id},6) then username else null end)mtd
			,${dt_id} dt_id
			,'DSE' parameter
from 		(
			select 		distinct id_outlet
						,month
						,username
						,region
			from 		stg.outlet_cso_mapping a
			where 	a.month between from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') and strleft(${dt_id},6)
			) dse
left join 	(
			select 	distinct region
					,circle
					,mth
			from 	rdm.bai_tbl_ref_hirarki_territory_ioh_v1
			where 	brand='IM3'
			and  mth>=from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM')
			) b on case
				when dse.region='NORTHERN SUMATERA' then 'NORTH SUMATERA'
				when dse.region='SOUTHERN SUMATERA' then 'SOUTH SUMATERA'
				else dse.region
			end=b.region and dse.month=b.mth
group by 	1,2,3,6,7
 
union all
 
--TOTAL MITRA 3KIOSK
select brand, circle, region
	,count(distinct case when mth =from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM')
				and (brand='IM3' AND pt_type='SDP') then pt_id 
						when mth =from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM')
				and (brand='3ID' and pt_type='3KIOSK') then pt_id 
				else null end)lmtd 
	,count(distinct case when mth=strleft(${dt_id},6) and (brand='IM3' AND pt_type='SDP') then pt_id
						when mth=strleft(${dt_id},6) and (brand='3ID' and pt_type='3KIOSK') then pt_id
			else  null end)mth
	,${dt_id} dt_id, 'SDP/3KIOSK' parameter
	from rdm.bai_tbl_ref_hirarki_territory_ioh_v1
where 		--mth=strleft(${dt_id},6)
			mth >= from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM')
			and (
					(brand='IM3' AND pt_type='SDP')
				or 	(brand='3ID' and pt_type='3KIOSK')
				)
group by 1,2,3,6,7			

union all
 
--SDP<350 GA
select 		'IM3' brand
			,circle
			,region
			,count(distinct case when mthf='lmtd' and coalesce(rgu_ga,0)<350 then kec.pt_id else null end) lmtd
			,count(distinct case when mthf='mtd' and coalesce(rgu_ga,0)<350 then kec.pt_id else null end) mtd
			,${dt_id} dt_id
			,'SDP <350 GA' parameter
from 		(
			select 	distinct pt_id
					,circle
					,region
					,mth
			from 	rdm.bai_tbl_ref_hirarki_territory_ioh_v1
			where 	mth>=from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM')  --mth=strleft(${dt_id},6)
			and 	brand='IM3'
			and 	pt_type='SDP'
			) kec
left join 	(
			select      kec.pt_id
						,case when strleft(a.dt_id,6)=strleft(${dt_id},6) then 'mtd'
            					when strleft(a.dt_id,6)= from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') then 'lmtd'
            			end mthf
            			,mth
						,count(a.msisdn) rgu_ga
            from        biadm.umr_rgs_ga_90d_dly_govt a
            join        biadm.umr_rgs_ga_channel_mth_govt b on a.msisdn = b.msisdn and a.dt_id = b.ga_dt
            left join   biadm.ref_site site on a.site_id=site.site_id
            left join   rdm.bai_tbl_ref_hirarki_territory_ioh_v1 kec on concat(site.kecamatan_nm,'|',site.kabkot_nm)=kec.kecamatan and kec.mth=strleft(${dt_id},6) and kec.brand='IM3'
            where       dt_id between from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMMdd') and ${dt_id}
						and right(dt_id,2)<=right(${dt_id},2)
            and         (churn_back = 'NO' OR recycled = 'YES')
            and 		(case
					        when flag_sp = 'SP OLA' then 'RGUGA-Digital-OLA'
					        when channel_grp = 'TRADITIONAL' then 'RGUGA-Trad-Outlet'
					        when channel_grp = 'DSF' then 'RGUGA-Trad-DSF'
					        when channel_grp = 'MODERN' and channel like '%ONLINE%' then 'RGUGA-Digital-Online'
					        when channel_grp = 'MODERN' then 'RGUGA-Digital-Modern'
					        else
					            case
					                when b.channel_alloc = 'TRADITIONAL' then 'RGUGA-Trad-Outlet'
					                when b.channel_alloc = 'DSF' then 'RGUGA-Trad-DSF'
					                else 'RGUGA-Others'
					            end
					    end) in ('RGUGA-Trad-Outlet','RGUGA-Trad-DSF')
		    group by 	1,2,3
			) a on kec.pt_id=a.pt_id and kec.mth=a.mth
group by 	1,2,3,6,7
 
union all
 
--SDP<75 MN
select 		'IM3' brand
			,circle
			,region
			,count(distinct case when mthf='lmtd' and coalesce(amount,0)<75000000 then kec.pt_id else null end) lmtd
			,count(distinct case when mthf='mtd' and coalesce(amount,0)<75000000 then kec.pt_id else null end) mtd
			,${dt_id} dt_id
			,'SDP <75 Mn' parameter
from 		(
			select 	distinct pt_id
					,circle
					,region
					,mth
			from 	rdm.bai_tbl_ref_hirarki_territory_ioh_v1
			where 	mth>=from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM')  --mth=strleft(${dt_id},6)
			and 	brand='IM3'
			and 	pt_type='SDP'
			) kec
left join 	(
			select 		tty.pt_id
						,case when strleft(a.dt_id,6)=strleft(${dt_id},6) then 'mtd'
            					when strleft(a.dt_id,6)= from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMM') then 'lmtd'
            			end mthf
            			,left(a.dt_id,6) mth
						,sum(a.amount)/1.11 amount
			from 		(select * from biadm.omn_secondary 
							where 	--	a.dt_id between from_timestamp(date_trunc('month',to_timestamp(${dt_id},'yyyyMMdd')),'yyyyMMdd') and ${dt_id}
						dt_id between from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMMdd') and ${dt_id}
						and right(dt_id,2)<=right(${dt_id},2)
						)a
			left join 	(
					        select *
					        from
					            (
					                select
					                    dt_id,
					                    a.site_id,
					                    concat(kecamatan_nm,'|',kabkot_nm) kec_unik,
					                    organization_id,
					                    organization_name,
					                    ROW_NUMBER() OVER (partition by organization_id, strleft(a.dt_id,6) ORDER BY dt_id desc) AS rn
					                from
					                    biadm.omn_outlet_loc_ns a
					                left join
					                    biadm.ref_site b
					                    on a.site_id = b.site_id
					                where
					                    a.dt_id between from_timestamp(trunc(months_sub(to_timestamp(${dt_id},'yyyyMMdd'),1),'month'),'yyyyMMdd') and ${dt_id}
					            ) a
					        where rn = 1
					    ) b
					    on a.credit_party_id = b.organization_id
					    and strleft(a.dt_id,6) = strleft(b.dt_id,6)
			left join   biadm.ref_site site on b.site_id=site.site_id
			left join 	rdm.bai_tbl_ref_hirarki_territory_ioh_v1 tty on concat(site.kecamatan_nm,'|',site.kabkot_nm) = tty.kecamatan 
			and tty.mth = strleft(${dt_id},6) and tty.brand='IM3'
			group by 	1,2,3
			) a on kec.pt_id=a.pt_id and kec.mth=a.mth
group by 	1,2,3,6,7