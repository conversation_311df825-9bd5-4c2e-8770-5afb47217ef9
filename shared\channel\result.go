package channel

type Result[T any] struct {
	err error
	val T
}

func NewResult[T any](val T, err error) Result[T] {
	return Result[T]{val: val, err: err}
}

func (r Result[T]) Unwrap() T {
	if r.err != nil {
		panic(r.err)
	}
	return r.val
}

func (r Result[T]) UnwrapErr() error {
	return r.err
}

func (r Result[T]) IsErr() bool {
	return r.err != nil
}

func (r Result[T]) IsOk() bool {
	return r.err == nil
}

func (r Result[T]) Map(f func(T)) Result[T] {
	if r.err != nil {
		return r
	}

	f(r.val)
	return r
}

func (r Result[T]) MapErr(f func(error)) Result[T] {
	if r.err != nil {
		f(r.err)
	}

	return r
}

func OkResult[T any](val T) Result[T] {
	return Result[T]{val: val}
}

func ErrResult[T any](err error) Result[T] {
	return Result[T]{err: err}
}
