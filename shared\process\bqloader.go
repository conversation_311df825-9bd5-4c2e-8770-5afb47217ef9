package process

import (
	"context"
	"errors"
	"fmt"
	"io"
	"reflect"
	"strings"
	"sync"
	"time"

	ctxpkg "github.com/csee-pm/etl/shared/context"
	"github.com/parquet-go/parquet-go"

	"cloud.google.com/go/bigquery"
)

type bqStreamBulkLoaderOptions struct {
	sourceFormat      bigquery.DataFormat
	writeDisposition  bigquery.TableWriteDisposition
	createDisposition bigquery.TableCreateDisposition
	maxBadRecord      int64
	batchSize         int
	progressCallBack  func(int)
	Labels            map[string]string

	client         *bigquery.Client
	validateSchema bool
}

func defaultBqStreamBulkLoaderOptions() *bqStreamBulkLoaderOptions {
	return &bqStreamBulkLoaderOptions{
		sourceFormat:      bigquery.Parquet,
		writeDisposition:  bigquery.WriteAppend,
		createDisposition: bigquery.CreateIfNeeded,
		maxBadRecord:      0,
		batchSize:         10000,
		progressCallBack:  func(int) {},
		Labels: map[string]string{
			"format": "PARQUET",
			"method": "io-pipe",
		},
	}
}

func WithSourceFormat(format bigquery.DataFormat) BigQueryStreamBulkLoadOption {
	return func(o *bqStreamBulkLoaderOptions) {
		o.sourceFormat = format
	}
}

func WithWriteDisposition(disposition bigquery.TableWriteDisposition) BigQueryStreamBulkLoadOption {
	return func(o *bqStreamBulkLoaderOptions) {
		o.writeDisposition = disposition
	}
}

func WithCreateDisposition(disposition bigquery.TableCreateDisposition) BigQueryStreamBulkLoadOption {
	return func(o *bqStreamBulkLoaderOptions) {
		o.createDisposition = disposition
	}
}

func WithMaxBadRecord(maxBadRecord int64) BigQueryStreamBulkLoadOption {
	return func(o *bqStreamBulkLoaderOptions) {
		o.maxBadRecord = maxBadRecord
	}
}

func WithBatchSize(batchSize int) BigQueryStreamBulkLoadOption {
	return func(o *bqStreamBulkLoaderOptions) {
		o.batchSize = batchSize
	}
}

func WithProgressCallBack(progressCallBack func(int)) BigQueryStreamBulkLoadOption {
	return func(o *bqStreamBulkLoaderOptions) {
		o.progressCallBack = progressCallBack
	}
}

func WithLabels(labels map[string]string) BigQueryStreamBulkLoadOption {
	return func(o *bqStreamBulkLoaderOptions) {
		o.Labels = labels
	}
}

type BigQueryStreamBulkLoadOption func(o *bqStreamBulkLoaderOptions)

func BigQueryStreamBulkLoader[T any](c context.Context, datasetID, tableID string, rows <-chan T, options ...BigQueryStreamBulkLoadOption) error {
	logger := ctxpkg.ExtractLogger(c)
	opts := defaultBqStreamBulkLoaderOptions()
	for _, o := range options {
		o(opts)
	}

	var bq *bigquery.Client
	var err error

	if opts.client != nil {
		bq = opts.client
	} else {
		bq, err = makeBqClient(c)
		if err != nil {
			return err
		}
		defer bq.Close()
	}

	ctx, cancel := context.WithCancel(c)
	defer cancel()

	// optional schema validation upfront to fail-fast before streaming
	if opts.validateSchema {
		if err := validateTableSchemaMatchesType[T](ctx, bq, datasetID, tableID); err != nil {
			return fmt.Errorf("schema validation failed: %w", err)
		}
	}

	pr, pw := io.Pipe()

	writerErrChan := make(chan error, 1)
	readerErrChan := make(chan error, 1)
	progressChan := make(chan int, 100)

	progressCallBack := opts.progressCallBack

	var wg sync.WaitGroup

	wg.Add(1)
	// Progress monitoring goroutine: progressChan -> progressCallBack
	go func() {
		defer wg.Done()
		totalRows := 0
		for rows := range progressChan {
			totalRows += rows
			if progressCallBack != nil {
				progressCallBack(totalRows)
			}
		}
	}()

	wg.Add(1)
	// Writer goroutine with progress reporting: writer: rows -> bigquery.DataFormat -> pw
	go func() {
		defer wg.Done()
		defer close(progressChan)

		var dfWriter rowsToDataFormatWriter[T]

		switch opts.sourceFormat {
		case bigquery.Parquet:
			dfWriter = writeRowsToParquet[T]
		default:
			writerErrChan <- fmt.Errorf("unsupported source format: %s", opts.sourceFormat)
			pw.CloseWithError(err)
			cancel()
			return
		}

		if err := dfWriter(ctx, pw, rows, progressChan, opts.batchSize); err != nil {
			writerErrChan <- err
			pw.CloseWithError(err)
			cancel()
			return
		}

		pw.Close()
		writerErrChan <- nil
	}()

	wg.Add(1)
	// Reader goroutine: reader: Parquet -> pr -> BigQuery
	go func() {
		defer wg.Done()

		table := bq.Dataset(datasetID).Table(tableID)
		rs := bigquery.NewReaderSource(pr)
		rs.SourceFormat = opts.sourceFormat
		rs.MaxBadRecords = opts.maxBadRecord

		loader := table.LoaderFrom(rs)
		loader.WriteDisposition = opts.writeDisposition
		loader.CreateDisposition = opts.createDisposition
		loader.Labels = opts.Labels

		job, err := loader.Run(c)
		if err != nil {
			readerErrChan <- err
			cancel()
			return
		}

		status, err := job.Wait(c)
		if err != nil {
			readerErrChan <- err
			cancel()
			return
		}

		if err := status.Err(); err != nil {
			readerErrChan <- err
			cancel()
			return
		}

		if status.Statistics != nil {
			if ls, ok := status.Statistics.Details.(*bigquery.LoadStatistics); ok && ls != nil {
				logger.Info("Successfully loaded %d rows to BigQuery via streaming\n", ls.OutputRows)
			}
		}

		readerErrChan <- nil

	}()

	// Wait for completion
	wg.Wait()

	// Check errors
	writerErr := <-writerErrChan
	readerErr := <-readerErrChan

	if writerErr != nil {
		return fmt.Errorf("writer error: %w", writerErr)
	}

	if readerErr != nil {
		return fmt.Errorf("reader error: %w", readerErr)
	}

	return nil

}

type rowsToDataFormatWriter[T any] func(c context.Context, w io.Writer, rows <-chan T, progressChan chan<- int, batchSize int) error

func writeRowsToParquet[T any](c context.Context, w io.Writer, rows <-chan T, progressChan chan<- int, batchSize int) error {
	var err error
	pqw := parquet.NewGenericWriter[T](w)
	defer func() {
		cerr := pqw.Close()
		if cerr != nil {
			if err == nil {
				err = cerr
			} else {
				err = fmt.Errorf("multiple errors: %w; %v", err, cerr)
			}
		}
	}()

	batch := make([]T, 0, max(1, batchSize))
	for row := range rows {
		select {
		case <-c.Done():
			return context.Cause(c)
		default:
		}
		batch = append(batch, row)
		if len(batch) == batchSize {
			_, err = pqw.Write(batch)
			if err != nil {
				return err
			}
			progressChan <- len(batch)
			batch = batch[:0]
		}
	}

	if len(batch) > 0 {
		_, err = pqw.Write(batch)
		if err != nil {
			return err
		}
		progressChan <- len(batch)
	}

	return nil
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// --- Schema validation ---

var timeType = reflect.TypeOf(time.Time{})

// validateTableSchemaMatchesType fetches BigQuery schema and compares to an inferred schema from T.
func validateTableSchemaMatchesType[T any](ctx context.Context, client *bigquery.Client, datasetID, tableID string) error {
	md, err := client.Dataset(datasetID).Table(tableID).Metadata(ctx)
	if err != nil {
		return err
	}
	inf, err := InferBigQuerySchemaFromType[T]()
	if err != nil {
		return err
	}
	if err := compareBQSchema(inf, md.Schema); err != nil {
		return err
	}
	return nil
}

// InferBigQuerySchemaFromType reflects T into an approximate BigQuery schema.
// Rules:
//   - Field name: parquet tag name if set and not "-"; else json tag; else Go field name.
//   - Mode: pointer => NULLABLE; slice (except []byte) => REPEATED; else REQUIRED.
//   - Types: string, bool, (u)int*, float*, []byte, time.Time, nested structs => RECORD.
func InferBigQuerySchemaFromType[T any]() (bigquery.Schema, error) {
	var zero T
	t := reflect.TypeOf(zero)
	for t.Kind() == reflect.Pointer {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return nil, errors.New("T must be a struct or pointer to struct for schema inference")
	}
	return inferStructToBQSchema(t)
}

func inferStructToBQSchema(t reflect.Type) (bigquery.Schema, error) {
	fields := make(bigquery.Schema, 0, t.NumField())
	for i := 0; i < t.NumField(); i++ {
		f := t.Field(i)
		if f.PkgPath != "" { // unexported
			continue
		}
		name, skip := pickFieldName(f)
		if skip || name == "" {
			continue
		}

		ft := f.Type
		required := true
		repeated := false

		// repeated
		if ft.Kind() == reflect.Slice && ft.Elem().Kind() != reflect.Uint8 { // not []byte
			repeated = true
			required = false
			ft = ft.Elem()
		}
		// nullable
		for ft.Kind() == reflect.Pointer {
			required = false
			ft = ft.Elem()
		}

		fs := &bigquery.FieldSchema{Name: name, Repeated: repeated, Required: required}
		// base type mapping
		if ft == timeType {
			fs.Type = bigquery.TimestampFieldType
		} else {
			switch ft.Kind() {
			case reflect.String:
				fs.Type = bigquery.StringFieldType
			case reflect.Bool:
				fs.Type = bigquery.BooleanFieldType
			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
				reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
				fs.Type = bigquery.IntegerFieldType
			case reflect.Float32, reflect.Float64:
				fs.Type = bigquery.FloatFieldType
			case reflect.Slice:
				if ft.Elem().Kind() == reflect.Uint8 {
					fs.Type = bigquery.BytesFieldType // []byte
				} else {
					return nil, fmt.Errorf("unsupported nested slice type for field %s", name)
				}
			case reflect.Struct:
				fs.Type = bigquery.RecordFieldType
				sub, err := inferStructToBQSchema(ft)
				if err != nil {
					return nil, err
				}
				fs.Schema = sub
			default:
				return nil, fmt.Errorf("unsupported field %s of kind %s", name, ft.Kind())
			}
		}
		fields = append(fields, fs)
	}
	return fields, nil
}

func pickFieldName(f reflect.StructField) (name string, skip bool) {
	if tag := f.Tag.Get("parquet"); tag != "" {
		if tag == "-" {
			return "", true
		}
		name = tag
		if i := strings.IndexByte(name, ','); i >= 0 {
			name = name[:i]
		}
		if name != "" {
			return name, false
		}
	}

	if tag := f.Tag.Get("json"); tag != "" {
		if tag == "-" {
			return "", true
		}
		name = tag
		if i := strings.IndexByte(name, ','); i >= 0 {
			name = name[:i]
		}
		if name != "" {
			return name, false
		}
	}
	return f.Name, false
}

// compareBQSchema checks that inferred == actual (by field name, type, and mode), order-insensitive.
func compareBQSchema(inferred, actual bigquery.Schema) error {
	ai := make(map[string]*bigquery.FieldSchema, len(actual))
	for _, f := range actual {
		ai[strings.ToLower(f.Name)] = f
	}

	var errs []string
	var walk func(path string, inf *bigquery.FieldSchema)
	walk = func(path string, inf *bigquery.FieldSchema) {
		nameKey := strings.ToLower(inf.Name)
		full := path + inf.Name
		af, ok := ai[nameKey]
		if !ok {
			errs = append(errs, fmt.Sprintf("missing field in table: %s", full))
			return
		}
		if af.Type != inf.Type {
			errs = append(errs, fmt.Sprintf("type mismatch for %s: inferred %s, table %s", full, inf.Type, af.Type))
		}
		if af.Required != inf.Required {
			errs = append(errs, fmt.Sprintf("required mismatch for %s: inferred %v, table %v", full, inf.Required, af.Required))
		}
		if af.Repeated != inf.Repeated {
			errs = append(errs, fmt.Sprintf("repeated mismatch for %s: inferred %v, table %v", full, inf.Repeated, af.Repeated))
		}
		if inf.Type == bigquery.RecordFieldType {
			// build child maps for nested
			aChildren := make(map[string]*bigquery.FieldSchema, len(af.Schema))
			for _, ch := range af.Schema {
				aChildren[strings.ToLower(ch.Name)] = ch
			}
			for _, ch := range inf.Schema {
				aiSaved := ai
				ai = aChildren
				walk(full+".", ch)
				ai = aiSaved
			}
		}
	}
	for _, f := range inferred {
		walk("", f)
	}
	if len(errs) > 0 {
		return errors.New(strings.Join(errs, "; "))
	}
	return nil
}
