package dto

import (
	utype "github.com/likearthian/types"
	"gopkg.in/guregu/null.v4"
)

type CreateUserRequestDTO struct {
	Username null.String `json:"username"`
	Fullname null.String `json:"fullname"`
	Email    null.String `json:"email"`
	Password null.String `json:"password"`
	IsAdmin  null.Bool   `json:"is_admin"`
	IsLdap   null.Bool   `json:"is_ldap"`
}

type GetUserRequestDTO struct {
	Username null.String `query:"id"`
}

type GetUsersRequestDTO struct {
	Username utype.SliceOfString `query:"id"`
}
