package api

import (
	"net/http"
)

// handleListFiles handles GET /api/files
func (r *Router) handleListFiles(w http.ResponseWriter, req *http.Request) {
	files, err := r.db.ListFiles()
	if err != nil {
		respondError(w, http.StatusInternalServerError, "Failed to list files: "+err.Error())
		return
	}

	respondJSON(w, http.StatusOK, files)
}

// handleGetFile handles GET /api/files/{id}
func (r *Router) handleGetFile(w http.ResponseWriter, req *http.Request) {
	id, err := getIDParam(req, "id")
	if err != nil {
		respondError(w, http.StatusBadRequest, "Invalid file ID: "+err.Error())
		return
	}

	file, err := r.db.GetFile(id)
	if err != nil {
		respondError(w, http.StatusNotFound, "File not found: "+err.Error())
		return
	}

	respondJSON(w, http.StatusOK, file)
}

// handleListProcessFiles handles GET /api/processes/{id}/files
func (r *Router) handleListProcessFiles(w http.ResponseWriter, req *http.Request) {
	id, err := getIDParam(req, "id")
	if err != nil {
		respondError(w, http.StatusBadRequest, "Invalid process ID: "+err.Error())
		return
	}

	files, err := r.db.ListFilesByProcess(id)
	if err != nil {
		respondError(w, http.StatusInternalServerError, "Failed to list files: "+err.Error())
		return
	}

	respondJSON(w, http.StatusOK, files)
}

// handleDeleteFile handles DELETE /api/files/{id}
func (r *Router) handleDeleteFile(w http.ResponseWriter, req *http.Request) {
	id, err := getIDParam(req, "id")
	if err != nil {
		respondError(w, http.StatusBadRequest, "Invalid file ID: "+err.Error())
		return
	}

	// Get the file first to check if it exists
	file, err := r.db.GetFile(id)
	if err != nil {
		respondError(w, http.StatusNotFound, "File not found: "+err.Error())
		return
	}

	// Delete the file record from the database
	if err := r.db.DeleteFile(id); err != nil {
		respondError(w, http.StatusInternalServerError, "Failed to delete file: "+err.Error())
		return
	}

	// Note: In a real implementation, you might also want to delete the actual file from disk
	// if it's no longer needed. For now, we'll just delete the database record.

	respondJSON(w, http.StatusOK, file)
}
