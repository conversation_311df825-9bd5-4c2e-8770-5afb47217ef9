package config

type ImpalaConfig struct {
	Host       string `yaml:"host"`
	Port       int    `yaml:"port"`
	Credential string `yaml:"credential"`
	Database   string `yaml:"database"`
	CaCert     string `yaml:"ca_cert" mapstructure:"ca_cert"`
	User       string `yaml:"-" config:"user"`
	Password   string `yaml:"-" config:"password"`
}

func (ic ImpalaConfig) ToMap() map[string]any {
	var cfMap = make(map[string]interface{})
	cfMap["host"] = ic.Host
	cfMap["port"] = ic.Port
	cfMap["credential"] = ic.Credential
	cfMap["database"] = ic.Database
	cfMap["ca_cert"] = ic.CaCert
	return cfMap
}
