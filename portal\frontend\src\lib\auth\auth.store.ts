import { defineStore } from 'pinia';
import { type AuthDTO } from './auth.dto';

export const AuthStore = defineStore('auth', () => {
  const auth = ref<AuthDTO | null>(null);

  function setAuth(auth: AuthDTO) {
    auth
  }

  function clearAuth() {
    token.value = '';
    expiry.value = 0;
    username.value = '';
    fullname.value = '';
    email.value = '';
    isAdmin.value = false;
  }

  return { token, expiry, username, fullname, email, isAdmin, setAuth, clearAuth };
});
