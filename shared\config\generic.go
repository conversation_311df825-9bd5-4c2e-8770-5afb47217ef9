package config

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/csee-pm/etl/shared/enc"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/spf13/viper"
)

type ConfigMapper interface {
	ToMap() map[string]any
}

type ConfigPrompter interface {
	Prompt(v *viper.Viper) error
}

type ConfigItem interface {
	ConfigMapper
	ConfigPrompter
}

type credentialFieldPosition struct {
	Credential int
	User       int
	Password   int
}

func ToConfigMap[T any](conf T) map[string]any {
	var vConf = reflect.ValueOf(conf)

	return toMap(vConf)
}

func toMap(conf reflect.Value) map[string]any {
	var tmap = make(map[string]any)
	tConf := conf.Type()
	if tConf.Implements(reflect.TypeOf((*ConfigMapper)(nil)).Elem()) {
		return conf.Interface().(ConfigMapper).ToMap()
	}

	for i := 0; i < conf.NumField(); i++ {
		field := conf.Field(i)
		tfield := tConf.Field(i)
		tag := tfield.Tag.Get("yaml")
		if tag == "-" {
			continue
		}

		configTag := tfield.Tag.Get("config")
		value := field.Interface()
		if tag == "" {
			tag = configTag
			if tag == "" {
				tag = utils.ToSnakeCase(tfield.Name)
			}
		}

		ftyp := field.Type()
		if ftyp.Kind() == reflect.Ptr {
			ftyp = ftyp.Elem()
		}

		if ftyp.Kind() == reflect.Struct {
			tmap[tag] = toMap(field)
			continue
		}

		tmap[tag] = value
	}

	return tmap
}

func GetConfig[T ConfigMapper](v *viper.Viper, key string) (T, error) {
	conf := new(T)
	if err := v.UnmarshalKey(key, conf); err != nil {
		return *conf, err
	}

	vConf := reflect.ValueOf(conf)
	if err := processCredential(vConf); err != nil {
		return *conf, err
	}

	// return vConf.Interface().(T), nil
	return *conf, nil
}

func ParseConfig[T ConfigMapper](v *viper.Viper, conf *T, key string) error {
	if err := v.UnmarshalKey(key, conf); err != nil {
		return err
	}

	vConf := reflect.ValueOf(conf)
	if err := processCredential(vConf); err != nil {
		return err
	}

	return nil
}

func processCredential(conf reflect.Value) error {
	cfp := findCredentialField(conf.Type())
	if cfp != nil {
		if err := parseCredential(conf, *cfp); err != nil {
			return err
		}
	}
	tconf := conf.Type()
	if tconf.Kind() == reflect.Ptr {
		tconf = tconf.Elem()
	}

	for i := 0; i < tconf.NumField(); i++ {
		field := conf.Elem().Field(i)
		tVal := field.Type()
		if tVal.Kind() == reflect.Ptr {
			tVal = tVal.Elem()
		}

		if tVal.Kind() == reflect.Struct {
			if err := processCredential(field.Addr()); err != nil {
				return err
			}
		}
	}

	return nil
}

func parseCredential(conf reflect.Value, fieldPos credentialFieldPosition) error {
	if fieldPos.Credential == -1 {
		return nil
	}

	vconf := conf
	if vconf.Kind() == reflect.Ptr {
		vconf = vconf.Elem()
	}

	credential := vconf.Field(fieldPos.Credential).String()
	if credential == "" {
		return nil
	}

	creds, err := enc.Decrypt(vconf.Field(fieldPos.Credential).String())
	if err != nil {
		return err
	}

	credsArr := strings.Split(creds, ":")
	if len(credsArr) != 2 {
		return fmt.Errorf("invalid credential format")
	}

	vconf.Field(fieldPos.User).SetString(strings.TrimSpace(credsArr[0]))
	vconf.Field(fieldPos.Password).SetString(strings.TrimSpace(credsArr[1]))

	return nil
}

func findCredentialField(t reflect.Type) *credentialFieldPosition {
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	if t.Kind() != reflect.Struct {
		return nil
	}

	check := &credentialFieldPosition{
		Credential: -1,
		User:       -1,
		Password:   -1,
	}

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		yamlTag := field.Tag.Get("yaml")
		configTag := field.Tag.Get("config")

		if yamlTag == "credential" || configTag == "credential" || utils.ToSnakeCase(field.Name) == "credential" {
			check.Credential = i
			continue
		}

		if yamlTag == "user" || configTag == "user" || utils.ToSnakeCase(field.Name) == "user" {
			check.User = i
			continue
		}

		if yamlTag == "password" || configTag == "password" || utils.ToSnakeCase(field.Name) == "password" {
			check.Password = i
			continue
		}
	}

	return check
}
