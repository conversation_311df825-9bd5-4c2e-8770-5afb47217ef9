package distrib

import (
	"strconv"

	"gopkg.in/guregu/null.v4"
)

type DistribMtdParam struct {
	DtID      string  `db:"dt_id"`
	Parameter string  `db:"parameter"`
	LMTD      float64 `db:"lmtd"`
	MTD       float64 `db:"mtd"`
}

type DistribMtdData struct {
	Brand  string      `db:"brand"`
	Circle null.String `db:"circle"`
	Region null.String `db:"region"`
	DistribMtdParam
}

func (d DistribMtdData) GetColumns() []string {
	return []string{"brand", "circle", "region", "lmtd", "mtd", "dt_id", "parameter"}
}

func (d DistribMtdData) GetRowValues() []string {
	return []string{d.Brand, d.Circle.String, d.Region.String, strconv.FormatFloat(d.LMTD, 'f', -1, 64), strconv.FormatFloat(d.MTD, 'f', -1, 64), d.DtID, d.Parameter}
}

func (d *DistribMtdData) ReadRow(row []string) error {
	d.Brand = row[0]
	d.Circle = null.StringFrom(row[1])
	d.Region = null.StringFrom(row[2])
	d.LMTD, _ = strconv.ParseFloat(row[3], 64)
	d.MTD, _ = strconv.ParseFloat(row[4], 64)
	d.DtID = row[5]
	d.Parameter = row[6]
	return nil
}

type DistribFmData struct {
	MonthID   string
	Brand     string
	Circle    null.String
	Region    null.String
	Parameter string
	Value     float64
}

func (d DistribFmData) GetColumns() []string {
	return []string{"month_id", "brand", "circle", "region", "parameter", "value"}
}

func (d DistribFmData) GetRowValues() []string {
	return []string{d.MonthID, d.Brand, d.Circle.String, d.Region.String, d.Parameter, strconv.FormatFloat(d.Value, 'f', -1, 64)}
}

func (d *DistribFmData) ReadRow(row []string) error {
	d.MonthID = row[0]
	d.Brand = row[1]
	d.Circle = null.StringFrom(row[2])
	d.Region = null.StringFrom(row[3])
	d.Parameter = row[4]
	d.Value, _ = strconv.ParseFloat(row[5], 64)
	return nil
}

type DistribReportData struct {
	EntityType      string
	EntityName      string
	ParamMtdDataMap map[string]DistribMtdData
	ParamFmDataMap  map[string]map[string]DistribFmData
}

type DistribReport struct {
	IOH    []DistribReportData
	IM3    []DistribReportData
	Tri    []DistribReportData
	Target BrandSndTarget
}

type TargetKpi struct {
	TargetGA    int
	TargetSecMn int
}

type SndTarget struct {
	Circle string
	Region string
	Brand  string
	TargetKpi
}

type EntityTarget struct {
	EntityType string
	EntityName string
	Target     TargetKpi
}

type BrandSndTarget struct {
	IOH []EntityTarget
	IM3 []EntityTarget
	Tri []EntityTarget
}

type DistribBrandData struct {
	RegionMtdParamMap   map[string]map[string]*DistribMtdData
	CircleMtdParamMap   map[string]map[string]*DistribMtdData
	NationalMtdParamMap map[string]*DistribMtdData
	RegionFmParamMap    map[string]map[string]map[string]*DistribFmData
	CircleFmParamMap    map[string]map[string]map[string]*DistribFmData
	NationalFmParamMap  map[string]map[string]*DistribFmData
	ReportEntries       []DistribReportData
}

type LrsData struct {
	MonthID     string      `db:"month_id"`
	Circle      null.String `db:"circle"`
	Region      null.String `db:"region"`
	GraduateCnt int         `db:"graduate_cnt"`
}

func (l LrsData) GetColumns() []string {
	return []string{"month_id", "circle", "region", "graduate_cnt"}
}

func (l LrsData) GetRowValues() []string {
	return []string{l.MonthID, l.Circle.String, l.Region.String, strconv.Itoa(l.GraduateCnt)}
}
