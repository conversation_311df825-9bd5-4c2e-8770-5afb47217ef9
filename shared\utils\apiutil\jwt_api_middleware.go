package apiutil

import "github.com/dgrijalva/jwt-go/v4"

// var jwtSigningMethod = jwt.SigningMethodHS256

type JwtClaims struct {
	Username string
	IsAdmin  bool
	FullName string
	Email    string
	jwt.StandardClaims
}

// // ClaimsFactory is a factory for jwt.Claims.
// // Useful in NewParser middleware.
// type ClaimsFactory func() jwt.Claims

// // MapClaimsFactory is a ClaimsFactory that returns
// // an empty jwt.MapClaims.
// func MapClaimsFactory() jwt.Claims {
// 	return jwt.MapClaims{}
// }

// // StandardClaimsFactory is a ClaimsFactory that returns
// // an empty jwt.StandardClaims.
// func StandardClaimsFactory() jwt.Claims {
// 	return &jwt.StandardClaims{}
// }

// func CreateToken(issuer, username, fullname, email string, isAdmin bool, aud jwt.ClaimStrings, duration time.Duration) (string, error) {
// 	expiry := float64(time.Now().Add(duration).Unix())
// 	claims := JwtClaims{
// 		Username: username,
// 		Email:    email,
// 		IsAdmin:  isAdmin,
// 		FullName: fullname,
// 		StandardClaims: jwt.StandardClaims{
// 			ExpiresAt: jwt.NewTime(expiry),
// 			Issuer:    issuer,
// 			Audience:  aud,
// 		},
// 	}

// 	token := jwt.NewWithClaims(jwtSigningMethod, claims)
// 	source := rand.NewSource(time.Now().UnixNano())
// 	r := rand.New(source)
// 	n := 1 + r.Intn(4-1+1)

// 	if n < 1 || n > 4 {
// 		n = 1
// 	}
// 	kid := strconv.Itoa(n)
// 	key := []byte(KidMap[kid])

// 	token.Header["kid"] = kid
// 	return token.SignedString(key)
// }

// func WithJWTAuthEPMiddleware[I, O any](ep api.Endpoint[I, O], options ...jwt.ParserOption) api.Endpoint[I, O] {
// 	return func(ctx context.Context, request I) (O, error) {
// 		var out O
// 		// tokenString is stored in the context from the transport handlers.
// 		tokenString, ok := ctx.Value(JWTTokenContextKey).(string)
// 		if !ok {
// 			return out, errs.ErrTokenContextMissing
// 		}

// 		options = append(options, jwt.WithAudience("ssp"))
// 		// Parse takes the token string and a function for looking up the
// 		// key. The latter is especially useful if you use multiple keys
// 		// for your application.  The standard is to use 'kid' in the head
// 		// of the token to identify which key to use, but the parsed token
// 		// (head and claims) is provided to the callback, providing
// 		// flexibility.
// 		token, err := jwt.ParseWithClaims(tokenString, &JwtClaims{}, func(token *jwt.Token) (interface{}, error) {
// 			// Don't forget to validate the alg is what you expect:
// 			if token.Method != jwtSigningMethod {
// 				return nil, errs.ErrUnexpectedSigningMethod
// 			}

// 			return JwtKeyGetterFunc(token)
// 		}, options...)

// 		if err != nil {
// 			switch err.(type) {
// 			case *jwt.MalformedTokenError:
// 				// Token is malformed
// 				return out, errs.ErrTokenMalformed
// 			case *jwt.TokenExpiredError:
// 				// Token is expired
// 				return out, errs.ErrTokenExpired
// 			case *jwt.TokenNotValidYetError:
// 				// Token is not active yet
// 				return out, errs.ErrTokenNotActive
// 			}
// 			// We have a ValidationError but have no specific Go kit error for it.
// 			// Fall through to return original error.
// 			return out, errs.ErrForbidden
// 		}

// 		if !token.Valid {
// 			return out, errs.ErrTokenInvalid
// 		}

// 		ctx = context.WithValue(ctx, JWTClaimsContextKey, token.Claims)
// 		// ctx = context.WithValue(ctx, JWTTokenContextKey, tokenString)

// 		return ep(ctx, request)
// 	}
// }

// var KidMap = map[string]string{
// 	"1":  "6ai1Vz6dHy9PbLCKUc8QtadUIuOUMuHQ",
// 	"2":  "rUpWCnIwgvHfEpKJpXknmw5ozfBrpzbz",
// 	"3":  "bczvJVnrzXk5WHzSTm5GNMQo5nBfHnyK",
// 	"4":  "PQsc8LdzrV6Mn7Kq71E31N4vRhNpj30q",
// 	"5":  "W9X0Y1Z2A3B4C5D6E7F8G9H0I1J2K3L4",
// 	"6":  "M5N6O7P8Q9R0S1T2U3V4W5X6Y7Z8A9B0",
// 	"7":  "C1D2E3F4G5H6I7J8K9L0M1N2O3P4Q5R6",
// 	"8":  "S7T8U9V0W1X2Y3Z4A5B6C7D8E9F0G1H2",
// 	"9":  "I3J4K5L6M7N8O9P0Q1R2S3T4U5V6W7X8",
// 	"10": "Y9Z0A1B2C3D4E5F6G7H8I9J0K1L2M3N4",
// 	"11": "Z0A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5",
// 	"12": "P6Q7R8S9T0U1V2W3X4Y5Z6A7B8C9D0E1",
// 	"13": "F2G3H4I5J6K7L8M9N0O1P2Q3R4S5T6U7",
// 	"14": "V8W9X0Y1Z2A3B4C5D6E7F8G9H0I1J2K3",
// 	"15": "L4M5N6O7P8Q9R0S1T2U3V4W5X6Y7Z8A9",
// 	"16": "B0C1D2E3F4G5H6I7J8K9L0M1N2O3P4Q5",
// }
