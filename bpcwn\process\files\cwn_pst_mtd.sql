WITH kpi_parameter as (
    select
        circle,
        region,
        a.as_of_dt asof_date,
        a.mth month_id,
        a.parameter,
        a.mthf as period,
        a.brand,
        sum(a.amount) amount
    from
        rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
        left join
        (
            select *
            from rdm.bai_tbl_ref_hirarki_territory_ioh_v1
            where mth in (strleft(${mtd_dt_id},6), from_timestamp(add_months(to_timestamp(${mtd_dt_id},'yyyyMMdd'),-1),'yyyyMM'))
        ) b
        on
            a.kec_unik = b.kecamatan
        and a.mth = b.mth
        and a.brand = b.brand
    where
        1=1
    and a.dt_id = ${mtd_dt_id}
    and a.parameter in
        (
            'Primary', --im3
            'Secondary', --im3
            'secondary', --3id
            'Tertiary B#', --im3
            'tertiary' --3id
        )
    and
        a.mthf in ('mtd', 'lmtd')
    group by
        1,2,3,4,5,6,7

    union all
    --3id
    select
        b.circle,
        a.region,
        a.as_of_dt asof_date,
        a.mth month_id,
        a.parameter,
        mthf as period,
        a.brand,
        sum(a.amt) amount
    from
        rdm.bai_tbl_kpi_dashboard_3id_primseco_mtdlmtd_v1 a
        left join
        (
            select
                distinct
                circle, area, branch, cluster, pt_id, partner, pt_type, mth
            from rdm.bai_tbl_ref_hirarki_territory_ioh_v1
            where mth = strleft(${mtd_dt_id},6)
            and brand = '3ID'
        ) b
        on
            a.pt_id = b.pt_id
    where
        a.dt_id = ${mtd_dt_id}
    and b.circle is not NULL
    and a.mthf in ('mtd', 'lmtd')
    and parameter = 'primary'
    group by
        1,2,3,4,5,6,7
)
SELECT
    month_id,
    period,
    asof_date,
    circle,
    region,
    brand,
    sum(case when parameter in ('Primary', 'primary') then amount else null end) `primary`,
    sum(case when parameter in ('Secondary', 'secondary') then amount else null end) `secondary`,
    sum(case when parameter in ('Tertiary B#', 'tertiary') then amount else null end) `tertiary`
FROM
    kpi_parameter
GROUP BY
    1,2,3,4,5,6