with ga as (
	select
	    a.dt,
	    organization_id,
	    sum(net_rev) net_acq_rev,
	    count(distinct a.msisdn) ga
	from
	(
	    select
	        msisdn,
	        actvn_dt,
	        total_rev/1.11 net_rev,
	        site_id,
	        churn_back,
	        recycled,
	        date(dt_id) dt,
	        format_date('%Y%m%d', date(dt_id)) dt_id
		from
		    `data-bi-prd-935c.bi_dm.umr_rgs_ga_90d_dly_govt` a
        where
            dt_id >= timestamp(date_add(date_trunc(date(${mtd_dt}), month), interval -3 month))
        and dt_id <= ${mtd_dt}
        and (churn_back = 'NO' OR recycled = 'YES')
    ) a
    join
    (
        select
            msisdn,
            organization_id,
            channel_grp,
            channel_alloc,
            cluster_alloc,
            flag_sp,
    	    case
    	        when flag_sp = 'SP OLA' then 'RGUGA-Digital-OLA'
		        when channel_grp = 'TRADITIONAL' then 'RGUGA-Trad-Outlet'
                when channel_grp = 'DSF' then 'RGUGA-Trad-DSF'
                when channel_grp = 'MODERN' and channel like '%ONLINE%' then 'RGUGA-Digital-Online'
                when channel_grp = 'MODERN' then 'RGUGA-Digital-Modern'
		        else
		        case
		            when channel_alloc = 'TRADITIONAL' then 'RGUGA-Trad-Outlet'
		            when channel_alloc = 'DSF' then 'RGUGA-Trad-DSF'
		            else 'RGUGA-Others'
		        end
		    end channel_grp_snd,
    	    ga_dt
	    from
	        `data-bi-prd-935c.bi_dm.umr_rgs_ga_channel_mth_govt` a
    	where
    	    ga_dt >= format_date('%Y%m%d', date_trunc(date_add(date(${mtd_dt}), interval -3 month), month))
        and ga_dt <= format_date('%Y%m%d', date(${mtd_dt}))
    ) b
    on
        a.msisdn = b.msisdn
    and a.dt_id = b.ga_dt
    where
        channel_grp_snd like 'RGUGA-Trad%'
    group by 1,2
),
sec as (
    select
        date(dt_id) dt,
        credit_party_id,
        cast(sum(amount) as float64)/1.11 net_amount
    from
        `data-bi-prd-935c.bi_dm.omn_secondary` a
    where
        dt_id >= timestamp(date_trunc(date_add(date(${mtd_dt}), interval -3 month), month))
    and dt_id <= ${mtd_dt}
    group by 1,2
),
kpi as (
    select
        format_date('%Y%m', coalesce(ga.dt, sec.dt)) month_id,
        coalesce(ga.dt, sec.dt) dt,
        coalesce(ga.organization_id, sec.credit_party_id) organization_id,
        coalesce(ga.ga, 0) ga,
        coalesce(sec.net_amount, 0) net_sec_amt
    from
        ga
        full outer join
        sec
        on
            ga.dt = sec.dt
        and ga.organization_id = sec.credit_party_id
),
nbs as (
    select
        *
    from
    (
        select
            site_id,
            organization_id,
            pt_nm,
        	mpc_nm,
        	parent_org_type,
            region,
            circle,
            format_timestamp('%Y%m', dt_id) month_id,
            ROW_NUMBER() OVER (partition by organization_id, format_timestamp('%Y%m', dt_id) ORDER BY dt_id desc) AS rn
        from
            `data-bi-prd-935c.bi_dm.omn_outlet_loc_ns_all` a
        where
            dt_id >= timestamp(date_trunc(date_add(date(${mtd_dt}), interval -3 month), month))
        and dt_id <= ${mtd_dt}
    ) a
    where rn=1
),
circle_ref as
(
    select
        site_id,
        sales_area,
        mpc_nm,
        pt_nm,
        region_circle,
        circle,
        kabkot_nm
    from
        `data-bi-prd-935c`.bi_dm.ref_site
)
select
    coalesce(kpi.month_id, nbs.month_id) month_id,
    case
        when coalesce(kpi.month_id, nbs.month_id) = format_date('%Y%m', date(${mtd_dt})) then 'MTD'
        else 'LMTD'
    end period,
    'IM3' as brand,
    coalesce(nbs.circle,circle_ref.circle) as circle,
    coalesce(nbs.region, circle_ref.region_circle) as region,
    circle_ref.kabkot_nm kabupaten,
    CASE
        WHEN parent_org_type like 'SDP%' OR coalesce(nbs.mpc_nm, circle_ref.mpc_nm) LIKE 'SDP%'
        THEN 'SDP'
        ELSE 'MPC'
    END AS flag,
    coalesce(nbs.pt_nm,circle_ref.pt_nm) dist_id,
    extract(day from date(${mtd_dt})) day_no,
    max(format_date('%Y%m%d', kpi.dt)) asof_date,
    sum(coalesce(kpi.ga, 0)) ga,
    round(sum(coalesce(kpi.net_sec_amt, 0)) / 1000000, 3) net_secondary_mn
from
    nbs
    left join
    kpi
    on
        nbs.organization_id = kpi.organization_id
    and nbs.month_id=kpi.month_id
    left join
    circle_ref
    on
        nbs.site_id = circle_ref.site_id
where
    kpi.dt >= date_trunc(date_add(date(${mtd_dt}), interval -1 month), month)
and extract(day from kpi.dt) <= extract(day from date(${mtd_dt}))
group by 1,2,4,5,6,7,8

UNION ALL

select
    coalesce(kpi.month_id, nbs.month_id) month_id,
    'FM' period,
    'IM3' as brand,
    coalesce(nbs.circle, circle_ref.circle) as circle,
    coalesce(nbs.region, circle_ref.region_circle) as region,
    circle_ref.kabkot_nm kabupaten,
    CASE
        WHEN parent_org_type like 'SDP%' OR coalesce(nbs.mpc_nm, circle_ref.mpc_nm) LIKE 'SDP%'
        THEN 'SDP'
        ELSE 'MPC'
    END AS flag,
    coalesce(nbs.pt_nm, circle_ref.pt_nm) dist_id,
    extract(day from date_add(date_add(parse_date('%Y%m%d', coalesce(kpi.month_id, nbs.month_id) || '01'), interval 1 month), interval -1 day)) day_no,
    max(format_date('%Y%m%d', kpi.dt)) asof_date,
    sum(coalesce(kpi.ga,0)) ga,
    round(sum(coalesce(kpi.net_sec_amt,0))/1000000, 3) net_secondary_mn
from
    nbs
    left join
    kpi
    on
        nbs.organization_id = kpi.organization_id
    and nbs.month_id=kpi.month_id
    left join
    circle_ref
    on
        nbs.site_id = circle_ref.site_id
where
    kpi.dt < date_trunc(date(${mtd_dt}), month)
group by
    1,4,5,6,7,8,9