select
	snp_dt_sk_id::varchar dt_id,
	'3ID' brand,
    a.qr_code organization_id,
	e.circle,
	e.region_circle region,
    kpi_value saldo_amount
from
    marketing.trd_trisakti_movement_detail a
left join
	(
		select	ret_qr_cd qr_code
				,distance
				,ret_msisdn
				,dt
				,site_id
				,row_number() over (partition by ret_qr_cd order by dt desc) rn
		from	mis.stg_v_map_tgt_ret_details_hist
		where	site_id!=''
		    and dt >= ${start_dt_int}
		    and dt <= ${end_dt_int}
	) d
	on a.qr_code = d.qr_code
	and d.rn=1
left join
	ioh_biadm.ref_site_h3i e
	on
	    d.site_id = e.site_id
where
    type='Retailer'
and kpi_name ='DB Closing Balance'
and snp_dt_sk_id >= ${start_dt_int}
and snp_dt_sk_id <= ${end_dt_int}