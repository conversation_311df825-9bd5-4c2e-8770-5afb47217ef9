package config

type TunnelConfig struct {
	Ssh         SSHConfig `yaml:"ssh" mapstructure:"ssh"`
	LocalPort   int       `yaml:"local_port" mapstructure:"local_port"`
	Destination string    `yaml:"destination" mapstructure:"destination"`
}

func (tc TunnelConfig) ToMap() map[string]any {
	var cfMap = make(map[string]any)
	cfMap["ssh"] = tc.Ssh.ToMap()
	cfMap["local_port"] = tc.LocalPort
	cfMap["destination"] = tc.Destination
	return cfMap
}

type SSHConfig struct {
	Host           string `yaml:"host"`
	Port           int    `yaml:"port"`
	User           string `yaml:"user"`
	PrivateKeyFile string `yaml:"private_key_file" mapstructure:"private_key_file"`
}

func (sc SSHConfig) ToMap() map[string]any {
	var cfMap = make(map[string]any)
	cfMap["host"] = sc.Host
	cfMap["port"] = sc.Port
	cfMap["user"] = sc.User
	cfMap["private_key_file"] = sc.PrivateKeyFile
	return cfMap
}
