package enc

import (
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/likearthian/go-crypto"
)

func Encrypt(str string) (string, error) {
	rr := rand.New(rand.NewSource(time.Now().UnixNano()))
	kid := rr.Intn(len(encKeys) - 1)

	buf, err := crypto.EncryptWithGCM([]byte(str), []byte(encKeys[kid]))
	if err != nil {
		return "", err
	}

	payload := fmt.Sprintf("enc/%d:%s", kid, crypto.EncodeBASE64(buf))
	return crypto.EncodeBASE64URL([]byte(payload)), nil
}

func Decrypt(str string) (string, error) {
	buf, err := crypto.DecodeBASE64URL(str)
	if err != nil {
		return "", err
	}

	arr := strings.Split(string(buf), ":")
	if len(arr) < 2 {
		return "", fmt.<PERSON>("invalid encrypted payload")
	}

	if !strings.HasPrefix(arr[0], "enc/") {
		return "", fmt.Errorf("invalid encrypted payload")
	}

	kid, err := strconv.ParseInt(arr[0][4:], 10, 64)
	if err != nil {
		return "", fmt.Errorf("invalid encrypted payload")
	}

	if kid > int64(len(encKeys)-1) {
		return "", fmt.Errorf("invalid encrypted payload")
	}

	if int(kid) > len(encKeys)-1 {
		return "", fmt.Errorf("invalid encrypted payload")
	}

	key := encKeys[int(kid)]

	buf, err = crypto.DecodeBASE64(arr[1])
	if err != nil {
		return "", fmt.Errorf("invalid encrypted payload")
	}

	res, err := crypto.DecryptWithGCM(buf, []byte(key))
	if err != nil {
		return "", err
	}

	return string(res), nil
}
