package dto

import (
	"github.com/likearthian/types"
	"gopkg.in/guregu/null.v4"
)

type RunProcessRequestDTO struct {
	ID              string              `json:"id" query:"id"`
	WorkDate        null.Int            `json:"work_date" query:"work_date"`
	WithEmail       null.Bool           `json:"with_email" query:"with_email"`
	EmailRecipients utype.SliceOfString `json:"email_recipients" query:"email_recipients"`
}
