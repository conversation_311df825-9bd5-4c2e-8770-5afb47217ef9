/*
Copyright © 2024 <NAME_EMAIL>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package cmd

import (
	"bytes"
	"fmt"
	"io"
	"os"

	apexLog "github.com/apex/log"
	apexcli "github.com/apex/log/handlers/cli"
	"github.com/apex/log/handlers/text"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"

	cfg "github.com/csee-pm/etl/3idSitePerf/config"
	"github.com/csee-pm/etl/3idSitePerf/process"
	etlCmd "github.com/csee-pm/etl/shared/cmd"
	etlConfig "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	log "github.com/csee-pm/etl/shared/logger"
	"github.com/csee-pm/etl/shared/notify"
)

var logBuffer *bytes.Buffer

func InitCommand() *cobra.Command {
	// workDate := time.Now().AddDate(0, 0, -2)
	// startDt, _ := strconv.Atoi(workDate.AddDate(0, 0, -30).Format("20060102"))
	// endDt, _ := strconv.Atoi(workDate.AddDate(0, 0, 1).Format("20060102"))

	v := viper.New()
	// v.SetDefault("etl_config.start_dt", startDt)
	// v.SetDefault("etl_config.end_dt", endDt)

	logBuffer = new(bytes.Buffer)
	logWriter := io.MultiWriter(os.Stdout, logBuffer)
	alog := &apexLog.Logger{}
	alog.Handler = apexcli.New(logWriter)
	alog.Handler = text.New(logWriter)

	logger := log.NewApexLogger(alog)

	root := etlCmd.CreateRootCmd(
		"3idSitePerf",
		v,
		logger,
		cfg.SetNewConfig,
		etlCmd.WithAction(rootAction),
		etlCmd.WithDescription("ETL job for getting 3id site performance into hadoop"),
	)

	return root
}

func rootAction(cmd *cobra.Command, args []string) {
	conf, ok := cmd.Context().Value(ctx.ContextKeyConfig).(*viper.Viper)
	if !ok {
		cmd.PrintErrln("failed to get cmd object from context")
	}

	logger := ctx.ExtractLogger(cmd.Context())

	var notifier notify.Notifier

	if conf.Get("notifier") != nil {
		if conf.Get("notifier.email") != nil {
			notifyConf, err := etlConfig.GetEmailConfig(conf, "notifier.email")
			if err != nil {
				logger.Error(err.Error())
			}

			notifier = notify.NewEmailNotifier(
				notifyConf.Host,
				notifyConf.Port,
				notifyConf.User,
				notifyConf.Pass,
				notifyConf.SenderAddress,
				notifyConf.SenderName,
				notifyConf.Receiver...)
		}
	}

	// fmt.Printf("config: %+v\n", cfg.AllSettings())
	msg := "ETL process finished successfully."
	subject := "3ID Site Perf ETL Process Success"
	if err := process.RunETLProcess(cmd.Context()); err != nil {
		subject = "3ID Site Perf ETL Process Failed"
		msg = fmt.Sprintf("ETL process failed. %s", err)
		logger.Error(err.Error())
	}

	logger.Info("ETL process finished")
	msg = fmt.Sprintf("%s\n\n%s", msg, logBuffer.String())
	if err := sendNotif(notifier, subject, msg); err != nil {
		logger.Error(err.Error())
	}

	// fmt.Printf("config: %+v\n", conf.AllSettings())
}

func sendNotif(nt notify.Notifier, subject string, message string, attachments ...notify.FileAttachment) error {
	if nt != nil {
		return nt.Notify(subject, notify.TextToHTMLString(message), attachments...)
	}

	return nil
}
