with mpr_kpi as (
    select
        (periode)::varchar dt_id,
        site_id_90 site_id,
        sum(case when kpi_tag = 'GA' then value_1 else 0 end) ga_mtd,
        sum(case when kpi_tag = 'QSC' then value_1 else 0 end) qsc_mtd,
        sum(case when kpi_tag = 'RGU30' then value_1 else 0 end) rgu30_mtd,
        sum(case when kpi_tag = 'RGU90' then value_1 else 0 end) rgu90_mtd,
        sum(case when kpi_tag = 'VLR DAILY' then value_1 else 0 end) vlr_mtd
    from
        analytics.mkt_mpr_performance_critical_kpi
    where
        cast(periode/100 as varchar) >= ${start_month_id}
    and cast(periode/100 as varchar) <= ${end_month_id}
    and flag = 'MTD'
    group by 1,2
),
otl_kpi as (
    select
        (dt_id)::varchar dt_id,
        a.site_id,
        sum(case when kpi_name = 'q_sso' then kpi_value else 0 end) q_sso_mtd,
        sum(case when kpi_name = 'q_uro' then kpi_value else 0 end) q_uro_mtd,
        sum(case when kpi_name = 'secondary' then kpi_value else 0 end) secondary,
        sum(case when kpi_name = 'sso' then kpi_value else 0 end) sso_mtd,
        sum(case when kpi_name = 'uro' then kpi_value else 0 end) uro_mtd
    from
        ioh_biadm.omn_sitewise_smy a
    where
        (a.dt_id/100)::varchar >= ${start_month_id}
    and (a.dt_id/100)::varchar <= ${end_month_id}
    and kpi_name in ('q_sso','q_uro','secondary','sso','uro')
    group by 1,2
),
base_mth as (
    select
        a.dt_id,
        a.site_id,
        a.ga_mtd,
        a.qsc_mtd,
        a.rgu30_mtd,
        a.rgu90_mtd,
        a.vlr_mtd,
        b.q_sso_mtd,
        b.q_uro_mtd,
        b.secondary,
        b.sso_mtd,
        b.uro_mtd
    from
        mpr_kpi a
        left join
        otl_kpi b
        on
            a.dt_id = b.dt_id
        and a.site_id = b.site_id
)
select * from base_mth