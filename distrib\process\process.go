package process

import (
	"bytes"
	"context"
	"embed"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/csee-pm/etl/shared/channel"
	"github.com/xuri/excelize/v2"

	cfg "github.com/csee-pm/etl/distrib/config"
	etlCfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlProc "github.com/csee-pm/etl/shared/process"
)

//go:embed all:files
var procFS embed.FS

func RunETL(c context.Context) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)
	tunnel := conf.Get("tunnel")

	var useTunnel = false
	if tunnel != nil && cfg.UseDistribFromFile == "" {
		useTunnel = true
	}

	if useTunnel {
		tunConfig, err := etlCfg.GetTunnelConfig(conf, "tunnel")
		if err != nil {
			return fmt.Errorf("failed to get tunnel config. %s", err)
		}

		if err := etlProc.StartTunnel(tunConfig, logger); err != nil {
			return fmt.Errorf("failed to start tunnel. %s", err)
		}
	}

	workDir := conf.GetString("work_dir")
	if workDir == "" {
		workDir = "workdir"
	}
	exepath := ctx.ExtractRootDir(c)
	workDir = filepath.Join(exepath, workDir)
	conf.Set("work_dir", workDir)

	if err := os.MkdirAll(workDir, os.ModePerm); err != nil {
		return fmt.Errorf("failed to create work dir. %s", err)
	}

	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	dst := NewDistribProcess()

	var distribReport *DistribReport
	var err error = nil

	f, err := procFS.Open("files/Distribution Tracker_LMTDvsMTD.xlsx")
	if err != nil {
		return fmt.Errorf("failed to open file. %s", err)
	}
	defer f.Close()

	wg.Add(1)
	distribResult := channel.RunAsyncContext(cCancel, func() (*DistribReport, error) {
		return dst.RunDistrib(cCancel)
	})

	go func() {
		defer wg.Done()
		for res := range distribResult {
			res.Map(func(report *DistribReport) {
				// do something with the report
				distribReport = report
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get Distrib data. %s", er)
				logger.Debug("calling cancel", "caller", "Distrib Main Async Process")
				cancel()
			})
		}
	}()

	wg.Wait()

	if err != nil {
		return err
	}

	xl, err := excelize.OpenReader(f)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}

	if err := dst.WriteReport(cCancel, xl, distribReport); err != nil {
		return fmt.Errorf("failed to write report. %s", err)
	}

	reportFile := fmt.Sprintf("%s/Distribution Tracker_LMTDvsMTD_%s.xlsx", workDir, time.Now().Format("20060102_150405"))
	if err := xl.SaveAs(reportFile); err != nil {
		return fmt.Errorf("failed to save report. %s", err)
	}

	buf, err := xl.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write report to buffer. %s", err)
	}

	if !cfg.UseNoMailer {
		if err := SendReportEmail(cCancel, Attachment{FileName: reportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			return fmt.Errorf("failed to send report email. %s", err)
		}
	}

	// buf, err := json.MarshalIndent(distribReport, "", "  ")
	// if err != nil {
	// 	return err
	// }

	// fmt.Println(string(buf))
	return nil
}
