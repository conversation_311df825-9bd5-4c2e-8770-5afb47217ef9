/*
Copyright © 2024 <NAME_EMAIL>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package cmd

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"

	cfg "github.com/csee-pm/etl/gpfat/config"
	"github.com/csee-pm/etl/gpfat/process"
	etlConfig "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etldb "github.com/csee-pm/etl/shared/db"
	log "github.com/csee-pm/etl/shared/logger"
)

var cfgFile string
var configSets []string

var sqlString string
var sqlFilePath string
var outputFilePath string

var noHeader bool = false

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "gpfat",
	Short: "tools to query 3id gpfat data",
	Run:   rootAction,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute(c context.Context) {
	err := rootCmd.ExecuteContext(c)
	if err != nil {
		os.Exit(1)
	}
}

func InitCommand(v *viper.Viper, logger log.Logger) *cobra.Command {
	// cobra.OnInitialize(createInitConfig(v, logger), createInitLogger(v, logger))

	rootCmd.AddCommand(initConfigSetCmd(v))
	rootCmd.AddCommand(initConfigGetCmd(v))

	// rootCmd.AddCommand(cmd.InitEncryptCmd(v))

	// Here you will define your flags and configuration settings.
	// Cobra supports persistent flags, which, if defined here,
	// will be global for your application.

	rootCmd.PersistentFlags().StringArrayVar(&configSets, "set", nil, "set config")
	rootCmd.Flags().StringVarP(&sqlFilePath, "sql-file", "f", "", "sql file path")
	rootCmd.Flags().StringVarP(&sqlString, "sql", "s", "", "sql string")

	rootCmd.Flags().StringVarP(&outputFilePath, "output", "o", "", "output file path")

	rootCmd.Flags().BoolVarP(&noHeader, "no-header", "n", false, "no header")

	// Cobra also supports local flags, which will only run
	// when this action is called directly.
	rootCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")

	return rootCmd
}

// initConfig reads in cmd file and ENV variables if set.
func createInitConfig(v *viper.Viper, logger log.Logger) func() {
	cfgFile = etlConfig.GetConfigFilePath(".gpfat.yaml")

	if _, err := os.Stat(cfgFile); errors.Is(err, os.ErrNotExist) {
		logger.Info("config not found")
		logger.Info("creating new config")
		fmt.Println("Please input your configuration")

		cf, err := cfg.PromptNewConfig(v)
		if err != nil {
			logger.Error(err.Error())
			os.Exit(1)
		}

		if err := etlConfig.WriteConfig(cf, cfgFile); err != nil {
			logger.Error(err.Error())
			os.Exit(1)
		}
	}

	return func() {
		// Find home process.
		home, err := os.UserHomeDir()
		cobra.CheckErr(err)

		// Search cmd in home process with name ".gpfat" (without extension).
		v.AddConfigPath(home)
		v.SetConfigType("yaml")
		v.SetConfigName(".gpfat")

		v.AutomaticEnv() // read in environment variables that match

		// If a config file is found, read it in.
		if err := v.ReadInConfig(); err == nil {
			logger.Debug(fmt.Sprintf("Using config file: %s", v.ConfigFileUsed()))
		} else {
			// logger.Error(fmt.Sprintf("failed to read configFile. %s", err))
			cobra.CheckErr(err)
		}
	}
}

func createInitLogger(v *viper.Viper, logger log.Logger) func() {
	return func() {
		level := v.GetString("logLevel")
		var logLevel log.Level = log.InfoLevel
		switch level {
		case "0", "debug":
			logLevel = log.DebugLevel
		case "1", "info":
			logLevel = log.InfoLevel
		case "2", "warn":
			logLevel = log.WarnLevel
		case "3", "error":
			logLevel = log.ErrorLevel
		}

		logger.SetLevel(logLevel)
	}
}

func rootAction(cmd *cobra.Command, args []string) {
	beforeExec(cmd)
	cfg, ok := cmd.Context().Value(ctx.ContextKeyConfig).(*viper.Viper)
	if !ok {
		cmd.PrintErrln("failed to get cmd object from context")
	}
	logger := ctx.ExtractLogger(cmd.Context())

	if len(configSets) > 0 {
		ovrMap := etlConfig.ArraySetsToMap(configSets)
		if err := etlConfig.Override(cfg, ovrMap); err != nil {
			logger.Error(err.Error())
		}
	}

	strSql := sqlString
	if sqlFilePath != "" {
		buf, err := os.ReadFile(sqlFilePath)
		if err != nil {
			logger.Error(err.Error())
			return
		}
		strSql = string(buf)
	}

	if strSql == "" {
		logger.Error("sql string is empty")
		return
	}

	var qryArgs []any
	var err error
	strSql, qryArgs, err = etldb.ParseSql(strSql)
	if err != nil {
		logger.Error(err.Error())
		return
	}

	w := os.Stdout
	if outputFilePath != "" {
		w, err = os.Create(outputFilePath)
		if err != nil {
			logger.Error(err.Error())
			return
		}
	}
	defer w.Close()

	out := csv.NewWriter(w)
	defer out.Flush()

	if err := process.RunQuery(cmd.Context(), strSql, qryArgs, out, noHeader); err != nil {
		logger.Error(err.Error())
	}

}

func beforeExec(cmd *cobra.Command) {
	cfg, ok := cmd.Context().Value(ctx.ContextKeyConfig).(*viper.Viper)
	if !ok {
		cmd.PrintErrln("failed to get cmd object from context")
	}
	logger := ctx.ExtractLogger(cmd.Context())

	createInitConfig(cfg, logger)()
	createInitLogger(cfg, logger)()
}
