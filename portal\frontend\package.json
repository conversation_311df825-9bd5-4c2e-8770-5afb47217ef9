{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"clsx": "^2.1.1", "pinia": "^3.0.1", "tailwind-merge": "^3.3.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "npm-run-all2": "^7.0.2", "postcss": "^8.5.3", "shadcn-vue": "^2.1.0", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}