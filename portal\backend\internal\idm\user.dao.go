package idm

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"github.com/csee-pm/etl/portal/backend/internal/db"
	"github.com/csee-pm/etl/portal/backend/pkg/dto"
	"github.com/csee-pm/etl/portal/backend/pkg/errs"
	"github.com/jmoiron/sqlx"
	"gopkg.in/guregu/null.v4"
)

type User struct {
	Username       string      `db:"username" json:"username"`
	Fullname       string      `db:"fullname" json:"fullname"`
	IsAdmin        bool        `db:"is_admin" json:"is_admin"`
	Email          string      `db:"email" json:"email"`
	HashedPassword null.String `db:"password" json:"-"`
	IsLdap         bool        `db:"is_ldap" json:"is_ldap"`
}

type UserDAO struct {
	db    *sqlx.DB
	model db.DBModel
}

func NewUserDAO(client *sqlx.DB) (*UserDAO, error) {
	model := db.CreateGenericModel[User]("user", "")
	userDAO := &UserDAO{db: client, model: model}

	return userDAO, userDAO.Init()
}

func (dao *UserDAO) Init() error {
	_, err := dao.db.Exec(userTableDdl)
	if err != nil {
		return err
	}

	admin, err := dao.GetUser(defaultAdmin.Username)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return fmt.Errorf("failed to check default admin. %s", err)
	}

	if admin == nil {
		if err := dao.CreateUser(defaultAdmin); err != nil {
			return fmt.Errorf("failed to create default admin. %s", err)
		}
	}

	return nil
}

func (dao *UserDAO) GetUser(username string) (*User, error) {
	query := fmt.Sprintf("SELECT %s FROM %s WHERE username = ?", strings.Join(dao.model.Columns(), ","), dao.model.FullTableName())
	var user User
	err := dao.db.Get(&user, query, username)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (dao *UserDAO) GetUsers(req dto.GetUsersRequestDTO) ([]User, error) {
	query := fmt.Sprintf("SELECT %s FROM %s", strings.Join(dao.model.Columns(), ","), dao.model.FullTableName())

	var usernames any
	switch {
	case len(req.Username) == 0:
		return dao.GetAllUsers()
	case len(req.Username) == 1:
		query += " WHERE username = ?"
		usernames = req.Username[0]
	default:
		query += " WHERE username IN (?)"
		usernames = req.Username
	}

	qry, args, err := sqlx.In(query, usernames)
	if err != nil {
		return nil, err
	}

	var users []User
	err = dao.db.Select(&users, qry, args...)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errs.ErrNoRows
		}
		return nil, err
	}
	return users, nil
}

func (dao *UserDAO) GetAllUsers() ([]User, error) {
	query := fmt.Sprintf("SELECT %s FROM %s", strings.Join(dao.model.Columns(), ","), dao.model.FullTableName())
	var users []User
	err := dao.db.Select(&users, query)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (dao *UserDAO) CreateUser(user *User) error {
	existingUser, err := dao.GetUser(user.Username)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return fmt.Errorf("failed to check user db. %s", err)
	}

	if existingUser != nil {
		return fmt.Errorf("user already exists")
	}

	if user.IsLdap {
		user.HashedPassword = null.NewString("", false)
	}

	qry, args, err := db.GenerateInsertQuery(dao.model, user)
	if err != nil {
		return err
	}

	_, err = dao.db.Exec(qry, args...)
	return err
}

var defaultAdmin = &User{
	Username: "80208379",
	Fullname: "Ziska Zarkasyi",
	IsAdmin:  true,
	Email:    "<EMAIL>",
	IsLdap:   true,
}

var userTableDdl = `
CREATE TABLE IF NOT EXISTS user (
	username TEXT PRIMARY KEY,
	fullname TEXT NOT NULL,
	is_admin BOOLEAN NOT NULL,
	email TEXT NOT NULL,
	password TEXT,
	is_ldap BOOLEAN NOT NULL
)
`
