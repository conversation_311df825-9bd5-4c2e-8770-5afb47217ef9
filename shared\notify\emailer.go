package notify

import (
	"bytes"
	"fmt"
	"io"

	"crypto/tls"
	"gopkg.in/gomail.v2"
)

type emailNotifier struct {
	host          string
	port          int
	user          string
	pass          string
	senderName    string
	senderAddress string
	receiver      []string
}

func NewEmailNotifier(host string, port int, user, password string, senderAddress, senderName string, receiverAddress ...string) Notifier {
	return emailNotifier{
		host:          host,
		port:          port,
		user:          user,
		pass:          password,
		senderAddress: senderAddress,
		senderName:    senderName,
		receiver:      receiverAddress,
	}
}

func (e emailNotifier) Notify(title, htmlMsg string, attachments ...FileAttachment) error {
	message := gomail.NewMessage()

	message.SetHeader("From", message.FormatAddress(e.senderAddress, e.senderName))
	message.SetHeader("To", e.receiver...)
	message.SetHeader("Subject", title)
	message.SetBody("text/html", string(htmlMsg))
	for _, at := range attachments {
		message.Attach(at.FileName, gomail.SetCopyFunc(createCopyFuncFromStream(at.Content)))
	}

	dl := gomail.NewDialer(e.host, e.port, e.user, e.pass)
	dl.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	return dl.DialAndSend(message)
}

func createCopyFuncFromBuffer(buf []byte) func(w io.Writer) error {
	return func(w io.Writer) error {
		b := bytes.NewReader(buf)
		if _, err := io.Copy(w, b); err != nil {
			return err
		}

		return nil
	}
}

func createCopyFuncFromStream(rd io.Reader) func(w io.Writer) error {
	return func(w io.Writer) error {
		n, err := io.Copy(w, rd)
		if err != nil {
			fmt.Printf("error when copying attachment. %s\n", err.Error())
			return err
		}

		fmt.Printf("copying content to mail attachment. written %d bytes\n", n)

		return nil
	}
}
