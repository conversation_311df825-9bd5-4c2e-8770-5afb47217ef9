package main

import (
	"bytes"
	"context"
	"fmt"
	"os"

	"github.com/csee-pm/etl/gcpsync/cmd"
	cfg "github.com/csee-pm/etl/gcpsync/config"
	etlcmd "github.com/csee-pm/etl/shared/cmd"
	etlcfg "github.com/csee-pm/etl/shared/config"
	ctxpkg "github.com/csee-pm/etl/shared/context"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/spf13/cobra"
)

var logBuffer *bytes.Buffer
var executedCmd *cobra.Command

func main() {
	logBuffer = new(bytes.Buffer)

	root := CreateRootCmd()

	err := root.Execute()
	process := ""
	if executedCmd != nil {
		process = executedCmd.Name()
	}

	err = etlProc.NotifyProcessFinished(root.Context(), process, logBuffer.String(), err)
	if err != nil {
		fmt.Println(err.Error())
		os.Exit(1)
	}
}

func CreateRootCmd() *cobra.Command {
	root := etlcmd.InitCommonRootCommand(
		"gcpsync",
		cfg.SetNewConfig,
		etlcmd.WithDescription("ETL job for loading data to BigQuery"),
		etlcmd.WithLogBuffer(logBuffer),
	)

	root.PersistentFlags().BoolVar(&cfg.NotInteractive, "not-interactive", false, "Do not prompt for configuration")

	root.PreRunE = createPrerun(func() bool {
		return true
	})

	gaProdCmd := CreateGaProdCmd()
	root.AddCommand(gaProdCmd)

	return root
}

func CreateGaProdCmd() *cobra.Command {
	pstCmd := cmd.InitGaProdCmd()
	pstCmd.PreRunE = createPrerun(func() bool {
		return false
	})
	return pstCmd
}

func createPrerun(useTunnelCheckFn func() bool) func(*cobra.Command, []string) error {
	return func(cmd *cobra.Command, args []string) error {
		executedCmd = cmd
		conf := ctxpkg.ExtractConfig(cmd.Context())
		logger := ctxpkg.ExtractLogger(cmd.Context())
		tunnel := conf.Get("tunnel")
		var useTunnel = false
		if tunnel != nil {
			useTunnel = true
			if useTunnelCheckFn != nil {
				useTunnel = useTunnelCheckFn()
			}
		}

		if useTunnel {
			tunConfig, err := etlcfg.GetTunnelConfig(conf, "tunnel")
			if err != nil {
				return fmt.Errorf("failed to get tunnel config. %s", err)
			}

			if err := etlProc.StartTunnel(tunConfig, logger); err != nil {
				return fmt.Errorf("failed to start tunnel. %s", err)
			}
		}

		workDir := conf.GetString("work_dir")
		if workDir == "" {
			workDir = "workdir"
		}

		exepath := ctxpkg.ExtractRootDir(cmd.Context())
		workDir = exepath + "/" + workDir
		conf.Set("work_dir", workDir)
		c := context.WithValue(cmd.Context(), ctxpkg.ContextKeyWorkDir, workDir)
		cmd.SetContext(c)

		if err := os.MkdirAll(workDir, os.ModePerm); err != nil {
			return fmt.Errorf("failed to create work dir. %s", err)
		}

		return nil
	}
}
