package register

import (
	"context"
)

type JobService interface {
	GetEtlByID(c context.Context, id string) (*EtlProcess, error)
	GetEtl(c context.Context, req GetEtlProcessRequestDTO) ([]EtlProcess, error)
	GetJobByID(c context.Context, id string) (*JobRegister, error)
	GetJob(c context.Context, req GetJobRequestDTO) ([]JobRegister, error)
	RegisterJobStart(c context.Context, req PostRegisterJobStartRequestDTO) (PostRegisterJobStartResponseDTO, error)
	RegisterJobEnd(c context.Context, id string) (any, error)
	UpdateJobStatus(c context.Context, req UpdateJobStatusRequestDTO) (any, error)
}
