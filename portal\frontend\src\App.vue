<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
</script>

<template>
  <div class="min-h-screen bg-gray-100">
    <header class="bg-white shadow-md">
      <div class="container mx-auto px-4 py-4">
        <div class="flex justify-between items-center">
          <h1 class="text-2xl font-bold text-gray-800">ETL Portal</h1>
          <nav class="space-x-4">
            <RouterLink to="/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100">Home</RouterLink>
            <RouterLink to="/about" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md hover:bg-gray-100">About</RouterLink>
          </nav>
        </div>
      </div>
    </header>

    <main class="container mx-auto py-6 px-4">
      <RouterView />
    </main>

    <footer class="bg-white shadow-md mt-8 py-4">
      <div class="container mx-auto px-4 text-center text-gray-500">
        &copy; 2025 ETL Portal
      </div>
    </footer>
  </div>
</template>

