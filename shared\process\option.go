package process

import (
	"bytes"
	"time"

	log "github.com/csee-pm/etl/shared/logger"
)

type processOptions struct {
	WorkDate        time.Time
	EmailRecipients []string
	SendEmail       bool
	Logger          log.Logger
	LogBuffer       *bytes.Buffer
}

type ProcessOption func(o *processOptions)

func WithWorkDate(workDate time.Time) ProcessOption {
	return func(o *processOptions) {
		o.WorkDate = workDate
	}
}

func WithEmailRecipients(emailRecipients []string) ProcessOption {
	return func(o *processOptions) {
		o.EmailRecipients = emailRecipients
	}
}

func WithSkipEmail(skip bool) ProcessOption {
	return func(o *processOptions) {
		o.SendEmail = !skip
	}
}

func WithLogger(logger log.Logger) ProcessOption {
	return func(o *processOptions) {
		o.Logger = logger
	}
}

func WithLogBuffer(buf *bytes.Buffer) ProcessOption {
	return func(o *processOptions) {
		o.Log<PERSON><PERSON> = buf
	}
}

func NewDefaultProcessOption() *processOptions {
	return &processOptions{
		WorkDate:        time.Now().AddDate(0, 0, -3),
		EmailRecipients: []string{},
		SendEmail:       true,
	}
}
