package process

// import (
// 	"context"
// 	"fmt"
// 	"time"

// 	cfg "github.com/csee-pm/etl/bpcwn/config"
// 	ctx "github.com/csee-pm/etl/shared/context"
// )

// func (p salmoboProcess) RunSalmoTracker(c context.Context) (*SalmoTrackerReport, error) {
// 	conf := ctx.ExtractConfig(c)
// 	// logger := ctx.ExtractLogger(c)

// 	workDate := time.Now().AddDate(0, 0, -2)
// 	lmonth := workDate.AddDate(0, -1, 0)
// 	startMonth := lmonth.AddDate(0, -2, 0)
// 	startDt := time.Date(startMonth.Year(), startMonth.Month(), 1, 0, 0, 0, 0, time.Local)
// 	endDt := workDate

// 	fromFile := cfg.UseSalmoTrackerFromFile

// 	var err error
// 	if conf.Get("etl.work_date") != nil {
// 		workDate, err = time.Parse("20060102", conf.GetString("etl.work_date"))
// 		if err != nil {
// 			return nil, fmt.Errorf("failed to parse work_date. %s", err)
// 		}
// 		lmonth = workDate.AddDate(0, -1, 0)
// 		startMonth = lmonth.AddDate(0, -2, 0)
// 		startDt = time.Date(startMonth.Year(), startMonth.Month(), 1, 0, 0, 0, 0, time.Local)
// 		endDt = workDate

// 	}

// 	fmt.Println(startDt, endDt, fromFile)

// 	return nil, nil
// }

// func (p salmoboProcess) getSalmoTrackerData(c context.Context, startDt time.Time, endDt time.Time) ([]SalmoTrackerData, error) {
// 	return nil, nil
// }
