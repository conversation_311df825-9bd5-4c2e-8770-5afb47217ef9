with site_ref as (
    select
        site_id,
        branch,
        circle,
        region_circle region,
        kabkot_nm,
        sales_area,
        cnt,
        row_number() over (partition by site_id order by cnt desc) rnk
    from
        (
            select
                site_id,
                gladiator_branch branch,
                sales_area,
                kabkot_nm,
                region_circle,
                circle,
                count(*) cnt
            from
                ioh_biadm.ref_site_h3i
            group by 1,2,3,4,5,6
        ) x
)
select
    (a.dt_id/100)::varchar month_id,
    case when dt_id = ${mtd_dt_int} then 'MTD' else 'LMTD' end period,
    a.dt_id::varchar asof_date,
    b.circle,
    b.region,
    b.kabkot_nm kabupaten,
    '3ID' as brand,
    cast(sum(case when kpi_name = 'rgu_ga' then kpi_value else 0 end) as int) ga,
    cast(sum(case when kpi_name = 'm2s_mtd' then kpi_value else 0 end) as int) m2s
from
    ioh_biadm.omn_sitewise_smy a
    left join
    site_ref b
    on
        a.site_id = b.site_id
    and b.rnk = 1
where
    kpi_name in ('rgu_ga', 'm2s_mtd')
and dt_id in (${mtd_dt_int},to_char(to_date(${mtd_dt_int}::varchar, 'yyyyMMdd') - interval '1 month', 'YYYYMMDD')::int)
group by 1,2,3,4,5,6

union all

select
    (a.dt_id/100)::varchar month_id,
    'FM' period,
    dt_id::varchar asof_date,
    b.circle,
    b.region,
    b.kabkot_nm kabupaten,
    '3ID' as brand,
    cast(sum(case when kpi_name = 'rgu_ga' then kpi_value else 0 end) as int) ga,
    cast(sum(case when kpi_name = 'm2s_mtd' then kpi_value else 0 end) as int) m2s
from
    ioh_biadm.omn_sitewise_smy a
    left join
    site_ref b
    on
        a.site_id = b.site_id
    and b.rnk = 1
where
    kpi_name in ('rgu_ga', 'm2s_mtd')
and dt_id >= to_char(to_date(${mtd_dt_int}::varchar, 'yyyyMMdd') - interval '6 month', 'yyyyMMdd')::int
and dt_id < to_char(date_trunc('month', to_date(${mtd_dt_int}::varchar, 'yyyyMMdd')), 'yyyyMMdd')::int
and dt_id = to_char(date_trunc('month', to_date(dt_id::varchar, 'yyyyMMdd')) + interval '1 month' - interval '1 day', 'yyyyMMdd')::int
group by 1,2,3,4,5,6