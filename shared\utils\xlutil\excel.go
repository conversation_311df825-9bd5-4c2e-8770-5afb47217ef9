package xlutil

import (
	"fmt"
	"strings"
)

type CellStruct struct {
	Row int
	Col int
}

func Cell(row int, col int) CellStruct {
	return CellStruct{Row: row, Col: col}
}

func (c CellStruct) Address() string {
	return fmt.Sprintf("%s%d", ColNumToColName(c.Col), c.Row)
}

type RangeStruct struct {
	StartCell CellStruct
	EndCell   CellStruct
}

func Range(startCell CellStruct, endCell CellStruct) RangeStruct {
	return RangeStruct{StartCell: startCell, EndCell: endCell}
}

func (r RangeStruct) Address() string {
	return fmt.Sprintf("%s%d:%s%d", ColNumToColName(r.StartCell.Col), r.StartCell.Row, ColNumToColName(r.EndCell.Col), r.EndCell.Row)
}

func ColNumToColName(colNum int) string {
	if colNum < 1 || colNum > 16384 {
		return "" // Invalid input
	}

	colName := ""
	for colNum > 0 {
		colNum--
		remainder := colNum % 26
		char := rune('A' + remainder)
		colName = string(char) + colName
		colNum = colNum / 26
	}
	return colName
}

func StringToCell(cellstr string) CellStruct {
	colName := ""
	rowNum := 0
	for _, char := range cellstr {
		if char >= 'A' && char <= 'Z' {
			colName += string(char)
		} else {
			rowNum = rowNum*10 + int(char-'0')
		}
	}

	colNum := 0
	for i, char := range colName {
		colNum += (int(char-'A') + 1) * intPow(26, len(colName)-i-1)
	}

	return CellStruct{Row: rowNum, Col: colNum}
}

func intPow(base, exponent int) int {
	result := 1
	for i := 0; i < exponent; i++ {
		result *= base
	}
	return result
}

func StringToRange(rangestr string) (RangeStruct, error) {
	parts := strings.Split(rangestr, ":")
	if len(parts) != 2 {
		return RangeStruct{}, fmt.Errorf("invalid range string")
	}

	startCell := StringToCell(parts[0])
	endCell := StringToCell(parts[1])

	return RangeStruct{StartCell: startCell, EndCell: endCell}, nil
}
