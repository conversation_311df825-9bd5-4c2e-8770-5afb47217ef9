package utils

// create a generic function to get keys of a map
func GetMapKeys[K comparable, V any](m map[K]V) []K {
	keys := make([]K, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// create a generic function to get values of a map
func GetMapValues[K comparable, V any](m map[K]V) []V {
	values := make([]V, 0, len(m))
	for _, v := range m {
		values = append(values, v)
	}
	return values
}

func MapProcessor[K comparable, V any](m map[K]V, fn func(key K, value V)) {
	for key, value := range m {
		fn(key, value)
	}
}

func MapToList[K comparable, V any, O any](m map[K]V, fn func(K, V) O) []O {
	list := make([]O, 0, len(m))
	for key, value := range m {
		list = append(list, fn(key, value))
	}
	return list
}

func MapToMap[K comparable, V any, OK comparable, OV any](m map[K]V, fn func(K, V) (OK, OV)) map[OK]OV {
	newMap := make(map[OK]OV)
	for key, value := range m {
		newKey, newValue := fn(key, value)
		newMap[newKey] = newValue
	}
	return newMap
}
