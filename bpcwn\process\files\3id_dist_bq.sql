with quro as (
    SELECT
        date(load_dt_sk_id) dt,
		case
		    when format_timestamp('%Y%m', load_dt_sk_id) = format_date('%Y%m', date(${mtd_dt})) then 'MTD'
			ELSE 'LMTD'
		end mthf,
        a.site_id,
        c.circle,
        c.region_circle region,
        CASE
           WHEN kpi_code = 'qsso_any' THEN 'qsso'
           WHEN kpi_code = 'quro_any' THEN 'quro'
           ELSE kpi_code
        END AS kpi_code,
        SUM(value) AS value
    FROM
        `data-dtptechm-prd-c7ca.mis.project_ioh_kpi_daily_tracker_site` a
        LEFT OUTER JOIN
        `data-dtptechm-prd-c7ca.ioh_biadm.ref_site_h3i` c
        ON
            CASE WHEN LENGTH(a.site_id) <= 5 THEN LPAD(a.site_id, 6, '0')
                       ELSE a.site_id
                  END = CASE WHEN LENGTH(c.site_id) <= 5 THEN LPAD(c.site_id, 6, '0')
                             ELSE c.site_id
                        END
    WHERE
        kpi_code IN ('qsso_any','quro_any')
    and load_dt_sk_id >= timestamp(date_trunc(date_add(date(${mtd_dt}), interval -1 month), month))
   	and extract(day from load_dt_sk_id) = extract(day from date(${mtd_dt}))
    GROUP BY
        1, 2, 3, 4, 5,6
),
dse_outlet_map as
(
    select distinct
        upper(a.circle) circle,
		case
		    when a.region = 'HOR_MALUKU PAPUA' then 'MAPA'
    		else replace(replace(region,'HOR ',''),'ERN','')
		end region,
        retailer_qrcode,
        se_partnerid dse_id,
    	cast(mth_id as string) mth
    from
        `data-nationalslsdist-prd-986g.datamart.snd_outlet_mapping` a
    where
        a.mth_id >= cast(format_date('%Y%m', date_add(date(${mtd_dt}), interval -1 month)) as int)
    and upper(se_category) like '%KIOSK%'
),
dse_map AS
(
    select distinct
        dse_id
        ,circle
        ,region
        ,mth
    from
        dse_outlet_map a
),
pt_kec_smy as
(
	select
	    a.partner_id pt_id
        ,a.circle
        ,a.hor region
        ,case
            when mth = safe_cast(format_date('%Y%m', date(${mtd_dt})) as int) then 'MTD'
            else 'LMTD'
        end mthf
        ,sum(case when a.kpi_name='RGUGA-Trad' then a.kpi_value else null end) rgu_ga
        ,sum(case when a.kpi_name='secondary' then a.kpi_value else null end) secondary
	from
	    `data-nationalslsdist-prd-986g.datamart.and_snd_kecamatan_summary_dly` a
	where
	    a.dt_id >= cast(format_date('%Y%m%d', date_trunc(date_add(date(${mtd_dt}), interval -1 month), month)) as int)
	and a.dt_id <= cast(format_date('%Y%m%d', date(${mtd_dt})) as int)
	and a.dt_id <= (mth * 100) + extract(day from date(${mtd_dt}))
	and a.partner_type = '3KIOSK'
	group by
	    1,2,3,4
),
site_ref as (
    select distinct
        site_id
    from
        `data-nationalslsdist-prd-986g.datamart.nshim3_tbl_ref_addrsite_gtm_mth` a
    where
        a.mth >= format_date('%Y%m', date_add(date(${mtd_dt}), interval -3 month))
    and trim(addressable_type) = 'ADDRESSABLE SITE'
    and list_3id = 'Y'
)
select
    date(${mtd_dt}) dt,
    '3ID' brand,
    s.circle,
    s.region_circle region,
    'Site w/ <1 GAD/Day' parameter,
    count(distinct case when a.mthf = 'LMTD' and (coalesce(a.rgu_ga,0) / extract(day from date(${mtd_dt}))) < 1 then a.site_id else null end) lmtd,
    count(distinct case when a.mthf = 'MTD' and (coalesce(a.rgu_ga,0) / extract(day from date(${mtd_dt}))) < 1 then a.site_id else null end) mtd
from
    site_ref
    inner join
    (
        select
            date(load_dt_sk_id) dt,
            case
                when format_timestamp('%Y%m', load_dt_sk_id) = format_date('%Y%m', date(${mtd_dt})) then 'MTD'
                else 'LMTD'
            end mthf,
            a.site_id,
            sum(case when kpi_code = 'trad_ga_cnt' then value else null end) rgu_ga
        from
            `data-dtptechm-prd-c7ca.mis.project_ioh_kpi_daily_tracker_site` a
        where
            kpi_code = 'trad_ga_cnt'
        and load_dt_sk_id >= timestamp(date_trunc(date_add(date(${mtd_dt}), interval -1 month), month))
        and load_dt_sk_id <= ${mtd_dt}
        and extract(day from load_dt_sk_id) = extract(day from date(${mtd_dt}))
        group by
            1, 2, 3
    ) a
    on
        site_ref.site_id = a.site_id
    left join
    `data-bi-prd-935c.bi_dm.ref_site_h3i` s
    on
        a.site_id = s.site_id
group by
    1, 3, 4

union all
--DSE WITH <12 GAD/DAY
select
    date(${mtd_dt}) dt,
    '3ID' brand,