package process

import (
	"context"
	"encoding/csv"
	"fmt"
	cfg "github.com/csee-pm/etl/baudeck/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
	"io"
	"io/fs"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"
)

type CwnProcess struct {
	procFS fs.ReadFileFS
}

func NewCwnProcess(procFS fs.ReadFileFS) *CwnProcess {
	return &CwnProcess{
		procFS: procFS,
	}
}

func (cw CwnProcess) GetReportData(c context.Context, workDate time.Time) (CwnReport, error) {
	conf := ctx.ExtractConfig(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return CwnReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	logger := ctx.ExtractLogger(c)
	logger.Debug("work months", "mtd", workDate.Format("20060102"))

	workDir := ctx.ExtractWorkDir(c)
	var cwnData []CwnData
	if cfg.UsePstQssoFromFile != "" {
		cwnData, err = cw.getCwnDataFromFile(c, cfg.UsePstQssoFromFile)
	} else {
		cwnData, err = cw.getCwnData(c, workDate)
	}

	if err != nil {
		return CwnReport{}, err
	}

	csvFilePath := fmt.Sprintf("%s/pst_qsso_%s.csv", workDir, time.Now().Format("20060102150405"))
	if cfg.UsePstQssoFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, cwnData); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	return cw.postProcessData(c, cwnData)
}

func (cw CwnProcess) getCwnData(c context.Context, mtdDate time.Time) ([]CwnData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := cw.procFS.ReadFile("files/cwn_pst.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format(utils.ToGoDateFormat("YYYYMMDD"))},
	}

	logger.Info("Getting CWN data")
	return etlProc.QueryImpalaData[CwnData](c, string(buf), params)
}

func (cw CwnProcess) getCwnDataFromFile(c context.Context, filePath string) ([]CwnData, error) {
	logger := ctx.ExtractLogger(c)
	logger.Info("Getting CWN data from file", "path", filePath)

	f, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read() // skip header
	var cwnData []CwnData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		primary, err := strconv.ParseFloat(record[6], 64)
		if err != nil {
			primary = 0
			if record[6] != "" {
				logger.Error("failed to parse primary amount", "error", err, "value", record[6])
			}
		}

		secondary, err := strconv.ParseFloat(record[7], 64)
		if err != nil {
			secondary = 0
			if record[7] != "" {
				logger.Error("failed to parse secondary amount", "error", err, "value", record[7])
			}
		}

		tertiary, err := strconv.ParseFloat(record[8], 64)
		if err != nil {
			tertiary = 0
			if record[8] != "" {
				logger.Error("failed to parse tertiary amount", "error", err, "value", record[8])
			}
		}

		site3qss, err := strconv.ParseFloat(record[9], 64)
		if err != nil {
			site3qss = 0
			if record[9] != "" {
				logger.Error("failed to parse site 3qss amount", "error", err, "value", record[9])
			}
		}

		cwnData = append(cwnData, CwnData{
			MonthID:  record[0],
			Period:   record[1],
			AsofDate: record[2],
			Circle:   null.StringFrom(record[3]),
			Region:   null.StringFrom(record[4]),
			Brand:    record[5],
			CwnKpi: CwnKpi{
				Primary:   null.FloatFrom(primary),
				Secondary: null.FloatFrom(secondary),
				Tertiary:  null.FloatFrom(tertiary),
				Site3QSSO: null.FloatFrom(site3qss),
			},
		})
	}

	return cwnData, nil
}

func (cw CwnProcess) postProcessData(c context.Context, data []CwnData) (CwnReport, error) {
	brandInfo := map[string]*CwnReportData{
		"IOH": NewCwnReportData(),
		"IM3": NewCwnReportData(),
		"3ID": NewCwnReportData(),
	}

	iohData := NewCwnReportData()

	fmMap := make(map[string]struct{})

	for _, d := range data {
		brand := d.Brand
		if _, ok := brandInfo[brand]; !ok {
			brandInfo[brand] = NewCwnReportData()
		}

		brandData := brandInfo[brand]

		circle := strings.TrimSpace(strings.ToUpper(d.Circle.String))
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}
		region := strings.TrimSpace(strings.ToUpper(d.Region.String))

		if _, ok := brandData.RegionalData[region]; !ok {
			brandData.RegionalData[region] = NewRegionalCwnData("REGION", region)
		}
		regData := brandData.RegionalData[region]

		if _, ok := iohData.RegionalData[region]; !ok {
			iohData.RegionalData[region] = NewRegionalCwnData("REGION", region)
		}
		iohRegData := iohData.RegionalData[region]

		if _, ok := brandData.CircleData[circle]; !ok {
			brandData.CircleData[circle] = NewRegionalCwnData("CIRCLE", circle)
		}
		cirData := brandData.CircleData[circle]

		if _, ok := iohData.CircleData[circle]; !ok {
			iohData.CircleData[circle] = NewRegionalCwnData("CIRCLE", circle)
		}
		iohCirData := iohData.CircleData[circle]

		switch strings.ToUpper(d.Period) {
		case "MTD":
			cw.processMtdKpi(d, regData.MTD)
			cw.processMtdKpi(d, cirData.MTD)
			cw.processMtdKpi(d, brandData.NationalData.MTD)
			cw.processMtdKpi(d, iohRegData.MTD)
			cw.processMtdKpi(d, iohCirData.MTD)
			cw.processMtdKpi(d, iohData.NationalData.MTD)
		case "LMTD":
			cw.processMtdKpi(d, regData.LMTD)
			cw.processMtdKpi(d, cirData.LMTD)
			cw.processMtdKpi(d, brandData.NationalData.LMTD)
			cw.processMtdKpi(d, iohRegData.LMTD)
			cw.processMtdKpi(d, iohCirData.LMTD)
			cw.processMtdKpi(d, iohData.NationalData.LMTD)
		case "FM":
			fmMap[d.MonthID] = struct{}{}
			cw.processFmKpi(d, regData.FM)
			cw.processFmKpi(d, cirData.FM)
			cw.processFmKpi(d, brandData.NationalData.FM)
			cw.processFmKpi(d, iohRegData.FM)
			cw.processFmKpi(d, iohCirData.FM)
			cw.processFmKpi(d, iohData.NationalData.FM)
		}
	}

	return CwnReport{
		IM3:   brandInfo["IM3"],
		Three: brandInfo["3ID"],
		IOH:   iohData,
	}, nil
}

func (cw CwnProcess) processMtdKpi(data CwnData, kpi *CwnKpi) {
	kpi.Primary.Float64 += data.Primary.Float64
	kpi.Secondary.Float64 += data.Secondary.Float64
	kpi.Tertiary.Float64 += data.Tertiary.Float64
	kpi.Site3QSSO.Float64 += data.Site3QSSO.Float64
}

func (cw CwnProcess) processFmKpi(data CwnData, fmKpi map[string]*CwnKpi) {
	monthID := data.MonthID
	if _, ok := fmKpi[monthID]; !ok {
		fmKpi[monthID] = NewCwnKpi()
	}

	fmKpi[monthID].Primary.Float64 += data.Primary.Float64
	fmKpi[monthID].Secondary.Float64 += data.Secondary.Float64
	fmKpi[monthID].Tertiary.Float64 += data.Tertiary.Float64
	fmKpi[monthID].Site3QSSO.Float64 += data.Site3QSSO.Float64
}

var (
	PSTBrandStartCol      = 2
	PSTCircleStartCol     = 7
	PSTPrimaryColOffset   = 1
	PSTSecondaryColOffset = 2
	PSTTertiaryColOffset  = 3

	LMTDRowOffset = 3
	MTDRowOffset  = 4

	S3QssoBrandStartCol  = 46
	S3QssoCircleStartCol = 49
	S3QssoColOffset      = 1
	S3QssoIM3ColOffset   = 1
	S3Qsso3IDColOffset   = 2

	SH_NAME = "to ppt"
)

func (cw CwnProcess) WriteReport(xl *excelize.File, data CwnReport) error {
	pstIoh := xlutil.Cell(6, PSTBrandStartCol)
	pstIm3 := xlutil.Cell(16, PSTBrandStartCol)
	pst3ID := xlutil.Cell(26, PSTBrandStartCol)

	s3qssoIm3 := xlutil.Cell(6, S3QssoBrandStartCol)
	s3qsso3ID := xlutil.Cell(16, S3QssoBrandStartCol)

	cirRowMap := map[string]int{
		"JAKARTA RAYA": 6,
		"JAVA":         16,
		"KALISUMAPA":   26,
		"SUMATERA":     36,
	}

	// do PST IOH
	if err := cw.writePSTReport(xl, data.IOH.NationalData, pstIoh.Row, pstIoh.Col); err != nil {
		return err
	}

	// do PST IM3
	if err := cw.writePSTReport(xl, data.IM3.NationalData, pstIm3.Row, pstIm3.Col); err != nil {
		return err
	}

	// do PST 3ID
	if err := cw.writePSTReport(xl, data.Three.NationalData, pst3ID.Row, pst3ID.Col); err != nil {
		return err
	}

	// do PST Circles
	for circle, row := range cirRowMap {
		cirData, ok := data.IOH.CircleData[circle]
		if !ok {
			continue
		}
		if err := cw.writePSTReport(xl, cirData, row, PSTCircleStartCol); err != nil {
			return err
		}
	}

	//do Sqsso IM3
	if err := cw.writeSiteQssoReport(xl, data.IM3.NationalData, s3qssoIm3.Row, s3qssoIm3.Col, S3QssoColOffset); err != nil {
		return err
	}

	// do Sqsso 3ID
	if err := cw.writeSiteQssoReport(xl, data.Three.NationalData, s3qsso3ID.Row, s3qsso3ID.Col, S3QssoColOffset); err != nil {
		return err
	}

	// do S3QssoCircles
	for circle, row := range cirRowMap {
		cirDataIm3, okIm3 := data.IM3.CircleData[circle]
		cirData3id, ok3id := data.Three.CircleData[circle]

		if okIm3 {
			if err := cw.writeSiteQssoReport(xl, cirDataIm3, row, S3QssoCircleStartCol, S3QssoIM3ColOffset); err != nil {
				return err
			}
		}

		if ok3id {
			if err := cw.writeSiteQssoReport(xl, cirData3id, row, S3QssoCircleStartCol, S3Qsso3IDColOffset); err != nil {
				return err
			}
		}
	}

	return nil
}

func (cw CwnProcess) writePSTReport(xl *excelize.File, data *RegionalCwnData, startRow, startCol int) error {
	shName := SH_NAME

	fmList := utils.MapToList(data.FM, func(key string, value *CwnKpi) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for i, mth := range fmList {
		if i > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		r := startRow + (2 - i)
		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+PSTPrimaryColOffset).Address(), data.FM[mth].Primary.Float64); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+PSTSecondaryColOffset).Address(), data.FM[mth].Secondary.Float64); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+PSTTertiaryColOffset).Address(), data.FM[mth].Tertiary.Float64); err != nil {
			return err
		}
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+LMTDRowOffset, startCol+PSTPrimaryColOffset).Address(), data.LMTD.Primary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+LMTDRowOffset, startCol+PSTSecondaryColOffset).Address(), data.LMTD.Secondary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+LMTDRowOffset, startCol+PSTTertiaryColOffset).Address(), data.LMTD.Tertiary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+MTDRowOffset, startCol+PSTPrimaryColOffset).Address(), data.MTD.Primary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+MTDRowOffset, startCol+PSTSecondaryColOffset).Address(), data.MTD.Secondary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+MTDRowOffset, startCol+PSTTertiaryColOffset).Address(), data.MTD.Tertiary.Float64); err != nil {
		return err
	}

	return nil
}

func (cw CwnProcess) writeSiteQssoReport(xl *excelize.File, data *RegionalCwnData, startRow, startCol int, s3qssoOffset int) error {
	shName := SH_NAME

	fmList := utils.MapToList(data.FM, func(key string, value *CwnKpi) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for i, mth := range fmList {
		if i > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		r := startRow + (2 - i)
		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+s3qssoOffset).Address(), data.FM[mth].Site3QSSO.Float64); err != nil {
			return err
		}
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+LMTDRowOffset, startCol+s3qssoOffset).Address(), data.LMTD.Site3QSSO.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+MTDRowOffset, startCol+s3qssoOffset).Address(), data.MTD.Site3QSSO.Float64); err != nil {
		return err
	}

	return nil
}
