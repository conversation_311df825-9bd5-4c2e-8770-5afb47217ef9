package config

import (
	"context"
	"fmt"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	"github.com/csee-pm/etl/shared/logger"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/spf13/viper"
)

type Config struct {
	Tunnel           *cfg.TunnelConfig `yaml:"tunnel"`
	GP               cfg.GPConfig      `yaml:"gpfat"`
	Impala           cfg.ImpalaConfig  `yaml:"impala"`
	ImpalaLandingSsh cfg.SSHConfig     `yaml:"impala_landing_ssh"`
	EtlConfig        *EtlConfig        `yaml:"etl_config"`
	Notifier         *NotifierConfig   `yaml:"notifier"`
	LogLevel         logger.Level      `yaml:"log_level"`
	WorkDir          string            `yaml:"work_dir"`
}

func (c Config) ToMap() map[string]interface{} {
	var cfMap = make(map[string]interface{})
	if c.Tunnel != nil {
		cfMap["tunnel"] = c.Tunnel.ToMap()
	}

	cfMap["gpfat"] = c.GP.ToMap()
	cfMap["impala"] = c.Impala.ToMap()
	cfMap["impala_landing_ssh"] = c.ImpalaLandingSsh.ToMap()

	if c.EtlConfig != nil {
		cfMap["etl_config"] = c.EtlConfig.ToMap()
	}

	cfMap["log_level"] = c.LogLevel
	cfMap["work_dir"] = c.WorkDir

	return cfMap
}

type EtlConfig struct {
	StartDate *int `yaml:"start_dt"`
	EndDate   *int `yaml:"end_dt"`
}

func (et EtlConfig) ToMap() map[string]interface{} {
	var etMap = make(map[string]interface{})
	if et.StartDate != nil {
		etMap["start_dt"] = *et.StartDate
	}
	if et.EndDate != nil {
		etMap["end_dt"] = *et.EndDate
	}

	return etMap
}

type NotifierConfig struct {
	Email *cfg.EmailConfig `yaml:"email"`
}

func (n NotifierConfig) ToMap() map[string]interface{} {
	var nMap = make(map[string]interface{})
	if n.Email != nil {
		nMap["email"] = n.Email.ToMap()
	}

	return nMap
}

func GetConfig(c context.Context) (Config, error) {
	conf := ctx.ExtractConfig(c)
	if conf == nil {
		return Config{}, fmt.Errorf("failed to get cmd object from context")
	}

	var err error
	cf := Config{}
	cf.WorkDir = conf.GetString("work_dir")
	cf.LogLevel = logger.Level(conf.GetInt("log_level"))
	cf.EtlConfig, err = GetEtlConfig(conf, "etl_config")
	if err != nil {
		return cf, err
	}

	cf.GP, err = cfg.GetGPConfig(conf, "gpfat")
	if err != nil {
		return cf, err
	}

	cf.Impala, err = cfg.GetImpalaConfig(conf, "impala")
	if err != nil {
		return cf, err
	}

	cf.ImpalaLandingSsh, err = cfg.GetSSHConfig(conf, "impala_landing_ssh")
	if err != nil {
		return cf, err
	}

	if conf.Get("tunnel") != nil {
		tunCfg, err := cfg.GetTunnelConfig(conf, "tunnel")
		if err != nil {
			return cf, err
		}

		cf.Tunnel = &tunCfg
	}

	if conf.Get("notifier") != nil {
		if conf.Get("notifier.email") != nil {
			notifyConf, err := cfg.GetEmailConfig(conf, "notifier.email")
			if err != nil {
				return cf, err
			}

			cf.Notifier = &NotifierConfig{
				Email: &notifyConf,
			}
		}
	}

	return cf, nil
}

func GetEtlConfig(v *viper.Viper, key string) (*EtlConfig, error) {
	var cf EtlConfig
	err := v.UnmarshalKey(key, &cf)
	return &cf, err
}

func SetNewConfig(v *viper.Viper) error {
	useTun := utils.StringToBool(cfg.GetPromptValue("Use SSH Tunnel To connect to GP[y/N]: ", "N", cfg.ShouldBeYesOrNo))
	if useTun {
		tunCfg, err := cfg.PromptNewTunnelConfig(v, "tunnel")
		if err != nil {
			return err
		}

		v.Set("tunnel", tunCfg.ToMap())
	}

	fmt.Println("\nPlease provide Greenplum configuration")
	gpCfg, err := cfg.PromptNewGPConfig(v, "gpfat")
	if err != nil {
		return err
	}
	v.Set("gpfat", gpCfg.ToMap())

	fmt.Println("\nPlease provide Impala configuration")
	impalaCfg, err := cfg.PromptNewImpalaConfig(v, "impala")
	if err != nil {
		return err
	}
	v.Set("impala", impalaCfg.ToMap())

	fmt.Println("\nPlease provide SSH configuration for Impala Landing")
	impalaSsh, err := cfg.PromptNewSSHConfig(v, "impala_landing_ssh")
	if err != nil {
		return err
	}
	v.Set("impala_landing_ssh", impalaSsh.ToMap())

	useNotify := utils.StringToBool(cfg.GetPromptValue("Do you want to use email notifier?[y/N]: ", "N", cfg.ShouldBeYesOrNo))
	if useNotify {
		fmt.Println("\nPlease provide Email configuration")
		emailCfg, err := cfg.PromptNewEmailConfig(v, "notifier.email")
		if err != nil {
			return err
		}

		notifyConfig := &NotifierConfig{
			Email: &emailCfg,
		}
		v.Set("notifier", notifyConfig.ToMap())
	}

	return nil
}

func PromptNewConfig() (Config, error) {
	cf := Config{}

	var tunCfg *cfg.TunnelConfig
	var notifyConfig *NotifierConfig
	var err error

	v := viper.New()

	useTun := utils.StringToBool(cfg.GetPromptValue("Use SSH Tunnel To connect to GP[y/N]: ", "N", cfg.ShouldBeYesOrNo))
	if useTun {
		tunCfg, err = cfg.PromptNewTunnelConfig(v, "tunnel")
		if err != nil {
			return cf, err
		}
	}

	fmt.Println("\nPlease provide Greenplum configuration")
	gpCfg, err := cfg.PromptNewGPConfig(v, "gpfat")
	if err != nil {
		return cf, err
	}

	fmt.Println("\nPlease provide Impala configuration")
	impalaCfg, err := cfg.PromptNewImpalaConfig(v, "impala")
	if err != nil {
		return cf, err
	}

	fmt.Println("\nPlease provide SSH configuration for Impala Landing")
	impalaSsh, err := cfg.PromptNewSSHConfig(v, "impala_landing_ssh")
	if err != nil {
		return cf, err
	}

	useNotify := utils.StringToBool(cfg.GetPromptValue("Do you want to use email notifier?[y/N]: ", "N", cfg.ShouldBeYesOrNo))
	if useNotify {
		fmt.Println("\nPlease provide Email configuration")
		emailCfg, err := cfg.PromptNewEmailConfig(v, "notifier.email")
		if err != nil {
			return cf, err
		}

		notifyConfig = &NotifierConfig{
			Email: &emailCfg,
		}
	}

	return Config{
		Tunnel:           tunCfg,
		GP:               gpCfg,
		Impala:           impalaCfg,
		ImpalaLandingSsh: impalaSsh,
		EtlConfig: &EtlConfig{
			StartDate: utils.ToPtr(20240401),
		},
		Notifier: notifyConfig,
	}, nil
}
