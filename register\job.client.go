package register

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"net/http"
	"net/url"

	apikit "github.com/likearthian/apikit/api"
	"github.com/likearthian/go-http/client"
)

var (
	JobPath = "/register/job"
	EtlPath = "/register/etl"
)

type JobClient struct {
	client     client.HttpClient
	apiKey     string
	serverAddr string
}

func NewJobClient(serverAddr string, apiKey string) JobService {
	c := client.New()
	return &JobClient{client: c, serverAddr: serverAddr, apiKey: apiKey}
}

func (jc *JobClient) GetEtlByID(c context.Context, id string) (*EtlProcess, error) {
	urlPath := fmt.Sprintf("%s%s/%s", jc.serverAddr, EtlPath, id)
	res, err := jc.client.Method(http.MethodGet).URL(urlPath).Set("api_key", jc.apiKey).Call()
	if err != nil {
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get etl. %s", res.Status)
	}

	var data apikit.BaseResponse[EtlProcess]
	err = json.NewDecoder(res.Body).Decode(&data)
	if err != nil {
		return nil, err
	}

	if data.Error != nil {
		return nil, fmt.Errorf("failed to get etl. %s", *data.Error)
	}

	return &data.Data, nil
}

func (jc *JobClient) GetEtl(c context.Context, req GetEtlProcessRequestDTO) ([]EtlProcess, error) {
	urlPath := fmt.Sprintf("%s%s", jc.serverAddr, EtlPath)
	up, err := url.Parse(urlPath)
	if err != nil {
		return nil, err
	}

	q := up.Query()
	if len(req.ID) > 0 {
		for _, id := range req.ID {
			q.Add("id", id)
		}
	}

	if req.IsRunning.Valid {
		q.Add("is_running", strconv.FormatBool(req.IsRunning.ValueOrZero()))
	}

	up.RawQuery = q.Encode()
	res, err := jc.client.Method(http.MethodGet).URL(up.String()).Set("api_key", jc.apiKey).Call()
	if err != nil {
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get etl. %s", res.Status)
	}

	var data apikit.BaseResponse[[]EtlProcess]
	err = json.NewDecoder(res.Body).Decode(&data)
	if err != nil {
		return nil, err
	}

	if data.Error != nil {
		return nil, fmt.Errorf("failed to get etl. %s", *data.Error)
	}

	return data.Data, nil
}

func (jc *JobClient) GetJobByID(c context.Context, id string) (*JobRegister, error) {
	urlPath := fmt.Sprintf("%s%s/%s", jc.serverAddr, JobPath, id)
	res, err := jc.client.Method(http.MethodGet).URL(urlPath).Set("api_key", jc.apiKey).Call()
	if err != nil {
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get job. %s", res.Status)
	}

	var data apikit.BaseResponse[JobRegister]
	err = json.NewDecoder(res.Body).Decode(&data)
	if err != nil {
		return nil, err
	}

	if data.Error != nil {
		return nil, fmt.Errorf("failed to get job. %s", *data.Error)
	}

	return &data.Data, nil
}

func (jc *JobClient) GetJob(c context.Context, req GetJobRequestDTO) ([]JobRegister, error) {
	urlPath := fmt.Sprintf("%s%s", jc.serverAddr, JobPath)
	up, err := url.Parse(urlPath)
	if err != nil {
		return nil, err
	}

	q := up.Query()
	if len(req.ID) > 0 {
		for _, id := range req.ID {
			q.Add("id", strconv.FormatInt(id, 10))
		}
	}

	if req.IsRunning.Valid {
		q.Add("is_running", strconv.FormatBool(req.IsRunning.ValueOrZero()))
	}

	if len(req.Status) > 0 {
		for _, status := range req.Status {
			q.Add("status", status)
		}
	}

	if len(req.EtlID) > 0 {
		for _, etlID := range req.EtlID {
			q.Add("etl_id", etlID)
		}
	}

	up.RawQuery = q.Encode()
	res, err := jc.client.Method(http.MethodGet).URL(up.String()).Set("api_key", jc.apiKey).Call()
	if err != nil {
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get job. %s", res.Status)
	}

	var data apikit.BaseResponse[[]JobRegister]
	err = json.NewDecoder(res.Body).Decode(&data)
	if err != nil {
		return nil, err
	}

	if data.Error != nil {
		return nil, fmt.Errorf("failed to get job. %s", *data.Error)
	}

	return data.Data, nil
}

func (jc *JobClient) RegisterJobStart(c context.Context, req PostRegisterJobStartRequestDTO) (PostRegisterJobStartResponseDTO, error) {
	urlPath := fmt.Sprintf("%s%s", jc.serverAddr, JobPath)
	body, err := json.Marshal(req)
	if err != nil {
		return PostRegisterJobStartResponseDTO{}, fmt.Errorf("failed to marshal request. %s", err)
	}

	res, err := jc.client.Method(http.MethodPost).URL(urlPath).Set("api_key", jc.apiKey).Body(body).Call()
	if err != nil {
		return PostRegisterJobStartResponseDTO{}, err
	}

	if res.StatusCode != http.StatusOK {
		return PostRegisterJobStartResponseDTO{}, fmt.Errorf("failed to register job. %s", res.Status)
	}

	var data apikit.BaseResponse[PostRegisterJobStartResponseDTO]
	err = json.NewDecoder(res.Body).Decode(&data)
	if err != nil {
		return PostRegisterJobStartResponseDTO{}, err
	}

	if data.Error != nil {
		return PostRegisterJobStartResponseDTO{}, fmt.Errorf("failed to register job. %s", *data.Error)
	}

	return data.Data, nil
}

func (jc *JobClient) RegisterJobEnd(c context.Context, id string) (any, error) {
	urlPath := fmt.Sprintf("%s%s/%s", jc.serverAddr, JobPath, id)
	res, err := jc.client.Method(http.MethodPut).URL(urlPath).Set("api_key", jc.apiKey).Call()
	if err != nil {
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to end job. %s", res.Status)
	}

	var data apikit.BaseResponse[any]
	err = json.NewDecoder(res.Body).Decode(&data)
	if err != nil {
		return nil, err
	}

	if data.Error != nil {
		return nil, fmt.Errorf("failed to end job. %s", *data.Error)
	}

	return data.Data, nil
}

func (jc *JobClient) UpdateJobStatus(c context.Context, req UpdateJobStatusRequestDTO) (any, error) {
	idStr := strconv.FormatInt(req.JobID, 10)
	urlPath := fmt.Sprintf("%s%s/%s", jc.serverAddr, JobPath, idStr)
	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request. %s", err)
	}

	res, err := jc.client.Method(http.MethodPut).URL(urlPath).Set("api_key", jc.apiKey).Body(body).Call()
	if err != nil {
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to update job. %s", res.Status)
	}

	var data apikit.BaseResponse[any]
	err = json.NewDecoder(res.Body).Decode(&data)
	if err != nil {
		return nil, err
	}

	if data.Error != nil {
		return nil, fmt.Errorf("failed to update job. %s", *data.Error)
	}

	return nil, nil
}
