package dto

import (
	"fmt"

	"github.com/dgrijalva/jwt-go/v4"
)

type AuthResponseDTO struct {
	Username string `json:"username"`
	FullName string `json:"fullname"`
	Email    string `json:"email"`
	IsA<PERSON><PERSON>  bool   `json:"is_admin"`
	Token    string `json:"token"`
	Expiry   int64  `json:"expiry"`
}

type AuthenticateRequestDTO struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

func (ar AuthenticateRequestDTO) Valid() error {
	if ar.Username == "" {
		return fmt.Errorf("username field required")
	}

	if ar.Password == "" {
		return fmt.Errorf("password field required")
	}

	return nil
}

type AuthorizeRequestDTO struct {
	Token  string
	Claims JwtClaimsDTO
}

type JwtClaimsDTO struct {
	Username string
	IsAdmin  bool
	FullName string
	Email    string
	jwt.StandardClaims
}
