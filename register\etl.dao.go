package register

import (
	"context"
	"errors"

	"github.com/jmoiron/sqlx"
	"github.com/likearthian/store"
)

type EtlProcess struct {
	store.DBTable `name:"etl_register"`
	ID            string `db:"id" json:"id"`
	Name          string `db:"name" json:"name"`
	Status        string `db:"status" json:"status"`
}

type EtlProcessDAO struct {
	store.Repository[string, EtlProcess]
}

func NewEtlProcessDAO(client *sqlx.DB) (*EtlProcessDAO, error) {
	repo, err := store.CreateSqliteRepository[string, EtlProcess](client)
	if err != nil {
		return nil, err
	}

	processRegisterDAO := &EtlProcessDAO{Repository: repo}
	return processRegisterDAO, nil
}

func (dao *EtlProcessDAO) CreateEtlRegister(c context.Context, etlProcess EtlProcess) error {
	_, err := dao.Insert(c, etlProcess)
	return err
}

func (dao *EtlProcessDAO) GetEtlProcess(c context.Context, req GetEtlProcessRequestDTO) ([]EtlProcess, error) {
	filter := make(map[string]any)
	if len(req.ID) > 0 {
		filter["id"] = req.ID
	}

	if req.IsRunning.Valid {
		isRunning := 0
		if req.IsRunning.ValueOrZero() {
			isRunning = 1
		}
		filter["is_running"] = isRunning
	}

	var etlProcesses []EtlProcess
	err := dao.Select(c, filter, &etlProcesses)
	if err != nil && !errors.Is(err, store.ErrNoRow) {
		return nil, err
	}
	return etlProcesses, nil
}

func (dao *EtlProcessDAO) GetAllEtlProcess(c context.Context) ([]EtlProcess, error) {
	var etlProcesses []EtlProcess
	err := dao.Select(c, nil, &etlProcesses)
	if err != nil {
		return nil, err
	}
	return etlProcesses, nil
}
