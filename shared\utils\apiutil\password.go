package apiutil

import "golang.org/x/crypto/bcrypt"

var hashKey = "ThisShouldBeA32BitLongStringLock"

// PasswordHash is a helper function to generate bcrypt hash encoding using given password string and the salt key
func PasswordHash(password string) string {
	passBytes := []byte(hashKey + password)
	hash, _ := bcrypt.GenerateFromPassword(passBytes, bcrypt.DefaultCost)
	return string(hash)
}

// PasswordCompare is to compare a bcrypt's hashed string with the provided plain password string and a key.
// Returns nil if it's a match, and an error if it's not.
func PasswordCompare(hashedPassword, plainPassword string) error {
	plainByte := []byte(hashKey + plainPassword)
	hashedByte := []byte(hashedPassword)

	return bcrypt.CompareHashAndPassword(hashedByte, plainByte)
}
