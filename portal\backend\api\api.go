package api

import (
	"fmt"

	"github.com/csee-pm/etl/portal/backend/config"
	"github.com/csee-pm/etl/portal/backend/internal/idm"
	"github.com/csee-pm/etl/register"
	gconf "github.com/csee-pm/etl/shared/config"
	"github.com/spf13/viper"
	log "github.com/likearthian/apikit/logger"
	"github.com/jmoiron/sqlx"
)

type APIEndpoints struct {
	Auth     *idm.AuthAPI
	Idm      *idm.IdmAPI
	Register register.JobService
}

func CreateAPIEndpoints(conf *viper.Viper, db *sqlx.DB, logger log.Logger) (APIEndpoints, error) {
	ldapConfig, err := gconf.GetConfig[config.LdapConfig](conf, "ldap")
	if err != nil {
		return APIEndpoints{}, fmt.Errorf("failed to get ldap config. %s", err)
	}

	ldapService := idm.NewLdapService(ldapConfig)
	idmService, err := idm.CreateIdmAPI(conf, db, ldapService)
	if err != nil {
		return APIEndpoints{}, fmt.Errorf("failed to create idm api. %s", err)
	}

	authService, err := idm.CreateAuthAPI(conf, idmService, ldapService)
	if err != nil {
		return APIEndpoints{}, fmt.Errorf("failed to create auth api. %s", err)
	}

	jobService, err := register.NewJobService(db)
	if err != nil {


	return APIEndpoints{}, nil
}
