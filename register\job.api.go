package register

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"gopkg.in/guregu/null.v4"

	"github.com/csee-pm/etl/shared/utils/apiutil"
	"github.com/jmoiron/sqlx"
)

type jobService struct {
	jobDAO         *JobDAO
	etlDAO         *EtlProcessDAO
	runningEtlJobs map[string]int64
	jobLock        *sync.RWMutex
}

func NewJobService(dbcl *sqlx.DB) (JobService, error) {
	processDao, err := NewJobDAO(dbcl)
	if err != nil {
		return nil, fmt.Errorf("failed to create process dao. %s", err)
	}

	etlDao, err := NewEtlProcessDAO(dbcl)
	if err != nil {
		return nil, fmt.Errorf("failed to create etl dao. %s", err)
	}

	return &jobService{jobDAO: processDao, etlDAO: etlDao, runningEtlJobs: make(map[string]int64), jobLock: new(sync.RWMutex)}, nil
}

func (ps *jobService) setRunning(etlID string, jobID int64) {
	ps.jobLock.RLock()
	defer ps.jobLock.RUnlock()
	if _, ok := ps.runningEtlJobs[etlID]; ok {
		return
	}
	ps.jobLock.Lock()
	defer ps.jobLock.Unlock()
	ps.runningEtlJobs[etlID] = jobID
}

func (ps *jobService) GetEtlByID(c context.Context, id string) (*EtlProcess, error) {
	etls, err := ps.etlDAO.GetEtlProcess(c, GetEtlProcessRequestDTO{ID: []string{id}})
	if err != nil {
		return nil, err
	}

	if len(etls) == 0 {
		return nil, nil
	}

	etlProc := etls[0]
	ps.jobLock.RLock()
	defer ps.jobLock.RUnlock()
	if _, ok := ps.runningEtlJobs[id]; ok {
		etlProc.Status = "running"
	} else {
		etlProc.Status = "ready"
	}

	return &etlProc, nil
}

func (ps *jobService) GetEtl(c context.Context, req GetEtlProcessRequestDTO) ([]EtlProcess, error) {
	etls, err := ps.etlDAO.GetEtlProcess(c, req)
	if err != nil {
		return nil, err
	}

	ps.jobLock.RLock()
	defer ps.jobLock.RUnlock()
	for i := range etls {
		if _, ok := ps.runningEtlJobs[etls[i].ID]; ok {
			etls[i].Status = "running"
		} else {
			etls[i].Status = "ready"
		}
	}

	return etls, nil
}

func (ps *jobService) GetJobByID(c context.Context, id string) (*JobRegister, error) {
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse id. %s", err)
	}

	prcs, err := ps.jobDAO.GetJob(c, GetJobRequestDTO{ID: []int64{idInt}})
	if err != nil {
		return nil, err
	}

	if len(prcs) == 0 {
		return nil, nil
	}
	job := prcs[0]

	return &job, nil
}

func (ps *jobService) GetJob(c context.Context, req GetJobRequestDTO) ([]JobRegister, error) {
	return ps.jobDAO.GetJob(c, req)
}

func (ps *jobService) RegisterJobStart(c context.Context, req PostRegisterJobStartRequestDTO) (PostRegisterJobStartResponseDTO, error) {
	auth, err := apiutil.CheckAuthFromContext(c, true)
	if err != nil {
		return PostRegisterJobStartResponseDTO{}, err
	}

	if auth == nil {
		return PostRegisterJobStartResponseDTO{}, fmt.Errorf("unauthorized")
	}

	etlID := req.EtlID
	etlproc, err := ps.GetEtlByID(c, etlID)
	if err != nil {
		return PostRegisterJobStartResponseDTO{}, fmt.Errorf("failed to get etl info. %s", err)
	}

	if etlproc == nil {
		return PostRegisterJobStartResponseDTO{}, fmt.Errorf("etl %s not found", etlID)
	}

	isRunning, id, err := ps.jobDAO.GetEtlRunningStatus(c, etlID)
	if err != nil {
		return PostRegisterJobStartResponseDTO{}, err
	}

	if isRunning {
		return PostRegisterJobStartResponseDTO{}, fmt.Errorf("etl %s is already running. job id %d", etlproc.Name, id)
	}

	optionsJson := null.String{}
	outputID := null.String{}

	if req.Options != nil {
		buf, err := json.Marshal(req.Options)
		if err != nil {
			return PostRegisterJobStartResponseDTO{}, fmt.Errorf("failed to marshal job options. %s", err)
		}

		optionsJson = null.StringFrom(string(buf))
	}

	if req.Output.ValueOrZero() != "" {
		outputID = req.Output
	}

	startedBy := auth.Username

	job, err := ps.jobDAO.RegisterJobStart(c, etlID, optionsJson, outputID, startedBy)
	if err != nil {
		return PostRegisterJobStartResponseDTO{}, err
	}

	ps.jobLock.Lock()
	defer ps.jobLock.Unlock()
	ps.runningEtlJobs[etlID] = job.ID

	return PostRegisterJobStartResponseDTO{JobID: job.ID}, nil
}

func (ps *jobService) RegisterJobEnd(c context.Context, id string) (any, error) {
	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse id. %s", err)
	}

	exJobs, err := ps.jobDAO.GetJob(c, GetJobRequestDTO{ID: []int64{idInt}})
	if err != nil {
		return nil, fmt.Errorf("failed to get job info. %s", err)
	}

	if len(exJobs) == 0 {
		return nil, fmt.Errorf("job %d not found", idInt)
	}

	etlID := exJobs[0].EtlID

	req := UpdateJobStatusRequestDTO{
		JobID:   idInt,
		Status:  null.StringFrom("completed"),
		EndTime: null.IntFrom(time.Now().UnixMilli()),
	}

	err = ps.jobDAO.UpdateJobStatus(c, req)
	if err != nil {
		return nil, err
	}

	ps.jobLock.Lock()
	defer ps.jobLock.Unlock()
	delete(ps.runningEtlJobs, etlID)

	return nil, nil
}

func (ps *jobService) UpdateJobStatus(c context.Context, req UpdateJobStatusRequestDTO) (any, error) {
	err := ps.jobDAO.UpdateJobStatus(c, req)
	return nil, err
}
