SELECT
    'IM3' brand,
    cast(to_timestamp(ga_dt,'yyyyMMdd') as date) AS ga_date,
    a.flag_quality,
    a.product_name,
    a.package_type_ng,
    case when family_ng = 'OTHERS' and package_type_ng = 'SP OLA' then 'SP OLA'
    when b.msisdn is not null and not upper(family_ng) like '%SP DATA%' then concat('TRY ME','-',family_ng) else a.family_ng end family_ng,
    a.inject,
    site_flag,
    c.sea_geosegment_im3 AS sea_flag,
    CONCAT(CONCAT(a.channel, '-'), a.channel2) AS channel,
    a.msisdn,
    SUM(acq_rev_m0) AS revenue,
    strleft(ga_dt,6) mth_id
FROM
    dm.pras_rgu_ga_mtd a
    LEFT JOIN
    (
        select
            msisdn,
            substring(taker_date,1,6) as mth_id
        from
            dm.im3_sp_inject_cvm
        where
            product_campaign = 'SP Zero'
        group by 1,2

    ) b
    on
        a.msisdn = b.msisdn
    and substring(a.ga_dt,1,6) = substring(mth_id,1,6)
    LEFT JOIN
    dm.mkt_reference_lrs_lrk_lock_feb h
    ON
        a.site_id = h.site_id
    LEFT JOIN
    biadm.ref_site f
    ON
        a.site_id = f.site_id
    LEFT JOIN
    dm.mkt_sea_geo_segment_im3 c
    ON
        UPPER(f.kecamatan_nm) = UPPER(c.kecamatan_im3)
    AND UPPER(f.kabkot_nm) = UPPER(c.kabupaten_im3)
WHERE
    period >= ${start_dt_id}
and period <= ${end_dt_id_exc}
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,strleft(ga_dt,6)
order by 2