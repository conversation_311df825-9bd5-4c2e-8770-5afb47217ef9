package main

import (
	"fmt"
	"strconv"

	etlDb "github.com/csee-pm/etl/shared/db"
	"gopkg.in/guregu/null.v4"
)

func main() {
	params := map[string]*etlDb.ParamValue{
		"start_dt_id": {Name: "start_dt_id", Value: "20241101"},
		"end_dt_id":   {Name: "end_dt_id", Value: "20250131"},
		"dt_id":       {Name: "dt_id", Value: "20250211"},
	}

	query, args, err := etlDb.ParseSqlWithParams(qry, params)
	if err != nil {
		panic(err)
	}

	iplClient, err := etlDb.CreateImpalaSqlClient(etlDb.ImpalaConfig{
		User:     "80208379",
		Password: "Indosat@2502",
		Host:     "udc2-impala-lb.office.corp.indosat.com",
		Port:     "21051",
		Cacerts:  "truststore.jks",
	}, etlDb.UseInsecureTLS())
	if err != nil {
		panic(err)
	}

	if err := iplClient.Ping(); err != nil {
		panic(err)
	}

	fmt.Printf("executing query: %s\nargs: %v\n", query, args)

	var data []FmPSTData
	if err := iplClient.Select(&data, query, args...); err != nil {
		panic(err)
	}

	for _, d := range data {
		fmt.Println(d.GetRowValues())
	}
}

type RegionPSTKpi struct {
	Circle null.String `db:"circle"`
	Region null.String `db:"region"`
	Brand  string      `db:"brand"`
	PSTKpi
}

type PSTKpi struct {
	Primary   float64 `db:"primary"`
	Secondary float64 `db:"secondary"`
	Tertiary  float64 `db:"tertiary"`
}
type FmPSTData struct {
	MonthID string `db:"month_id"`
	RegionPSTKpi
}

func (fm FmPSTData) GetColumns() []string {
	return []string{"month_id", "circle", "region", "brand", "primary", "secondary", "tertiary"}
}

func (fm FmPSTData) GetRowValues() []string {
	return []string{fm.MonthID, fm.Circle.String, fm.Region.String, fm.Brand, strconv.FormatFloat(fm.Primary, 'f', -1, 64), strconv.FormatFloat(fm.Secondary, 'f', -1, 64), strconv.FormatFloat(fm.Tertiary, 'f', -1, 64)}
}

var qry = `
with max_date as (
    select
        a.mth month_id,
        a.kec_unik,
        max(a.dt_id) max_dt
    from
        rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
    where
        a.dt_id >= ${start_dt_id}
    and a.dt_id <= ${end_dt_id}
    and a.mthf = 'mtd'
    and a.parameter in
      (
       'Primary',
       'primary',
       'Secondary',
       'secondary',
       'Tertiary B#',
       'tertiary'
          )
    group by 1,2
)
select
    a.mth month_id,
    coalesce(b.circle, 'Null') circle,
    coalesce(b.region, 'Null') region,
    a.brand,
    sum(case when a.parameter in ('Primary', 'primary') then a.amount else 0 end) "primary",
    sum(case when a.parameter in ('Secondary', 'secondary') then a.amount else 0 end) secondary,
    sum(case when a.parameter in ('Tertiary B#', 'tertiary') then a.amount else 0 end) tertiary
from
    rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
    inner join
    max_date md
    on
        a.dt_id = md.max_dt
    and a.kec_unik = md.kec_unik
    left join
    biadm.ref_kecamatan b
    on
        a.kec_unik = b.kec_kabkot
where 1=1
  and a.dt_id >= ${start_dt_id}
  and a.dt_id <= ${end_dt_id}
  and a.mthf = 'mtd'
  and a.parameter in
      (
       'Primary',
       'primary',
       'Secondary',
       'secondary',
       'Tertiary B#',
       'tertiary'
          )
group by 1,2,3,4

`
