package utils

import (
	"archive/zip"
	"io"
	"os"
	"strings"
)

func PackZip(w io.Writer, files ...string) error {
	zw := zip.NewWriter(w)

	for _, file := range files {
		if err := addFileToZip(zw, file); err != nil {
			return err
		}
	}

	return zw.Close()
}

func addFileToZip(zw *zip.Writer, file string) error {
	f, err := os.Open(file)
	if err != nil {
		return err
	}
	defer f.Close()

	info, err := f.Stat()
	if err != nil {
		return err
	}

	basename := info.Name()

	header, err := zip.FileInfoHeader(info)
	if err != nil {
		return err
	}

	header.Name = strings.ReplaceAll(basename, "\\", "/") // set the name of the header to the basename of the file, e.g., "file.txt" instead of "C:\path\to\file.txt"
	header.Method = zip.Deflate

	writer, err := zw.CreateHeader(header)
	if err != nil {
		return err
	}

	_, err = io.Co<PERSON>(writer, f)

	return err
}
