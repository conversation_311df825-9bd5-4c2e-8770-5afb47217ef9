package process

import (
	"context"
	"github.com/spf13/viper"

	prc "github.com/csee-pm/etl/shared/process"
)

type ProcessItem struct {
	ID     string
	Exec   func(context.Context, ...prc.ProcessOption) error
	Mailer func() error
	conf   *viper.Viper
}

func WithEtlProcess(id string, etl func(context.Context, ...prc.ProcessOption) error) *ProcessItem {
	exec := func(c context.Context, options ...prc.ProcessOption) error {
		return etl(c)
	}
	return &ProcessItem{ID: id, Exec: exec}
}

func (pi *ProcessItem) Execute(c context.Context, options ...prc.ProcessOption) error {
	return pi.Exec(c, options...)
}
