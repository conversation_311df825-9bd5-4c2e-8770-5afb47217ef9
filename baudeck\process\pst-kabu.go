package process

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"io/fs"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	cfg "github.com/csee-pm/etl/baudeck/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type PstKabuProcess struct {
	procFS fs.ReadFileFS
}

func NewPstKabuProcess(procFS fs.ReadFileFS) PstKabuProcess {
	return PstKabuProcess{procFS}
}

func (pst PstKabuProcess) GetReportData(c context.Context, workDate time.Time) (PstKabuReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)
	workDir := ctx.ExtractWorkDir(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return PstKabuReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	var data []PstKabuData
	if cfg.UsePstKabuFromFile != "" {
		data, err = pst.getPstDataFromFile(c, cfg.UsePstKabuFromFile)
	} else {
		data, err = pst.getPstData(c, workDate)
	}

	if err != nil {
		return PstKabuReport{}, err
	}

	csvFilePath := fmt.Sprintf("%s/pst_kabu_%s.csv", workDir, time.Now().Format("20060102150405"))

	if cfg.UsePstKabuFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, data); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	return pst.postProcessData(data)
}

func (pst PstKabuProcess) getPstDataFromFile(c context.Context, fpath string) ([]PstKabuData, error) {
	logger := ctx.ExtractLogger(c)

	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()

	var data []PstKabuData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}

		secondary, err := strconv.ParseFloat(record[8], 64)
		if err != nil {
			secondary = 0
			if record[8] != "" {
				logger.Error("failed to parse secondary value from file", "error", err, "value", record[8], "file", fpath)
			}
		}

		tertiary, err := strconv.ParseFloat(record[9], 64)
		if err != nil {
			tertiary = 0
			if record[9] != "" {
				logger.Error("failed to parse tertiary value from file", "error", err, "value", record[9], "file", fpath)
			}
		}

		data = append(data, PstKabuData{
			MonthID:   record[0],
			Period:    record[1],
			AsofDate:  record[2],
			Circle:    null.StringFrom(record[3]),
			Region:    null.StringFrom(record[4]),
			Kabupaten: null.StringFrom(record[5]),
			KabuFlag:  null.StringFrom(record[6]),
			Brand:     record[7],
			PstKabuKpi: PstKabuKpi{
				Secondary: null.FloatFrom(secondary),
				Tertiary:  null.FloatFrom(tertiary),
			},
		})
	}

	return data, nil
}

func (pst PstKabuProcess) getPstData(c context.Context, mtdDate time.Time) ([]PstKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := pst.procFS.ReadFile("files/cwn_pst_kabu.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format(utils.ToGoDateFormat("YYYYMMDD"))},
	}

	kabumap, err := etlProc.GetMicroMktMap(c)
	if err != nil {
		return nil, err
	}

	logger.Info("Getting PST Kabu data")
	data, err := etlProc.QueryImpalaData[PstKabuData](c, string(buf), params)
	if err != nil {
		return nil, err
	}

	for i := range data {
		kabu := strings.TrimSpace(strings.ToUpper(data[i].Kabupaten.String))
		if segment, ok := kabumap[kabu]; ok {
			data[i].KabuFlag = null.StringFrom(segment)
		}
	}

	return data, nil
}

func (pst PstKabuProcess) postProcessData(data []PstKabuData) (PstKabuReport, error) {
	//regionMap := make(map[string]*RegionalPstKabuData)
	//circleMap := make(map[string]*RegionalPstKabuData)
	//regionalKabuMap := make(map[string]map[string]*RegionalPstKabuData)
	//nationalData := NewRegionalPstKabuData("NATIONAL", "INDONESIA")
	//
	//nationalKabuMap := make(map[string]*RegionalPstKabuData)

	brandInfo := map[string]*PstKabuReportData{
		"IM3": NewPstKabuReportData(),
		"3ID": NewPstKabuReportData(),
	}

	iohData := NewPstKabuReportData()

	fmMap := make(map[string]struct{})
	for _, d := range data {
		period := d.Period
		flag := strings.TrimSpace(strings.ToUpper(d.KabuFlag.String))
		circle := d.Circle.String
		region := d.Region.String

		brand := d.Brand
		if _, ok := brandInfo[brand]; !ok {
			brandInfo[brand] = NewPstKabuReportData()
		}

		brandData := brandInfo[brand]

		if _, ok := brandData.RegionalMap[region]; !ok {
			brandData.RegionalMap[region] = NewRegionalPstKabuData("REGION", region)
			brandData.RegionalKabuMap[region] = make(map[string]*RegionalPstKabuData)
		}
		regionData := brandData.RegionalMap[region]

		if _, ok := brandData.RegionalKabuMap[region][flag]; !ok {
			brandData.RegionalKabuMap[region][flag] = NewRegionalPstKabuData("FLAG", flag)
		}
		regionKabuData := brandData.RegionalKabuMap[region][flag]

		if _, ok := iohData.RegionalMap[region]; !ok {
			iohData.RegionalMap[region] = NewRegionalPstKabuData("REGION", region)
			iohData.RegionalKabuMap[region] = make(map[string]*RegionalPstKabuData)
		}
		iohRegionData := iohData.RegionalMap[region]

		if _, ok := iohData.RegionalKabuMap[region][flag]; !ok {
			iohData.RegionalKabuMap[region][flag] = NewRegionalPstKabuData("FLAG", flag)
		}
		iohRegionKabuData := iohData.RegionalKabuMap[region][flag]

		if _, ok := brandData.CircleMap[circle]; !ok {
			brandData.CircleMap[circle] = NewRegionalPstKabuData("CIRCLE", circle)
		}
		circleData := brandData.CircleMap[circle]

		if _, ok := iohData.CircleMap[circle]; !ok {
			iohData.CircleMap[circle] = NewRegionalPstKabuData("CIRCLE", circle)
		}
		iohCircleData := iohData.CircleMap[circle]

		if _, ok := brandData.NationalKabuMap[flag]; !ok {
			brandData.NationalKabuMap[flag] = NewRegionalPstKabuData("FLAG", flag)
		}
		nationalKabuData := brandData.NationalKabuMap[flag]
		nationalData := brandData.NationalData

		if _, ok := iohData.NationalKabuMap[flag]; !ok {
			iohData.NationalKabuMap[flag] = NewRegionalPstKabuData("FLAG", flag)
		}
		iohNationalKabuData := iohData.NationalKabuMap[flag]
		iohNationalData := iohData.NationalData

		switch period {
		case "MTD":
			brandData.AsofDate = d.AsofDate
			iohData.AsofDate = d.AsofDate

			pst.processKpiData(d, regionData.MTD)
			regionData.KabuCount++
			//regionData.MTD.Secondary.Float64 += d.Secondary.Float64
			//regionData.MTD.Tertiary.Float64 += d.Tertiary.Float64
			//regionData.MTD.Site3QSSO.Float64 += d.Site3QSSO.Float64

			pst.processKpiData(d, regionKabuData.MTD)
			regionKabuData.KabuCount++
			//regionKabuData.MTD.Secondary = d.Secondary
			//regionKabuData.MTD.Tertiary = d.Tertiary
			//regionKabuData.MTD.Site3QSSO = d.Site3QSSO

			pst.processKpiData(d, circleData.MTD)
			circleData.KabuCount++
			//circleData.MTD.Secondary.Float64 += d.Secondary.Float64
			//circleData.MTD.Tertiary.Float64 += d.Tertiary.Float64
			//circleData.MTD.Site3QSSO.Float64 += d.Site3QSSO.Float64

			pst.processKpiData(d, nationalData.MTD)
			nationalData.KabuCount++
			//nationalData.MTD.Secondary.Float64 += d.Secondary.Float64
			//nationalData.MTD.Tertiary.Float64 += d.Tertiary.Float64
			//nationalData.MTD.Site3QSSO.Float64 += d.Site3QSSO.Float64

			pst.processKpiData(d, nationalKabuData.MTD)
			nationalKabuData.KabuCount++
			//nationalKabuData.MTD.Secondary.Float64 += d.Secondary.Float64
			//nationalKabuData.MTD.Tertiary.Float64 += d.Tertiary.Float64
			//nationalKabuData.MTD.Site3QSSO.Float64 += d.Site3QSSO.Float64

			pst.processKpiData(d, iohRegionData.MTD)
			iohRegionData.KabuCount++

			pst.processKpiData(d, iohRegionKabuData.MTD)
			iohRegionKabuData.KabuCount++

			pst.processKpiData(d, iohCircleData.MTD)
			iohCircleData.KabuCount++

			pst.processKpiData(d, iohNationalData.MTD)
			iohNationalData.KabuCount++

			pst.processKpiData(d, iohNationalKabuData.MTD)
			iohNationalKabuData.KabuCount++

		case "LMTD":
			pst.processKpiData(d, regionData.LMTD)
			//regionData.LMTD.Secondary.Float64 += d.Secondary.Float64
			//regionData.LMTD.Tertiary.Float64 += d.Tertiary.Float64
			//regionData.LMTD.Site3QSSO.Float64 += d.Site3QSSO.Float64

			pst.processKpiData(d, regionKabuData.LMTD)
			//regionKabuData.LMTD.Secondary = d.Secondary
			//regionKabuData.LMTD.Tertiary = d.Tertiary
			//regionKabuData.LMTD.Site3QSSO = d.Site3QSSO

			pst.processKpiData(d, circleData.LMTD)
			//circleData.LMTD.Secondary.Float64 += d.Secondary.Float64
			//circleData.LMTD.Tertiary.Float64 += d.Tertiary.Float64
			//circleData.LMTD.Site3QSSO.Float64 += d.Site3QSSO.Float64

			pst.processKpiData(d, nationalData.LMTD)
			//nationalData.LMTD.Secondary.Float64 += d.Secondary.Float64
			//nationalData.LMTD.Tertiary.Float64 += d.Tertiary.Float64
			//nationalData.LMTD.Site3QSSO.Float64 += d.Site3QSSO.Float64

			pst.processKpiData(d, nationalKabuData.LMTD)
			//nationalKabuData.LMTD.Secondary.Float64 += d.Secondary.Float64
			//nationalKabuData.LMTD.Tertiary.Float64 += d.Tertiary.Float64
			//nationalKabuData.LMTD.Site3QSSO.Float64 += d.Site3QSSO.Float64

			pst.processKpiData(d, iohRegionData.LMTD)
			pst.processKpiData(d, iohRegionKabuData.LMTD)
			pst.processKpiData(d, iohCircleData.LMTD)
			pst.processKpiData(d, iohNationalData.LMTD)
			pst.processKpiData(d, iohNationalKabuData.LMTD)

		case "FM":
			pst.processFMKpiData(d, regionData.FM)
			pst.processFMKpiData(d, regionKabuData.FM)
			pst.processFMKpiData(d, circleData.FM)
			pst.processFMKpiData(d, nationalData.FM)
			pst.processFMKpiData(d, nationalKabuData.FM)
			pst.processFMKpiData(d, iohRegionData.FM)
			pst.processFMKpiData(d, iohRegionKabuData.FM)
			pst.processFMKpiData(d, iohCircleData.FM)
			pst.processFMKpiData(d, iohNationalData.FM)
			pst.processFMKpiData(d, iohNationalKabuData.FM)
		}

	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	brandInfo["IM3"].FmList = fmList
	brandInfo["3ID"].FmList = fmList
	iohData.FmList = fmList

	return PstKabuReport{
		IOH:   iohData,
		IM3:   brandInfo["IM3"],
		Three: brandInfo["3ID"],
	}, nil
}

func (pst PstKabuProcess) processKpiData(data PstKabuData, row *PstKabuKpi) error {
	row.Secondary.Float64 += data.Secondary.Float64
	row.Tertiary.Float64 += data.Tertiary.Float64
	row.Site3QSSO.Float64 += data.Site3QSSO.Float64

	return nil
}

func (pst PstKabuProcess) processFMKpiData(data PstKabuData, fm map[string]*PstKabuKpi) error {
	monthID := data.MonthID
	if _, ok := fm[monthID]; !ok {
		fm[monthID] = NewPstKabuKpi()
	}

	fm[monthID].Secondary.Float64 += data.Secondary.Float64
	fm[monthID].Tertiary.Float64 += data.Tertiary.Float64
	fm[monthID].Site3QSSO.Float64 += data.Site3QSSO.Float64

	return nil
}

var (
	PST_SHEET = "to ppt"

	PSTKabuStartCol = 13

	PSTMtdOffsetRow  = 4
	PSTLmtdOffsetCol = 3

	kabuFlagMap = map[string]string{
		"MUST WIN 50":     "MUST WIN 50",
		"SUPER 88":        "SUPER 88",
		"MUST WIN - REST": "REST MUST WIN",
		"WINNING":         "WINNING",
	}
)

func (pst PstKabuProcess) WriteReport(xl *excelize.File, data PstKabuReport) error {
	// startCells := map[string]xlutil.CellStruct{
	// 	"WINNING":       xlutil.Cell(6, 13),
	// 	"MUST WIN 50":   xlutil.Cell(16, 13),
	// 	"SUPER 88":      xlutil.Cell(36, 13),
	// 	"REST MUST WIN": xlutil.Cell(26, 13),
	// }

	startCells := map[string]xlutil.CellStruct{
		"MUST WIN 50":         xlutil.Cell(6, 13),
		"SUPER 88":            xlutil.Cell(16, 13),
		"HIGH POTENTIAL AREA": xlutil.Cell(26, 13),
		"REST MUST WIN":       xlutil.Cell(36, 13),
		"THE REST":            xlutil.Cell(46, 13),
	}

	for k, c := range startCells {
		if err := pst.writePstKabuReport(xl, data.IOH.NationalKabuMap[k], c.Row, c.Col); err != nil {
			return err
		}

		if err := pst.writeQssoKabuReport(xl, data.IM3.NationalKabuMap[k], c.Row, c.Col+41); err != nil {
			return err
		}

		if err := pst.writeQssoKabuReport(xl, data.Three.NationalKabuMap[k], c.Row, c.Col+42); err != nil {
			return err
		}
	}

	return nil
}

func (pst PstKabuProcess) writePstKabuReport(xl *excelize.File, data *RegionalPstKabuData, startRow, startCol int) error {
	shName := PST_SHEET

	fmList := utils.MapToList(data.FM, func(key string, value *PstKabuKpi) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for m, mth := range fmList {
		if m > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		r := startRow + (2 - m)

		mthName := mthDate.Format("Jan-06")
		if err := xl.SetCellValue(shName, xlutil.Cell(r, PSTKabuStartCol-1).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, PSTKabuStartCol+3).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, PSTKabuStartCol+40).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, PSTKabuStartCol).Address(), data.FM[mth].Secondary.Float64); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, PSTKabuStartCol+1).Address(), data.FM[mth].Tertiary.Float64); err != nil {
			return err
		}
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTMtdOffsetRow, PSTKabuStartCol).Address(), data.MTD.Secondary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTMtdOffsetRow, PSTKabuStartCol+1).Address(), data.MTD.Tertiary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTLmtdOffsetCol, PSTKabuStartCol).Address(), data.LMTD.Secondary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTLmtdOffsetCol, PSTKabuStartCol+1).Address(), data.LMTD.Tertiary.Float64); err != nil {
		return err
	}

	return nil
}

func (pst PstKabuProcess) writeQssoKabuReport(xl *excelize.File, data *RegionalPstKabuData, startRow, startCol int) error {
	shName := PST_SHEET

	fmList := utils.MapToList(data.FM, func(key string, value *PstKabuKpi) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for m, mth := range fmList {
		if m > 2 {
			break
		}

		r := startRow + (2 - m)

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol).Address(), data.FM[mth].Site3QSSO.Float64); err != nil {
			return err
		}
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTMtdOffsetRow, startCol).Address(), data.MTD.Site3QSSO.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTLmtdOffsetCol, startCol).Address(), data.LMTD.Site3QSSO.Float64); err != nil {
		return err
	}

	return nil
}
