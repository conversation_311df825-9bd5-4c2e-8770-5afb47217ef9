package http

import (
	"net/http"

	"github.com/csee-pm/etl/register"
	htx "github.com/likearthian/apikit/transport/http"
)

type JobServiceHandlers struct {
	GetEtlByIDHandler       http.Handler
	GetEtlHandler           http.Handler
	GetJobByIDHandler       http.Handler
	GetJobHandler           http.Handler
	RegisterJobStartHandler http.Handler
	RegisterJobEndHandler   http.Handler
	UpdateJobStatusHandler  http.Handler
}

func createJobServiceHandlers(jobService register.JobService, options ...htx.ServerOption) JobServiceHandlers {
	return JobServiceHandlers{
		GetEtlByIDHandler: htx.NewServer(
			jobService.GetEtlByID,
			htx.CommonGetByIDStringRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		GetEtlHandler: htx.NewServer(
			jobService.GetEtl,
			htx.CommonGetRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		GetJobByIDHandler: htx.NewServer(
			jobService.GetJobByID,
			htx.CommonGetByIDStringRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		GetJobHandler: htx.NewServer(
			jobService.GetJob,
			htx.CommonGetRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		RegisterJobStartHandler: htx.NewServer(
			jobService.RegisterJobStart,
			htx.CommonPostRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		RegisterJobEndHandler: htx.NewServer(
			jobService.RegisterJobEnd,
			htx.CommonGetByIDStringRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		UpdateJobStatusHandler: htx.NewServer(
			jobService.UpdateJobStatus,
			htx.CommonPostRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
	}
}
