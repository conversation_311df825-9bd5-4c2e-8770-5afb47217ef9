/*
Copyright © 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
*/
package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// configSetCmd represents the cmd command
var configSetCmd = &cobra.Command{
	Use:   "config-set",
	Short: "set config value",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("config-set called", args)
	},
}

func initConfigSetCmd(_ *viper.Viper) *cobra.Command {
	return configSetCmd
}
