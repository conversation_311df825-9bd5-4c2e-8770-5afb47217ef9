package api

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/csee-pm/etl/portal/backend/db"
)

// handleListProcesses handles GET /api/processes
func (r *Router) handleListProcesses(w http.ResponseWriter, req *http.Request) {
	processes, err := r.db.ListProcesses()
	if err != nil {
		respondError(w, http.StatusInternalServerError, "Failed to list processes: "+err.Error())
		return
	}

	respondJSON(w, http.StatusOK, processes)
}

// handleGetProcess handles GET /api/processes/{id}
func (r *Router) handleGetProcess(w http.ResponseWriter, req *http.Request) {
	id, err := getIDParam(req, "id")
	if err != nil {
		respondError(w, http.StatusBadRequest, "Invalid process ID: "+err.Error())
		return
	}

	process, err := r.db.GetProcess(id)
	if err != nil {
		respondError(w, http.StatusNotFound, "Process not found: "+err.Error())
		return
	}

	respondJSON(w, http.StatusOK, process)
}

// ProcessRequest represents a request to create a process
type ProcessRequest struct {
	Name    string `json:"name"`
	Command string `json:"command"`
	Args    string `json:"args"`
}

// handleCreateProcess handles POST /api/processes
func (r *Router) handleCreateProcess(w http.ResponseWriter, req *http.Request) {
	var processReq ProcessRequest
	if err := json.NewDecoder(req.Body).Decode(&processReq); err != nil {
		respondError(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	process := &db.Process{
		Name:      processReq.Name,
		Status:    "pending",
		StartTime: time.Now(),
		Command:   processReq.Command,
		Args:      processReq.Args,
	}

	if err := r.db.CreateProcess(process); err != nil {
		respondError(w, http.StatusInternalServerError, "Failed to create process: "+err.Error())
		return
	}

	respondJSON(w, http.StatusCreated, process)
}

// ProcessUpdateRequest represents a request to update a process
type ProcessUpdateRequest struct {
	Status   string `json:"status"`
	EndTime  string `json:"end_time,omitempty"`
	Output   string `json:"output,omitempty"`
	ExitCode int    `json:"exit_code"`
}

// handleUpdateProcess handles PUT /api/processes/{id}
func (r *Router) handleUpdateProcess(w http.ResponseWriter, req *http.Request) {
	id, err := getIDParam(req, "id")
	if err != nil {
		respondError(w, http.StatusBadRequest, "Invalid process ID: "+err.Error())
		return
	}

	process, err := r.db.GetProcess(id)
	if err != nil {
		respondError(w, http.StatusNotFound, "Process not found: "+err.Error())
		return
	}

	var updateReq ProcessUpdateRequest
	if err := json.NewDecoder(req.Body).Decode(&updateReq); err != nil {
		respondError(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	process.Status = updateReq.Status
	process.Output = updateReq.Output
	process.ExitCode = updateReq.ExitCode

	if updateReq.EndTime != "" {
		endTime, err := time.Parse(time.RFC3339, updateReq.EndTime)
		if err != nil {
			respondError(w, http.StatusBadRequest, "Invalid end time: "+err.Error())
			return
		}
		process.EndTime = endTime
	} else if process.Status == "completed" || process.Status == "failed" {
		process.EndTime = time.Now()
	}

	if err := r.db.UpdateProcess(process); err != nil {
		respondError(w, http.StatusInternalServerError, "Failed to update process: "+err.Error())
		return
	}

	respondJSON(w, http.StatusOK, process)
}
