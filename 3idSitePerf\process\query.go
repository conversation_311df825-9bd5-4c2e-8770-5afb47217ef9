package process

var h3iSitePerfSql = `
with subs_site as (
    select distinct
        dt,
        sbscrptn_ek_id,
        case when length(trim(site_id_90)) < 6 then lpad(trim(site_id_90),6,'0') else trim(site_id_90) end site_id
    from
        mis.ioh_subscriber_site_attribs_rolling a
    where
        dt >= ${start_dt}
    and dt < ${end_dt_exc}
),
packsub as (
    select
        a.periode_data dt,
        b.site_id,
        count(distinct a.sbscrptn_ek_id) subs_90d,
        sum(case when a.rgu30 = 1 then 1 else 0 end) subs_30d,
        sum(case when a.flag_pack_mtd in ('DATA PACK', 'VS PACK') then 1 else 0 end) subs_vsdpack,
        sum(case when a.flag_pack_mtd = 'DATA PACK' then 1 else 0 end) subs_datapack
    from
        analytics.mkt_rgu90d_base_and_pack a
        left join
        subs_site b
        on
            a.sbscrptn_ek_id = b.sbscrptn_ek_id
        and a.periode_data = b.dt
    where
        periode_data >= ${start_dt}
    and periode_data < ${end_dt_exc}
    group by 1, 2
),
datauu as (
    select
        a.load_dt_sk_id dt,
        b.site_id,
        count(distinct a.sbscrptn_ek_id) datauu30d
    from
        mis.project_ioh_data_user_daily a
        left join
        subs_site b
        on
            a.sbscrptn_ek_id = b.sbscrptn_ek_id
        and a.load_dt_sk_id = b.dt
    where
        a.load_dt_sk_id >= ${start_dt}
    and a.load_dt_sk_id < ${end_dt_exc}
    group by 1, 2
),
subs as (
    select
        to_char(a.dt_id, 'YYYYMMDD')::int dt_id,
        a.site_id,
        sum(case when a.kpi_id = 'SUB0017' then a.metric else null end) rgu90d,
        sum(case when a.kpi_id = 'SUB0061' then a.metric else null end) rgu90d_hvc,
        sum(case when a.kpi_id = 'SUB0008' then a.metric else null end) rgu90d_gross_churn,
        sum(case when a.kpi_id = 'CRN0005' then a.metric else null end) rgu90d_hvc_gross_churn,
        sum(case when a.kpi_id = 'SUB0011' then a.metric else null end) rgu90d_churn_back,
        sum(case when a.kpi_id = 'CRN0009' then a.metric else null end) rgu90d_hvc_churn_back
    from
        mis.hg_kpi_site_wise_dly a
    where
        a.kpi_id in ('SUB0061', 'SUB0017', 'SUB0008', 'CRN0005', 'SUB0011', 'CRN0009')
    and a.dt_id >= to_date(${start_dt}::text, 'YYYYMMDD')
    and a.dt_id < to_date(${end_dt_exc}::text, 'YYYYMMDD')
    group by 1, 2
),
outlet as (
    select
        periode_data dt,
        nearest_site site_id,
        count(distinct retailer_qrcode) outlet_cnt
    from
        dwh.retailer_hierarchy a
    where
        periode_data >= ${start_dt}
    and periode_data < ${end_dt_exc}
    and trx_type ='Owner'
    and type='ANGIE'
    and coalesce(nearest_site, '') != ''
    group by 1, 2  
),
kpi as (
    select
        load_dt_sk_id dt,
        case when length(site_id) <= 5 then lpad(site_id, 6, '0') else site_id end site_id,
        round(coalesce(sum(case when kpi_code = 'rev_mobo' then value else null end), sum(case when kpi_code = 'rev_mobo_ori' then value else null end)) +
            coalesce(sum(case when kpi_code = 'rev_nondata' then value else null end), sum(case when kpi_code = 'rev_nondata_ori' then value else null end)) +
            coalesce(sum(case when kpi_code = 'rev_organic' then value else null end), sum(case when kpi_code = 'rev_organic_ori' then value else null end)), 3) total_rev_gross,
        sum(case when a.kpi_code = 'rgu90_gross_add' then value else null end) rgu90_ga,
        sum(case when kpi_code = 'vlr_daily' then value else null end) vlr_subs,
        sum(case when kpi_code = 'rgu30_closing_base' then value else null end) rgu30d,
        sum(case when kpi_code = 'rgu90_closing_base' then value else null end) rgu90d,
        sum(case when kpi_code = 'rgu90_gross_churn' then value else null end) rgu90d_gross_churn,
        sum(case when kpi_code = 'rgu90_churn_back' then value else null end) rgu90d_churn_back,
        sum(case when kpi_code = 'sso_any' then value else null end) sso,
        sum(case when kpi_code = 'qsso_any' then value else null end) qsso,
        sum(case when kpi_code = 'uro_any' then value else null end) uro,
        sum(case when kpi_code = 'quro_any' then value else null end) quro,
        round(sum(case when kpi_code = 'voice traffic' then value else null end), 3) voice_traffic_min,
        round(sum(case when kpi_code = 'data traffic' then value/1024 else null end), 3) data_traffic_gb
    from
        mis.project_ioh_kpi_daily_tracker_site a
    where
        a.load_dt_sk_id >= ${start_dt}
    and a.load_dt_sk_id < ${end_dt_exc}
    group by 1, 2
)
select
    a.dt::varchar dt_id,
    a.site_id,
    '3ID' as brand,
    a.total_rev_gross,
    a.rgu90_ga rgu_ga,
    a.vlr_subs,
    a.rgu30d,
    d.rgu90d,
    d.rgu90d_hvc,
    d.rgu90d_gross_churn,
    d.rgu90d_hvc_gross_churn,
    d.rgu90d_churn_back,
    d.rgu90d_hvc_churn_back,
    a.sso,
    a.qsso,
    a.uro,
    a.quro,
    a.voice_traffic_min,
    a.data_traffic_gb,
    b.subs_vsdpack,
    b.subs_datapack,
    c.datauu30d,
    e.outlet_cnt
from
    kpi a
    left join
    packsub b
    on
        a.dt = b.dt
    and a.site_id = b.site_id
    left join
    datauu c
    on
        a.dt = c.dt
    and a.site_id = c.site_id
    left join
    subs d
    on
        a.dt = d.dt_id
    and a.site_id = d.site_id
    left join
    outlet e
    on
        a.dt = e.dt
    and a.site_id = e.site_id
order 
    by 1, 2
`

var h3iSitePerfUpdateSql = `
with new_data as (
    select
        a.dt_id,
        a.site_id,
        a.brand,
        a.total_rev_gross,
        a.rgu_ga,
        a.vlr_subs,
        a.rgu30d,
        a.rgu90d,
        a.rgu90d_hvc,
        a.rgu90d_gross_churn,
        a.rgu90d_hvc_gross_churn,
        a.rgu90d_churn_back,
        a.rgu90d_hvc_churn_back,
        a.sso,
        a.qsso,
        a.uro,
        a.quro,
        a.voice_traffic_min,
        a.data_traffic_gb,
        a.subs_vsdpack,
        a.subs_datapack,
        a.datauu30d,
        a.outlet_cnt
    from
        vbt.zz_3id_site_perf_dly_stg a
),
ex_data as (
    select
        a.dt_id,
        a.site_id,
        a.brand,
        a.total_rev_gross,
        a.rgu_ga,
        a.vlr_subs,
        a.rgu30d,
        a.rgu90d,
        a.rgu90d_hvc,
        a.rgu90d_gross_churn,
        a.rgu90d_hvc_gross_churn,
        a.rgu90d_churn_back,
        a.rgu90d_hvc_churn_back,
        a.sso,
        a.qsso,
        a.uro,
        a.quro,
        a.voice_traffic_min,
        a.data_traffic_gb,
        a.subs_vsdpack,
        a.subs_datapack,
        a.datauu30d,
        a.outlet_cnt
    from
        vbt.zz_3id_site_perf_dly a
        left join
        vbt.zz_3id_site_perf_dly_stg b
        on
            a.dt_id = b.dt_id
        and a.site_id = b.site_id
    where
        b.dt_id is null
),
gbg as (
    select
        *
    from
        ex_data
    union all
    select
        *
    from
        new_data
)
insert overwrite table vbt.zz_3id_site_perf_dly (
    dt_id,
    site_id,
    brand,
    total_rev_gross,
    rgu_ga,
    vlr_subs,
    rgu30d,
    rgu90d,
    rgu90d_hvc,
    rgu90d_gross_churn,
    rgu90d_hvc_gross_churn,
    rgu90d_churn_back,
    rgu90d_hvc_churn_back,
    sso,
    qsso,
    uro,
    quro,
    voice_traffic_min,
    data_traffic_gb,
    subs_vsdpack,
    subs_datapack,
    datauu30d,
    outlet_cnt,
    month
)
select 
    a.dt_id,
    a.site_id,
    a.brand,
    a.total_rev_gross,
    a.rgu_ga,
    a.vlr_subs,
    a.rgu30d,
    a.rgu90d,
    a.rgu90d_hvc,
    a.rgu90d_gross_churn,
    a.rgu90d_hvc_gross_churn,
    a.rgu90d_churn_back,
    a.rgu90d_hvc_churn_back,
    a.sso,
    a.qsso,
    a.uro,
    a.quro,
    a.voice_traffic_min,
    a.data_traffic_gb,
    a.subs_vsdpack,
    a.subs_datapack,
    a.datauu30d,
    a.outlet_cnt,
    CAST(substr(a.dt_id, 1, 6) as INT) as month
from 
    gbg a
`
