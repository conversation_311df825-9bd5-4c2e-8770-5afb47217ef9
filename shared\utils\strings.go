package utils

import (
	"strconv"
	"strings"
	"unicode"
)

func StringToInt(s string) int64 {
	i, _ := strconv.ParseInt(s, 10, 64)
	return i
}

func StringToFloat(s string) float64 {
	f, _ := strconv.ParseFloat(s, 64)
	return f
}

func StringToBool(s string) bool {
	s = strings.ToLower(s)
	switch s {
	case "true", "t", "yes", "y":
		return true
	}
	return false
}

func StringToStringSlice(s string, sep string) []string {
	strArr := strings.Split(s, sep)
	return SliceMap(strArr, func(s string) string { return strings.TrimSpace(s) })
}

func ToPtr[T any](v T) *T {
	return &v
}

func PtrToValCopy[T any](v T) *T {
	copyVal := v
	return &copyVal
}

func ConvertStringValue(val string, existing any) (any, error) {
	switch existing.(type) {
	case int:
		return strconv.Atoi(val)
	case int64:
		return strconv.ParseInt(val, 10, 64)
	case string:
		return val, nil
	case float32, float64:
		return strconv.ParseFloat(val, 64)
	case []string:
		return StringToStringSlice(val, ";"), nil
	case []int:
		return SliceMap(StringToStringSlice(val, ";"), func(s string) int { return int(StringToInt(s)) }), nil
	case []int64:
		return SliceMap(StringToStringSlice(val, ";"), func(s string) int64 { return StringToInt(s) }), nil
	}

	return val, nil
}

func ToSnakeCase(input string) string {
	var result strings.Builder
	length := len(input)
	result.Grow(length + 5) // Pre-allocate some space for efficiency

	lastWasUnderscore := false

	for i, r := range input {
		if unicode.IsUpper(r) {
			// Add an underscore before the uppercase letter (if it's not the first character)
			if i > 0 && !lastWasUnderscore {
				result.WriteByte('_')
			}
			result.WriteRune(unicode.ToLower(r))
			lastWasUnderscore = false
		} else if unicode.IsSpace(r) || r == '-' || r == '_' {
			// Replace spaces, hyphens, and existing underscores with a single underscore
			if !lastWasUnderscore && result.Len() > 0 {
				result.WriteByte('_')
				lastWasUnderscore = true
			}
		} else {
			result.WriteRune(r)
			lastWasUnderscore = false
		}
	}

	// Remove any trailing underscores
	snake := result.String()
	if len(snake) > 0 && snake[len(snake)-1] == '_' {
		snake = snake[:len(snake)-1]
	}

	return snake
}
