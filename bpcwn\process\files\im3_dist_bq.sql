with dse as (
    select distinct
        username
        ,case
            when trim(region) = 'NORTHERN SUMATERA' then 'NORTH SUMATERA'
            when trim(region) = 'SOUTHERN SUMATERA' then 'SOUTH SUMATERA'
            else trim(region)
        end region
        ,month
    from
        `data-nationalslsdist-prd-986g.datamart.outlet_cso_mapping` a
    where
        a.month >= format_date('%Y%m', date_add(date(${mtd_dt}), interval -1 month))
),
region_map as (
    select distinct
        region,
        circle
    from
        `data-bi-prd-935c.bi_dm.ref_kecamatan`
)
select
    a.dt
    ,a.brand
	,circle
    ,region
    ,case
        when a.parameter in ('Site 3-QSSO M0', 'site_3qsso_new') then 'Site 3-QSSO'
        when a.parameter='RGUGA-Trad' then 'RGU GA'
        when a.parameter='site_5quro' then 'Site 5-QURO'
        when a.parameter='secondary' then 'Secondary'
        else a.parameter
    end parameter
    ,sum(case when mthf='lmtd' then a.amount else 0 end) lmtd
    ,sum(case when mthf='mtd' then a.amount else 0 end) mtd
from
    (
        select
          a.kec_unik
          ,parse_date('%Y%m%d', a.dt_id) dt
          ,a.as_of_dt
          ,a.mth
          ,a.parameter
          ,a.mthf
          ,brand
          ,sum(a.amount) amount
        from
          `data-nationalslsdist-prd-986g.datamart.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2` a
        where
          dt_id = format_date('%Y%m%d', date(${mtd_dt}))
        and upper(mthf) in ('MTD', 'LMTD')
        and parameter in (
          'Site 3-QSSO M0',
          'Site 5-QURO',
          'Secondary',
          'RGUGA-Trad',
          'site_3qsso_new',
          'site_5quro',
          'secondary'
        )
        group by
          1,2,3,4,5,6,7
    ) a
left join
    `data-bi-prd-935c.bi_dm.ref_kecamatan` kec
    on
        a.kec_unik = kec.kec_kabkot
where
    a.parameter is not null
group by
    1,2,3,4,5

union all

--GAD AVG<1
select
    date(${mtd_dt}) dt
    ,'IM3' brand
    ,site.circle
    ,site.region_circle region
    ,'Site w/ <1 GAD/Day' parameter
    ,count(distinct case when upper(mthf) = 'LMTD' and (coalesce(a.rgu_ga,0) / extract(day from date(${mtd_dt}))) < 1 then a.site_id else null end) lmtd
    ,count(distinct case when upper(mthf) = 'MTD' and(coalesce(a.rgu_ga,0) / extract(day from date(${mtd_dt}))) < 1 then a.site_id else null end) mtd
from
    (
        select distinct
            site_id,
            row_number() over (partition by site_id order by mth desc) rnk
        from
            `data-nationalslsdist-prd-986g.datamart.nshim3_tbl_ref_addrsite_gtm_mth` a
        where
            a.mth >= format_date('%Y%m', date_add(date(${mtd_dt}), interval -1 month))
        and trim(addressable_type) = 'ADDRESSABLE SITE'
        and list_im3 = 'Y'
    ) site_ref
    left join
    (
		select
		    a.site_id
			,case
			    when format_date('%Y%m', date(a.dt_id)) = format_date('%Y%m', date(${mtd_dt})) then 'MTD'
				else 'LMTD'
			end mthf
			,format_date('%Y%m', date(a.dt_id)) mth
			,count(a.msisdn) rgu_ga
		from
		    `data-bi-prd-935c.bi_dm.umr_rgs_ga_90d_dly_govt` a
			join
		    `data-bi-prd-935c.bi_dm.umr_rgs_ga_channel_mth_govt` b
		    on
		        a.msisdn = b.msisdn
		    and a.dt_id = timestamp(parse_date('%Y%m%d', b.ga_dt))
		where
			a.dt_id >= timestamp(date_trunc(date_add(date(${mtd_dt}), interval -1 month), month))
		and a.dt_id <= timestamp(date(${mtd_dt}))
		and extract(day from a.dt_id) <= extract(day from date(${mtd_dt}))
		and (a.churn_back = 'NO' OR a.recycled = 'YES')
		group by
		    1,2,3
	) a
    on
        site_ref.site_id = a.site_id
    and site_ref.rnk = 1
    left join
    `data-bi-prd-935c.bi_dm.ref_site` site
    on
        a.site_id = site.site_id
group by
    1,3,4

union all
--DSE WITH <12 GAD/DAY
select
    date(${mtd_dt}) dt
    ,'IM3' brand
    ,b.circle
    ,dse.region
    ,'DSE w/ <12 GAD' parameter
    ,count(distinct case when upper(mthf) = 'LMTD' and (coalesce(a.rgu_ga,0) / extract(day from date(${mtd_dt}))) < 12 then dse.username else null end) lmtd
    ,count(distinct case when upper(mthf) = 'MTD' and (coalesce(a.rgu_ga,0) / extract(day from date(${mtd_dt}))) < 12 then dse.username else null end) mtd
from
    dse
    left join
    (
        select
            c.username
            ,case
                when format_date('%Y%m', date(a.dt_id)) = format_date('%Y%m', date(${mtd_dt})) then 'MTD'
                else 'LMTD'
            end mthf
            ,format_date('%Y%m', date(a.dt_id)) mth
            ,count(a.msisdn) rgu_ga
        from
            `data-bi-prd-935c.bi_dm.umr_rgs_ga_90d_dly_govt` a
            join
            `data-bi-prd-935c.bi_dm.umr_rgs_ga_channel_mth_govt` b
            on
                a.msisdn = b.msisdn
            and a.dt_id = timestamp(parse_date('%Y%m%d', b.ga_dt))
            left join
            `data-nationalslsdist-prd-986g.datamart.outlet_cso_mapping` c
            on
                b.organization_id = c.id_outlet
            and c.month = format_date('%Y%m', date(${mtd_dt}))
        where
            dt_id >= timestamp(date_trunc(date_add(date(${mtd_dt}), interval -1 month), month))
        and dt_id <= ${mtd_dt}
        and extract(day from a.dt_id) <= extract(day from date(${mtd_dt}))
        and (churn_back = 'NO' OR recycled = 'YES')
        group by 1,2,3
	) a
    on
        dse.username = a.username
    and dse.month = a.mth
    left join
    (
        select distinct
            region,
            circle
        from
            `data-bi-prd-935c.bi_dm.ref_kecamatan`
    ) b
    on
        dse.region = b.region
group by
    1,3,4

union all
--DSE WITH <5 MN/DAY
select
    date(${mtd_dt}) dt
    ,'IM3' brand
    ,b.circle
    ,dse.region
    ,'DSE w/ <5 Mn Secondary' parameter
    ,count(distinct case when upper(mthf) = 'LMTD' and (coalesce(a.amount,0) / extract(day from date(${mtd_dt}))) < 5000000 then dse.username else null end) lmtd
    ,count(distinct case when upper(mthf) = 'MTD' and (coalesce(a.amount,0) / extract(day from date(${mtd_dt}))) < 5000000 then dse.username else null end) mtd
from
    dse
    left join
    (
	    select
	        username
	        ,case
	            when format_timestamp('%Y%m', a.dt_id) = format_date('%Y%m', date(${mtd_dt})) then 'MTD'
            	else 'LMTD'
            end mthf
            ,format_timestamp('%Y%m', a.dt_id) mth
			,sum(a.amount)/1.11 amount
        from
            `data-bi-prd-935c.bi_dm.omn_secondary` a
            left join
            `data-nationalslsdist-prd-986g.datamart.outlet_cso_mapping` c
            on
                a.credit_party_id = c.id_outlet
            and c.month = format_date('%Y%m', date(${mtd_dt}))
        where
            a.dt_id >= timestamp(date_trunc(date_add(date(${mtd_dt}), interval -1 month), month))
        and a.dt_id <= timestamp(date(${mtd_dt}))
        and extract(day from a.dt_id) <= extract(day from date(${mtd_dt}))
        group by
            1,2,3
	) a
	on
	    dse.username = a.username
	and dse.month = a.mth
    left join
    region_map b
    on
        dse.region = b.region
group by
    1,3,4

union all
--TOTAL DSE
select
    date(${mtd_dt}) dt
    ,'IM3' brand
    ,b.circle
    ,dse.region
    ,'DSE' parameter
    ,count(distinct case when month = format_date('%Y%m', date_add(date(${mtd_dt}), interval -1 month)) then username else null end) lmtd
    ,count(distinct case when month = format_date('%Y%m', date(${mtd_dt})) then username else null end) mtd
from
    dse
    left join
    region_map b
    on
        dse.region = b.region
group by
    1,3,4

union all
--TOTAL MITRA 3KIOSK
select
	date(${mtd_dt}) dt_id,
    brand,
    circle,
    region,
	'SDP/3KIOSK' parameter,
	count(distinct
        case when mth = format_date('%Y%m', date_add(date(${mtd_dt}), interval -1 month))
			and ((brand='IM3' AND pt_type in ('SDP', 'MITRAIM3')) or (brand='3ID' and pt_type='3KIOSK')) then pt_id
			else null
        end) lmtd
	,count(distinct
        case when mth = format_date('%Y%m', date(${mtd_dt}))
            and ((brand='IM3' AND pt_type in ('SDP', 'MITRAIM3')) or (brand='3ID' and pt_type='3KIOSK')) then pt_id
			else  null
    end) mtd
from
    `data-nationalslsdist-prd-986g.datamart.bai_tbl_ref_hirarki_territory_ioh_v1` a
where
	mth >= format_date('%Y%m', date_add(date(${mtd_dt}), interval -1 month))
and pt_type in ('SDP', 'MITRAIM3', '3KIOSK')
group by 1,2,3,4