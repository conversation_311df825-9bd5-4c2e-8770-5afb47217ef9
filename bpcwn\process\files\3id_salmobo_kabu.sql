with outlet_salmo_dly as (
    select
        (snp_dt_sk_id/100)::varchar month_id,
        snp_dt_sk_id dt_id,
        cast(substr(snp_dt_sk_id::varchar, 7, 2) as int) day_int,
        cast((cast(substr(snp_dt_sk_id::varchar, 7, 2) as int) - 1) / 7 as int) + 1 wk,
        a.qr_code organization_id,
        e.circle,
        e.region_circle region,
        e.kabkot_nm,
        kpi_value saldo_amount
    from
        marketing.trd_trisakti_movement_detail a
        left join
        (
            select	ret_qr_cd qr_code
                    ,dt
                    ,site_id
                    ,row_number() over (partition by ret_qr_cd order by dt desc) rn
            from	mis.stg_v_map_tgt_ret_details_hist
            where	site_id!=''
                and dt >= to_char(date_trunc('month', to_date(${end_dt_int}::varchar, 'YYYYMMDD') + interval '-3 month'), 'YYYYMMDD')::int
                and dt <= ${end_dt_int}
        ) d
        on a.qr_code = d.qr_code
        and d.rn=1
        left join
        ioh_biadm.ref_site_h3i e
        on
            d.site_id = e.site_id
    where
        type='Retailer'
    and kpi_name ='DB Closing Balance'
    and snp_dt_sk_id >= to_char(date_trunc('month', to_date(${end_dt_int}::varchar, 'YYYYMMDD') + interval '-3 month'), 'YYYYMMDD')::int
    and snp_dt_sk_id <= ${end_dt_int}
),
pjp_outlets as (
    select distinct
        mth_id month_id,
        retailer_qrcode outlet_id
    from
        marketing.snd_outlet_mapping
    where
        mth_id >= to_char(date_trunc('month', to_date(${end_dt_int}::varchar, 'YYYYMMDD') + interval '-3 month'), 'YYYYMM')
    and mth_id <= substr(${end_dt_int}::varchar,1,6)
),
outlet_salmo_avg as (
    select
        a.month_id,
        case when a.wk > 4 then 4 else a.wk end wk,
        a.organization_id,
        a.circle,
        a.region,
        a.kabkot_nm,
        round(avg(a.saldo_amount), 3) saldo_amount,
        ${bin3idStr} slab
    from
        outlet_salmo_dly a
    group by 1,2,3,4,5,6
)
select
    a.month_id,
    wk,
    a.circle,
    a.region,
    a.kabkot_nm kabupaten,
    '3ID' brand,
    ${bin3idCntStr}
from
    outlet_salmo_avg a
    inner join
    pjp_outlets p
    on
        p.outlet_id = a.organization_id
    and p.month_id = a.month_id
group by 1,2,3,4,5
order by 1,2,3,4,5
