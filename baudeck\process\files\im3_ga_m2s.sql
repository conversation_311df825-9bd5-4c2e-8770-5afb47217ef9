select
    substr(a.dt_id, 1, 6) month_id,
    case when a.dt_id = ${mtd_dt_id} then 'MTD' else 'LMTD' end period,
    a.dt_id asof_date,
    b.circle,
    b.region_circle region,
    b.kabkot_nm kabupaten,
    'IM3' as brand,
    cast(sum(case when kpi_code = 'acq_ga' then metric else 0 end) as int) ga,
    cast(sum(case when kpi_code = 'acq_qoa_m2s' then metric else 0 end) as int) m2s
from
    biadm.hg_kpi_site_wise_dly a
    left join
    biadm.ref_site b
    on
        a.site_id = b.site_id
where
    a.kpi_code in ('acq_qoa_m2s', 'acq_ga')
and brand = 'IM3'
and a.dt_id in (${mtd_dt_id}, from_timestamp(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -1 month), 'yyyyMMdd'))
group by
    1, 2, 3, 4, 5, 6

union all

select
    substr(dt_id, 1, 6) month_id,
    'FM' period,
    a.dt_id asof_date,
    b.circle,
    b.region_circle region,
    b.kabkot_nm kabupaten,
    'IM3' as brand,
    cast(sum(case when kpi_code = 'acq_ga' then metric else 0 end) as int) ga,
    cast(sum(case when kpi_code = 'acq_qoa_m2s' then metric else 0 end) as int) m2s
from
    biadm.hg_kpi_site_wise_dly a
    left join
    biadm.ref_site b
    on
        a.site_id = b.site_id
where
    kpi_code in ('acq_qoa_m2s', 'acq_ga')
and brand = 'IM3'
and dt_id >= from_timestamp(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -6 month), 'yyyyMMdd')
and dt_id < from_timestamp(trunc(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), 'month'), 'yyyyMMdd')
and dt_id = from_timestamp(date_add(date_add(trunc(to_timestamp(a.dt_id, 'yyyyMMdd'), 'month'), interval 1 month), interval -1 day), 'yyyyMMdd')
group by
    1, 2, 3, 4, 5, 6