with reff as
(
    select distinct kecamatan,region,circle, case when instr(kecamatan, '|') > 0 then split_part(kecamatan, '|', 2) else null end kabupaten
    from rdm.bai_tbl_ref_hirarki_territory_ioh_v1
    where mth = substr(${mtd_dt_id},1,6) and brand='3ID'
)
select
    upper(a.mthf) period,
    a.brand,
    reff.circle,
    reff.region,
    reff.kabupaten,
    sum(a.amount) osa
from
    rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
    left join
    reff
    on
        a.kec_unik = reff.kecamatan
where
    a.parameter  in ('osa','OSA')
and a.mthf in ('mtd', 'lmtd')
and a.dt_id = ${mtd_dt_id}
group by 1, 2, 3, 4, 5
