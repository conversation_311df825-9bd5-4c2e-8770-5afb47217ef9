with site_ref as (
    select
		t0.site_id,
		t0.site_nm,
		t0.circle,
		t0.region_circle,
		t0.kabkot_nm,
		t0.kecamatan_nm,
		t0.kecamatan_id,
		t1.rural_urban_kecamatan,
		t0.longitude,
		t0.latitude
	from
	(
		select
			coalesce(a.site_id,b.site_id) site_id,
			coalesce(a.site_nm,b.site_nm) site_nm,
			COALESCE(a.circle, b.circle) circle,
			coalesce(a.region_circle, b.region_circle) region_circle,
			COALESCE(a.kabkot_nm, b.kabkot_nm) kabkot_nm,
			coalesce(a.kecamatan_nm,b.kecamatan_nm) kecamatan_nm,
			concat(coalesce(a.kecamatan_nm,b.kecamatan_nm),'|',coalesce(a.kabkot_nm,b.kabkot_nm)) kecamatan_id,
			coalesce(a.longitude,SAFE_CAST(b.longitude as float64)) longitude,
			coalesce(a.latitude,SAFE_CAST(b.latitude as float64)) latitude
		from
			`data-bi-prd-935c.bi_dm.ref_site` a
			full join
			`data-bi-prd-935c.bi_dm.ref_site_h3i` b
			on
				a.site_id = b.site_id
	) t0
	left join
	(
		select
			kec_kabkot,
			kecamatan_nm,
			rural_urban_kecamatan
		from
			`data-bi-prd-935c.bi_dm.ref_kecamatan`
	) t1
	on
		t0.kecamatan_id = t1.kec_kabkot
),
kabu_kpi_dly as (
    select
		a.dt_id,
		coalesce(r.circle, 'NULL') circle,
		coalesce(r.region_circle, 'NULL') region_circle,
		coalesce(r.kabkot_nm, 'NULL') kabupaten,
		case when timestamp_trunc(a.dt_id, month) = timestamp_trunc(${mtd_dt}, month) then 'MTD' else 'LMTD' end period,
        sum(case when kpi_code = 'acq_ga' then metric else 0 end) acq_ga,
		sum(case when kpi_code = 'acq_qsc_m0' then metric else 0 end) acq_qsc_m0,
		sum(case when kpi_code = 'primary_trad' then metric else 0 end) primary_trad,
		sum(case when kpi_code = 'ret_qsso' then metric else 0 end) ret_qsso,
		sum(case when kpi_code = 'ret_quro' then metric else 0 end) ret_quro,
		sum(case when kpi_code = 'ret_sso_mtd' then metric else 0 end) ret_sso_mtd,
		sum(case when kpi_code = 'ret_uro_mtd' then metric else 0 end) ret_uro_mtd,
		sum(case when kpi_code = 'rgu30_base' then metric else 0 end) rgu30_base,
		sum(case when kpi_code = 'rgu30_churn_back' then metric else 0 end) rgu30_churn_back,
		sum(case when kpi_code = 'rgu30_gross_add' then metric else 0 end) rgu30_gross_add,
		sum(case when kpi_code = 'rgu30_gross_churn' then metric else 0 end) rgu30_gross_churn,
		sum(case when kpi_code = 'rgu30_inflow' then metric else 0 end) rgu30_inflow,
		sum(case when kpi_code = 'rgu90_base' then metric else 0 end) rgu90_base,
		sum(case when kpi_code = 'rgu90_churn_back' then metric else 0 end) rgu90_churn_back,
		sum(case when kpi_code = 'rgu90_gross_add' then metric else 0 end) rgu90_gross_add,
		sum(case when kpi_code = 'rgu90_gross_churn' then metric else 0 end) rgu90_gross_churn,
		sum(case when kpi_code = 'rgu90_inflow' then metric else 0 end) rgu90_inflow,
		sum(case when kpi_code = 'sec_sell_ret_trad' then metric else 0 end) sec_sell_ret_trad,
		sum(case when kpi_code = 'ter_sell_nontrad' then metric else 0 end) ter_sell_nontrad,
		sum(case when kpi_code = 'ter_sell_trad' then metric else 0 end) ter_sell_trad,
		sum(case when kpi_code = 'ter_sell_trad_saldo' then metric else 0 end) ter_sell_trad_saldo,
		sum(case when kpi_code = 'ter_sell_trad_sp' then metric else 0 end) ter_sell_trad_sp,
		sum(case when kpi_code = 'ter_sell_trad_vou' then metric else 0 end) ter_sell_trad_vou,
		sum(case when kpi_code = 'vlr_daily' then metric else 0 end) vlr_daily,
		sum(case when kpi_code = 'rev_data_mobo' then metric else 0 end) rev_data_mobo,
		sum(case when kpi_code = 'rev_data_organic' then metric else 0 end) rev_data_organic,
		sum(case when kpi_code = 'rev_total' then metric else 0 end) total_rev,
		sum(case when kpi_code = 'usg_data_gb' then metric else 0 end) traffic_gb
    from
    	`data-bi-prd-935c`.bi_dm.hg_kpi_site_wise_dly a
        left join
    	site_ref r
        on
            a.site_id = r.site_id
    where
    	a.dt_id >= timestamp(date_trunc(date_add(${mtd_dt}, interval -1 month), month))
    and a.dt_id <= ${mtd_dt}
    and extract(day from a.dt_id) <= extract(day from date(${mtd_dt}))
    and a.brand = 'IM3'
    group by 1, 2, 3, 4, 5
),
kabu_kpi_max_date as (
    select
        format_timestamp('%Y%m', a.dt_id) month_id,
        max(a.dt_id) last_date
    from
        kabu_kpi_dly a
    group by 1
),
kabu_kpi_mth_sum as (
    select
        format_timestamp('%Y%m', a.dt_id) month_id,
        a.period,
        a.circle,
        a.region_circle,
        a.kabupaten,
        sum(a.acq_qsc_m0) acq_qsc_m0,
        sum(a.primary_trad) primary_trad,
        sum(a.sec_sell_ret_trad) sec_sell_ret_trade,
        sum(a.ter_sell_nontrad) ter_sell_nontrad,
        sum(a.ter_sell_trad) ter_sell_trad,
        sum(a.ter_sell_trad_sp) ter_sell_trad_sp,
        sum(a.ter_sell_trad_vou) ter_sell_trad_vou,
        sum(a.rev_data_mobo) rev_data_mobo,
        sum(a.rev_data_organic) rev_data_organic,
        sum(a.total_rev) total_rev,
        sum(a.traffic_gb) traffic_gb
    from
        kabu_kpi_dly a
    group by 1, 2, 3, 4, 5
),
kabu_kpi_mth_snap as (
    select
        format_timestamp('%Y%m', a.dt_id) month_id,
        a.period,
        a.circle,
        a.region_circle,
        a.kabupaten,
        a.dt_id,
        a.acq_ga,
        a.acq_qsc_m0,
        a.ret_qsso,
        a.ret_quro,
        a.ret_sso_mtd,
        a.ret_uro_mtd,
        a.rgu30_base,
        a.rgu30_churn_back,
        a.rgu30_gross_add,
        a.rgu30_gross_churn,
        a.rgu30_inflow,
        a.rgu90_base,
        a.rgu90_churn_back,
        a.rgu90_gross_add,
        a.rgu90_gross_churn,
        a.rgu90_inflow,
        a.vlr_daily
    from
        kabu_kpi_dly a
        inner join
        kabu_kpi_max_date m
        on
            a.dt_id = m.last_date
),
nbs as (
    select site_id,organization_id, org_nm organization_name,region,circle, mth
    from
    (
        select
            site_id,
            organization_id,
            upper(organization_name) org_nm,
            region,
            case
            when region in ('INNER JAKARTA', 'OUTER JAKARTA', 'WEST JAVA') then 'JAKARTA RAYA'
            when region in ('CENTRAL JAVA', 'EAST JAVA', 'BALI NUSRA') then 'JAVA'
            when region in ('KALIMANTAN', 'MAPA', 'SULAWESI') then 'KALISUMAPA'
            when region in ('NORTH SUMATERA', 'SOUTH SUMATERA', 'CENTRAL SUMATERA') then 'SUMATERA'
            else 'NULL'
            end circle,
            format_timestamp('%Y%m', dt_id) mth,
            ROW_NUMBER() OVER (partition by organization_id, timestamp_trunc(dt_id,month) ORDER BY dt_id desc) AS rn
        from
            `data-bi-prd-935c.bi_dm.omn_outlet_loc_ns`
        where
            dt_id between timestamp(date_trunc(date_add(date(${mtd_dt}), interval -1 month), month)) and ${mtd_dt}
    ) a where rn=1
)
select * from nbs
