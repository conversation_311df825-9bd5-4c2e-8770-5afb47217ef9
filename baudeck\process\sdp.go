package process

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"io/fs"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"

	"gopkg.in/guregu/null.v4"

	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
)

type SdpProcess struct {
	procFS fs.ReadFileFS
}

func NewSdpProcess(procFS fs.ReadFileFS) SdpProcess {
	return SdpProcess{
		procFS: procFS,
	}
}

func (sdp SdpProcess) GetReportData(c context.Context, workDate time.Time) (SdpDseReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	logger.Info("Running SDP Report")

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return SdpDseReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var dseData []DseData
	var dseReport *DseReport
	var sdpData []SdpData
	var sdpReport *SdpReport

	var g *errgroup.Group
	g, cCancel = errgroup.WithContext(cCancel)

	//var wg sync.WaitGroup
	//
	//wg.Add(1)
	//dseResult := channel.RunAsyncContext(cCancel, func() ([]DseData, error) {
	//	if cfg.UseDSEFromFile != "" {
	//		return sdp.GetDSEFromFile(cCancel, cfg.UseDSEFromFile)
	//	}
	//	return sdp.GetDseData(cCancel)
	//})
	//
	//go func() {
	//	defer wg.Done()
	//	for res := range dseResult {
	//		res.Map(func(data []DseData) {
	//			dseData = data
	//			dseReport, err = sdp.postProcessDseData(data)
	//			if err != nil {
	//				logger.Error("failed to process DSE data", "error", err)
	//				cancel()
	//			}
	//		}).MapErr(func(er error) {
	//			err = fmt.Errorf("failed to get DSE data. %s", er)
	//			logger.Debug("calling cancel", "caller", "sdp.GetDseData", "error", err)
	//			cancel()
	//		})
	//	}
	//}()

	//wg.Add(1)
	//sdpResult := channel.RunAsyncContext(cCancel, func() ([]SdpData, error) {
	//	if cfg.UseSDPFromFile != "" {
	//		return sdp.GetSdpFromFile(cCancel, cfg.UseSDPFromFile)
	//	}
	//	return sdp.GetSdpData(cCancel)
	//})
	//
	//go func() {
	//	defer wg.Done()
	//	for res := range sdpResult {
	//		res.Map(func(data []SdpData) {
	//			sdpData = data
	//			sdpReport, err = sdp.postProcessSdpData(data)
	//			if err != nil {
	//				logger.Error("failed to process SDP data", "error", err)
	//				cancel()
	//			}
	//		}).MapErr(func(er error) {
	//			err = fmt.Errorf("failed to get SDP data. %s", er)
	//			logger.Debug("calling cancel", "caller", "sdp.GetSdpData", "error", err)
	//			cancel()
	//		})
	//	}
	//}()
	//
	//wg.Wait()
	//if err != nil {
	//	return SdpDseReport{}, err
	//}

	g.Go(func() error {
		var errg error
		dseData, errg = sdp.GetDseData(cCancel)
		if errg != nil {
			return fmt.Errorf("failed to get DSE data. %s", errg)
		}

		dseReport, errg = sdp.postProcessDseData(dseData)
		if errg != nil {
			return fmt.Errorf("failed to process DSE data. %s", errg)
		}
		return errg
	})

	g.Go(func() error {
		var errg error
		sdpData, errg = sdp.GetSdpData(cCancel)
		if errg != nil {
			return fmt.Errorf("failed to get SDP data. %s", errg)
		}

		sdpReport, errg = sdp.postProcessSdpData(sdpData)
		if errg != nil {
			return fmt.Errorf("failed to process SDP data. %s", errg)
		}
		return errg
	})

	if err := g.Wait(); err != nil {
		return SdpDseReport{}, err
	}

	workDir := ctx.ExtractWorkDir(c)

	dseCsvFilePath := fmt.Sprintf("%s/dse_%s.csv", workDir, time.Now().Format("20060102150405"))
	err = utils.WriteToCsv(dseCsvFilePath, dseData)
	if err != nil {
		logger.Error("failed to write CSV file", "path", dseCsvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", dseCsvFilePath)
	}

	sdpCsvFilePath := fmt.Sprintf("%s/sdp_%s.csv", workDir, time.Now().Format("20060102150405"))
	err = utils.WriteToCsv(sdpCsvFilePath, sdpData)
	if err != nil {
		logger.Error("failed to write CSV file", "path", sdpCsvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", sdpCsvFilePath)
	}

	zipFilePath := fmt.Sprintf("%s/sdp_dse_%s.zip", workDir, time.Now().Format("20060102150405"))
	f, err := os.Create(zipFilePath)
	if err != nil {
		logger.Error("failed to create zip file", "path", zipFilePath, "error", err)
	} else {
		defer f.Close()

		err = utils.PackZip(f, dseCsvFilePath, sdpCsvFilePath)
		if err != nil {
			logger.Error("failed to zip files", "path", zipFilePath, "error", err)
		} else {
			logger.Info("Zip file written", "path", zipFilePath)
		}
	}

	zipBuffer := new(bytes.Buffer)
	if err := utils.PackZip(zipBuffer, dseCsvFilePath, sdpCsvFilePath); err != nil {
		logger.Error("failed to zip files", "error", err)
	}

	return SdpDseReport{
		Sdp:               sdpReport,
		Dse:               dseReport,
		RawDataZipContent: zipBuffer.Bytes(),
	}, nil
}

func (sdp SdpProcess) GetDseData(c context.Context) ([]DseData, error) {
	conf := ctx.ExtractConfig(c)

	mtdDate := conf.GetTime("work_date")
	mtdDateInt, err := strconv.Atoi(mtdDate.Format("20060102"))
	if err != nil {
		return nil, err
	}

	var im3Data []DseData
	var threeData []DseData

	g, c := errgroup.WithContext(c)

	g.Go(func() error {
		data, errg := sdp.getIm3DseData(c, mtdDate.Format("20060102"))
		if errg != nil {
			return fmt.Errorf("failed to get IM3 DSE data. %s", errg)
		}
		im3Data = data
		return nil
	})

	g.Go(func() error {
		data, errg := sdp.get3idDseData(c, mtdDateInt)
		if errg != nil {
			return fmt.Errorf("failed to get 3ID DSE data. %s", errg)
		}
		threeData = data
		return nil
	})

	if err := g.Wait(); err != nil {
		return nil, err
	}

	result := append(im3Data, threeData...)
	for i, d := range result {
		result[i].GASlab = getDseGaBinLabel(d.GrossAdds)
		result[i].NetSecSlab = getDseSecBinLabel(d.NetSecondaryMn, d.Brand)
	}

	return result, nil
}

func (sdp SdpProcess) GetDSEFromFile(c context.Context, fpath string) ([]DseData, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()
	var data []DseData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		ga, err := strconv.Atoi(record[7])
		if err != nil {
			return nil, fmt.Errorf("failed to parse GA. %s", err)
		}

		netSec, err := strconv.ParseFloat(record[8], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse Net Secondary Mn. %s", err)
		}

		dayNum, err := strconv.Atoi(record[9])
		if err != nil {
			return nil, fmt.Errorf("failed to parse Day Num. %s", err)
		}

		data = append(data, DseData{
			MonthID:        record[0],
			Period:         record[1],
			AsofDate:       record[2],
			Brand:          record[3],
			Circle:         null.StringFrom(record[4]),
			Region:         null.StringFrom(record[5]),
			Dse:            record[6],
			GrossAdds:      ga,
			GASlab:         getDseGaBinLabel(ga),
			NetSecondaryMn: netSec,
			NetSecSlab:     getDseSecBinLabel(netSec, record[3]),
			DayNum:         dayNum,
		})
	}

	return data, nil
}

func (sdp SdpProcess) getIm3DseData(c context.Context, mtdDate string) ([]DseData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/im3_dse.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read im3_dse.sql. %s", err)
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate},
	}

	logger.Info("Getting IM3 DSE data")
	return etlProc.QueryImpalaData[DseData](c, string(buf), params)
}

func (sdp SdpProcess) get3idDseData(c context.Context, mtdDate int) ([]DseData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/3id_dse.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read 3id_dse.sql. %s", err)
	}

	logger.Info("getting 3ID DSE data")

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDate},
	}

	return etlProc.QueryGreenplumData[DseData](c, string(buf), params)
}

func (sdp SdpProcess) GetSdpData(c context.Context) ([]SdpData, error) {
	conf := ctx.ExtractConfig(c)

	mtdDate := conf.GetTime("work_date")
	//mtdDateInt, err := strconv.Atoi(mtdDate.Format("20060102"))
	//if err != nil {
	//	return nil, err
	//}

	var im3Data []SdpData
	var threeData []SdpData

	g, c := errgroup.WithContext(c)

	g.Go(func() error {
		data, errg := sdp.getIm3SdpData(c, mtdDate)
		if errg != nil {
			return fmt.Errorf("failed to get IM3 SDP data. %s", errg)
		}
		im3Data = data
		return nil
	})

	g.Go(func() error {
		data, errg := sdp.get3idSdpData(c, mtdDate)
		if errg != nil {
			return fmt.Errorf("failed to get 3ID SDP data. %s", errg)
		}
		threeData = data
		return nil
	})

	if err := g.Wait(); err != nil {
		return nil, err
	}

	kabumap, err := etlProc.GetMicroMktMap(c)
	if err != nil {
		return nil, err
	}

	data := append(im3Data, threeData...)
	for i := range data {
		kabu := strings.TrimSpace(strings.ToUpper(data[i].Kabupaten.String))
		if segment, ok := kabumap[kabu]; ok {
			data[i].KabuFlag = null.StringFrom(segment)
		}
	}

	return data, nil
}

func (sdp SdpProcess) GetSdpFromFile(c context.Context, fpath string) ([]SdpData, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true
	cr.Read()

	var data []SdpData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		ga, err := strconv.Atoi(record[10])
		if err != nil {
			return nil, fmt.Errorf("failed to parse GA. %s", err)
		}

		netSec, err := strconv.ParseFloat(record[11], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse Net Secondary Mn. %s", err)
		}

		dayNum, err := strconv.Atoi(record[12])
		if err != nil {
			return nil, fmt.Errorf("failed to parse Day Num. %s", err)
		}

		data = append(data, SdpData{
			MonthID:        record[0],
			Period:         record[1],
			AsofDate:       record[2],
			Brand:          record[3],
			Circle:         null.StringFrom(record[4]),
			Region:         null.StringFrom(record[5]),
			Kabupaten:      null.StringFrom(record[6]),
			KabuFlag:       null.StringFrom(record[7]),
			Sdp:            record[8],
			SdpFlag:        record[9],
			GrossAdds:      ga,
			NetSecondaryMn: netSec,
			DayNum:         dayNum,
		})
	}

	return data, nil
}

func (sdp SdpProcess) getIm3SdpData(c context.Context, mtdDate time.Time) ([]SdpData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/im3_sdp.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read im3_sdp.sql. %s", err)
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format("20060102")},
	}

	logger.Info("Getting IM3 SDP data")
	return etlProc.QueryImpalaData[SdpData](c, string(buf), params)
}

func (sdp SdpProcess) getIm3SdpDataBQ(c context.Context, mtdDate time.Time) ([]SdpData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/im3_sdp_bq.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read im3_sdp_bq.sql. %s", err)
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt": {Name: "mtd_dt", Value: mtdDate.Format("2006-01-02")},
	}

	logger.Info("Getting IM3 SDP data")
	res, err := etlProc.QueryBigQueryData[SdpDataBQ](c, string(buf), etlProc.WithBqParams(params))
	if err != nil {
		return nil, err
	}

	return utils.SliceMap(res, func(d SdpDataBQ) SdpData {
		return d.ToSdpData()
	}), nil
}

func (sdp SdpProcess) get3idSdpData(c context.Context, mtdDate time.Time) ([]SdpData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/3id_sdp.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read 3id_sdp_bq.sql. %s", err)
	}

	mtdDateInt, err := strconv.Atoi(mtdDate.Format("20060102"))
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDateInt},
	}

	logger.Info("Getting 3ID SDP data")
	return etlProc.QueryGreenplumData[SdpData](c, string(buf), params)
}

func (sdp SdpProcess) get3idSdpDataBQ(c context.Context, mtdDate time.Time) ([]SdpData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/3id_sdp_bq.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read 3id_sdp_bq.sql. %s", err)
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt": {Name: "mtd_dt", Value: mtdDate.Format("2006-01-02")},
	}

	logger.Info("Getting 3ID SDP data")
	res, err := etlProc.QueryBigQueryData[SdpDataBQ](c, string(buf), etlProc.WithBqParams(params))
	if err != nil {
		return nil, err
	}

	return utils.SliceMap(res, func(d SdpDataBQ) SdpData {
		return d.ToSdpData()
	}), nil
}

func (sdp SdpProcess) postProcessDseData(data []DseData) (*DseReport, error) {
	logger := ctx.ExtractLogger(context.Background())
	brandInfo := map[string]*PartnerReportData{
		"IM3": NewPartnerReportData(),
		"3ID": NewPartnerReportData(),
	}

	iohData := NewPartnerReportData()

	fmMap := make(map[string]struct{})

	var asofDate time.Time
	var err error

	for _, d := range data {
		brand := strings.TrimSpace(strings.ToUpper(d.Brand))

		circle := strings.TrimSpace(strings.ToUpper(d.Circle.String))
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := strings.TrimSpace(strings.ToUpper(d.Region.String))
		period := d.Period

		brandData, ok := brandInfo[brand]
		if !ok {
			continue
		}

		if _, ok := brandData.RegionalData[region]; !ok {
			brandData.RegionalData[region] = NewRegionalPartnerReportData("REGION", region)
		}
		regData := brandData.RegionalData[region]

		if _, ok := iohData.RegionalData[region]; !ok {
			iohData.RegionalData[region] = NewRegionalPartnerReportData("REGION", region)
		}
		iohRegData := iohData.RegionalData[region]

		if _, ok := brandData.CircleData[circle]; !ok {
			brandData.CircleData[circle] = NewRegionalPartnerReportData("CIRCLE", circle)
		}
		cirData := brandData.CircleData[circle]

		if _, ok := iohData.CircleData[circle]; !ok {
			iohData.CircleData[circle] = NewRegionalPartnerReportData("CIRCLE", circle)
		}
		iohCirData := iohData.CircleData[circle]

		natData := brandData.NationalData
		iohNatData := iohData.NationalData

		switch period {
		case "MTD":
			asofDate, err = time.Parse("20060102", d.AsofDate)
			if err != nil {
				logger.Error("failed to parse asof date", "date", d.AsofDate, "error", err)
			}
			sdp.populateDseData(d, regData.MTD)
			sdp.populateDseData(d, cirData.MTD)
			sdp.populateDseData(d, natData.MTD)
			sdp.populateDseData(d, iohRegData.MTD)
			sdp.populateDseData(d, iohCirData.MTD)
			sdp.populateDseData(d, iohNatData.MTD)

		case "LMTD":
			sdp.populateDseData(d, regData.LMTD)
			sdp.populateDseData(d, cirData.LMTD)
			sdp.populateDseData(d, natData.LMTD)
			sdp.populateDseData(d, iohRegData.LMTD)
			sdp.populateDseData(d, iohCirData.LMTD)
			sdp.populateDseData(d, iohNatData.LMTD)
		case "FM":
			fmMap[d.MonthID] = struct{}{}
			sdp.populateDseFmData(d, regData.FM)
			sdp.populateDseFmData(d, cirData.FM)
			sdp.populateDseFmData(d, natData.FM)
			sdp.populateDseFmData(d, iohRegData.FM)
			sdp.populateDseFmData(d, iohCirData.FM)
			sdp.populateDseFmData(d, iohNatData.FM)
		}
	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.Sort(fmList)

	brandInfo["IM3"].FmList = fmList
	brandInfo["3ID"].FmList = fmList
	iohData.FmList = fmList

	return &DseReport{
		AsofDate: asofDate,
		IM3:      brandInfo["IM3"],
		Three:    brandInfo["3ID"],
		IOH:      iohData,
	}, nil
}

func (sdp SdpProcess) populateDseData(data DseData, regData *PartnerKpiData) {
	gaSlab := data.GASlab
	secSlab := data.NetSecSlab

	if _, ok := regData.PtGaSlabCount[gaSlab]; !ok {
		regData.PtGaSlabCount[gaSlab] = 0
	}
	regData.PtGaSlabCount[gaSlab]++

	if _, ok := regData.PtSecSlabCount[secSlab]; !ok {
		regData.PtSecSlabCount[secSlab] = 0
	}
	regData.PtSecSlabCount[secSlab]++

	regData.TotalGA += data.GrossAdds
	regData.TotalSec += data.NetSecondaryMn
	regData.PtCount++
}

func (sdp SdpProcess) populateDseFmData(data DseData, regData map[string]*PartnerKpiData) {
	monthID := data.MonthID
	if _, ok := regData[monthID]; !ok {
		regData[monthID] = &PartnerKpiData{
			PtGaSlabCount:  make(map[string]int),
			PtSecSlabCount: make(map[string]int),
		}
	}

	sdp.populateDseData(data, regData[monthID])
}

func (sdp SdpProcess) postProcessSdpData(data []SdpData) (*SdpReport, error) {
	logger := ctx.ExtractLogger(context.Background())
	brandInfo := map[string]*PartnerReportData{
		"IM3": NewPartnerReportData(),
		"3ID": NewPartnerReportData(),
	}

	iohData := NewPartnerReportData()

	fmMap := make(map[string]struct{})

	var asofDate time.Time
	var err error

	for _, d := range data {
		brand := strings.TrimSpace(strings.ToUpper(d.Brand))

		circle := strings.TrimSpace(strings.ToUpper(d.Circle.String))
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := strings.TrimSpace(strings.ToUpper(d.Region.String))
		period := d.Period

		brandData, ok := brandInfo[brand]
		if !ok {
			continue
		}

		flag := strings.TrimSpace(strings.ToUpper(d.SdpFlag))
		kabu := strings.TrimSpace(strings.ToUpper(d.Kabupaten.String))
		kabuFlag := strings.TrimSpace(strings.ToUpper(d.KabuFlag.String))

		if flag == "MPC" {
			continue
		}

		if flag == "MP3" {
			continue
		}

		if _, ok := brandData.RegionalData[region]; !ok {
			brandData.RegionalData[region] = NewRegionalPartnerReportData("REGION", region)
			brandData.RegionalKabuMap[region] = make(map[string]*RegionalPartnerReportData)
		}
		regData := brandData.RegionalData[region]
		regData.KabuMap[kabu] = struct{}{}

		if _, ok := brandData.RegionalKabuMap[region][kabuFlag]; !ok {
			brandData.RegionalKabuMap[region][kabuFlag] = NewRegionalPartnerReportData("FLAG", kabuFlag)
		}
		regKabuData := brandData.RegionalKabuMap[region][kabuFlag]
		regKabuData.KabuMap[kabu] = struct{}{}

		if _, ok := iohData.RegionalData[region]; !ok {
			iohData.RegionalData[region] = NewRegionalPartnerReportData("REGION", region)
			iohData.RegionalKabuMap[region] = make(map[string]*RegionalPartnerReportData)
		}
		iohRegData := iohData.RegionalData[region]
		iohRegData.KabuMap[kabu] = struct{}{}

		if _, ok := iohData.RegionalKabuMap[region][kabuFlag]; !ok {
			iohData.RegionalKabuMap[region][kabuFlag] = NewRegionalPartnerReportData("FLAG", kabuFlag)
		}
		iohRegKabuData := iohData.RegionalKabuMap[region][kabuFlag]
		iohRegKabuData.KabuMap[kabu] = struct{}{}

		if _, ok := brandData.CircleData[circle]; !ok {
			brandData.CircleData[circle] = NewRegionalPartnerReportData("CIRCLE", circle)
		}
		cirData := brandData.CircleData[circle]
		cirData.KabuMap[kabu] = struct{}{}

		if _, ok := iohData.CircleData[circle]; !ok {
			iohData.CircleData[circle] = NewRegionalPartnerReportData("CIRCLE", circle)
		}
		iohCirData := iohData.CircleData[circle]
		iohCirData.KabuMap[kabu] = struct{}{}

		natData := brandData.NationalData
		if _, ok := brandData.NationalKabuMap[kabuFlag]; !ok {
			brandData.NationalKabuMap[kabuFlag] = NewRegionalPartnerReportData("FLAG", kabuFlag)
		}
		natKabuData := brandData.NationalKabuMap[kabuFlag]
		natKabuData.KabuMap[kabu] = struct{}{}

		iohNatData := iohData.NationalData
		if _, ok := iohData.NationalKabuMap[kabuFlag]; !ok {
			iohData.NationalKabuMap[kabuFlag] = NewRegionalPartnerReportData("FLAG", kabuFlag)
		}
		iohNatKabuData := iohData.NationalKabuMap[kabuFlag]
		iohNatKabuData.KabuMap[kabu] = struct{}{}

		switch period {
		case "MTD":
			asofDate, err = time.Parse("20060102", d.AsofDate)
			if err != nil {
				logger.Error("failed to parse asof date", "date", d.AsofDate, "error", err)
			}
			sdp.populateSdpData(d, regData.MTD)
			sdp.populateSdpData(d, regKabuData.MTD)
			sdp.populateSdpData(d, cirData.MTD)
			sdp.populateSdpData(d, natData.MTD)
			sdp.populateSdpData(d, natKabuData.MTD)
			sdp.populateSdpData(d, iohRegData.MTD)
			sdp.populateSdpData(d, iohRegKabuData.MTD)
			sdp.populateSdpData(d, iohCirData.MTD)
			sdp.populateSdpData(d, iohNatData.MTD)
			sdp.populateSdpData(d, iohNatKabuData.MTD)
		case "LMTD":
			sdp.populateSdpData(d, regData.LMTD)
			sdp.populateSdpData(d, regKabuData.LMTD)
			sdp.populateSdpData(d, cirData.LMTD)
			sdp.populateSdpData(d, natData.LMTD)
			sdp.populateSdpData(d, natKabuData.LMTD)
			sdp.populateSdpData(d, iohRegData.LMTD)
			sdp.populateSdpData(d, iohRegKabuData.LMTD)
			sdp.populateSdpData(d, iohCirData.LMTD)
			sdp.populateSdpData(d, iohNatData.LMTD)
			sdp.populateSdpData(d, iohNatKabuData.LMTD)
		case "FM":
			fmMap[d.MonthID] = struct{}{}
			sdp.populateSdpFMData(d, regData.FM)
			sdp.populateSdpFMData(d, regKabuData.FM)
			sdp.populateSdpFMData(d, cirData.FM)
			sdp.populateSdpFMData(d, natData.FM)
			sdp.populateSdpFMData(d, natKabuData.FM)
			sdp.populateSdpFMData(d, iohRegData.FM)
			sdp.populateSdpFMData(d, iohRegKabuData.FM)
			sdp.populateSdpFMData(d, iohCirData.FM)
			sdp.populateSdpFMData(d, iohNatData.FM)
			sdp.populateSdpFMData(d, iohNatKabuData.FM)
		}
	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.Sort(fmList)

	brandInfo["IM3"].FmList = fmList
	brandInfo["3ID"].FmList = fmList
	iohData.FmList = fmList

	return &SdpReport{
		AsofDate: asofDate,
		IM3:      brandInfo["IM3"],
		Three:    brandInfo["3ID"],
		IOH:      iohData,
	}, nil
}

func (sdp SdpProcess) populateSdpData(d SdpData, regData *PartnerKpiData) {
	if d.SdpFlag == "MPC" || d.SdpFlag == "MP3" {
		return
	}

	regData.TotalGA += d.GrossAdds
	regData.TotalSec += d.NetSecondaryMn
	regData.PtCount++
}

func (sdp SdpProcess) populateSdpFMData(d SdpData, regData map[string]*PartnerKpiData) {
	monthID := d.MonthID
	if _, ok := regData[monthID]; !ok {
		regData[monthID] = &PartnerKpiData{
			PtGaSlabCount:  make(map[string]int),
			PtSecSlabCount: make(map[string]int),
		}
	}

	sdp.populateSdpData(d, regData[monthID])
}

var (
	SDP_DSE_SHEET = "DSE SDP"
	GaStartCol    = 1
	SecStartCol   = 8

	SdpLmtdColOffset = 4
	SdpMtdColOffset  = 5
)

func (sdp SdpProcess) WriteDseReport(xl *excelize.File, data *DseReport) error {
	shName := SDP_DSE_SHEET
	startIOH := 5
	cirStart := map[string]int{
		"JAKARTA RAYA": 10,
		"JAVA":         15,
		"KALISUMAPA":   20,
		"SUMATERA":     25,
	}

	asofDate := data.AsofDate.Format("02 Jan")
	if err := xl.SetCellValue(shName, "K2", fmt.Sprintf("As of %s", asofDate)); err != nil {
		return err
	}

	if err := sdp.writeDseReport(xl, data.IOH.NationalData, startIOH); err != nil {
		return err
	}

	for circle, startCol := range cirStart {
		if _, ok := data.IOH.CircleData[circle]; !ok {
			continue
		}

		if err := sdp.writeDseReport(xl, data.IOH.CircleData[circle], startCol); err != nil {
			return err
		}
	}

	return nil
}

func (sdp SdpProcess) writeDseReport(xl *excelize.File, data *RegionalPartnerReportData, startRow int) error {
	shName := SDP_DSE_SHEET
	fmList := utils.MapToList(data.FM, func(key string, value *PartnerKpiData) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for i, mth := range fmList {
		if i > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		gaCol := GaStartCol + (3 - i)
		secCol := SecStartCol + (3 - i)

		mthName := mthDate.Format("Jan-06")
		if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, gaCol).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, secCol).Address(), mthName); err != nil {
			return err
		}

		if err := sdp.writeDseBox(xl, data.FM[mth], startRow, gaCol, secCol); err != nil {
			return err
		}
	}

	if err := sdp.writeDseBox(xl, data.LMTD, startRow, GaStartCol+SdpLmtdColOffset, SecStartCol+SdpLmtdColOffset); err != nil {
		return err
	}

	if err := sdp.writeDseBox(xl, data.MTD, startRow, GaStartCol+SdpMtdColOffset, SecStartCol+SdpMtdColOffset); err != nil {
		return err
	}

	return nil
}

func (sdp SdpProcess) writeDseBox(xl *excelize.File, data *PartnerKpiData, startRow, gaCol int, secCol int) error {
	shName := SDP_DSE_SHEET

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow, gaCol).Address(), data.PtCount); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow, secCol).Address(), data.PtCount); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+1, gaCol).Address(), data.PtGaSlabCount["<100%"]); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+1, secCol).Address(), data.PtSecSlabCount["<100%"]); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+2, gaCol).Address(), data.PtGaSlabCount[">=100%"]); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+2, secCol).Address(), data.PtSecSlabCount[">=100%"]); err != nil {
		return err
	}

	return nil
}

func (sdp SdpProcess) WriteSdpReport(xl *excelize.File, data *SdpReport) error {
	shName := SDP_DSE_SHEET
	iohStart := 33
	cirStart := map[string]int{
		"JAKARTA RAYA": 38,
		"JAVA":         43,
		"KALISUMAPA":   48,
		"SUMATERA":     53,
	}

	microStart := map[string]int{
		"MUST WIN 50":         33,
		"SUPER 88":            38,
		"HIGH POTENTIAL AREA": 43,
		"REST MUST WIN":       48,
		"THE REST":            53,
	}

	// fill asof date
	asofDate := data.AsofDate.Format("02 Jan")
	if err := xl.SetCellValue(shName, "K30", fmt.Sprintf("As of %s", asofDate)); err != nil {
		return err
	}

	if err := sdp.writeSdpReport(xl, data.IOH.NationalData, iohStart, true); err != nil {
		return err
	}

	if err := sdp.writeSdpReport(xl, data.Three.NationalData, iohStart+1, false); err != nil {
		return err
	}

	if err := sdp.writeSdpReport(xl, data.IM3.NationalData, iohStart+2, false); err != nil {
		return err
	}

	for circle, startCol := range cirStart {
		if _, ok := data.IOH.CircleData[circle]; !ok {
			continue
		}

		if err := sdp.writeSdpReport(xl, data.IOH.CircleData[circle], startCol, true); err != nil {
			return err
		}

		if _, ok := data.Three.CircleData[circle]; !ok {
			continue
		}

		if err := sdp.writeSdpReport(xl, data.Three.CircleData[circle], startCol+1, false); err != nil {
			return err
		}

		if _, ok := data.IM3.CircleData[circle]; !ok {
			continue
		}

		if err := sdp.writeSdpReport(xl, data.IM3.CircleData[circle], startCol+2, false); err != nil {
			return err
		}
	}

	for micro, startRow := range microStart {
		if _, ok := data.IOH.NationalKabuMap[micro]; !ok {
			continue
		}

		if err := sdp.writeSdpReport(xl, data.IOH.NationalKabuMap[micro], startRow, true); err != nil {
			return err
		}

		if _, ok := data.Three.NationalKabuMap[micro]; !ok {
			continue
		}

		if err := sdp.writeSdpReport(xl, data.Three.NationalKabuMap[micro], startRow+1, false); err != nil {
			return err
		}

		if _, ok := data.IM3.NationalKabuMap[micro]; !ok {
			continue
		}

		if err := sdp.writeSdpReport(xl, data.IM3.NationalKabuMap[micro], startRow+2, false); err != nil {
			return err
		}
	}

	return nil
}

func (sdp SdpProcess) writeSdpReport(xl *excelize.File, data *RegionalPartnerReportData, startRow int, regStart bool) error {
	shName := SDP_DSE_SHEET
	fmList := utils.MapToList(data.FM, func(key string, value *PartnerKpiData) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for i, mth := range fmList {
		if i > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		gaCol := GaStartCol + (3 - i)
		if data.RegionType == "FLAG" {
			gaCol = gaCol + 14
		}

		secCol := SecStartCol + (3 - i)
		if data.RegionType == "FLAG" {
			secCol = secCol + 14
		}

		mthName := mthDate.Format("Jan-06")

		if regStart {
			if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, gaCol).Address(), mthName); err != nil {
				return err
			}

			if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, secCol).Address(), mthName); err != nil {
				return err
			}
		}

		if err := sdp.writeSdpBox(xl, data.FM[mth], startRow, gaCol, secCol); err != nil {
			return err
		}
	}

	gaCol := GaStartCol
	if data.RegionType == "FLAG" {
		gaCol = gaCol + 14
	}

	secCol := SecStartCol
	if data.RegionType == "FLAG" {
		secCol = secCol + 14
	}

	if err := sdp.writeSdpBox(xl, data.LMTD, startRow, gaCol+SdpLmtdColOffset, secCol+SdpLmtdColOffset); err != nil {
		return err
	}

	if err := sdp.writeSdpBox(xl, data.MTD, startRow, gaCol+SdpMtdColOffset, secCol+SdpMtdColOffset); err != nil {
		return err
	}

	return nil
}

func (sdp SdpProcess) writeSdpBox(xl *excelize.File, data *PartnerKpiData, startRow, gaCol int, secCol int) error {
	shName := SDP_DSE_SHEET

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow, gaCol).Address(), float64(data.TotalGA)/float64(data.PtCount)); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow, secCol).Address(), data.TotalSec/float64(data.PtCount)); err != nil {
		return err
	}

	return nil
}

func getDseGaBinLabel(ga int) string {
	if ga < 350 {
		return "<100%"
	}
	return ">=100%"
}

func getDseSecBinLabel(secMn float64, brand string) string {
	thr := 80.0
	if brand == "3ID" {
		thr = 50.0
	}

	if secMn < thr {
		return "<100%"
	}

	return ">=100%"
}
