package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

var EncKeys []string

func AddKey(key string) {
	EncKeys = append(Enc<PERSON><PERSON><PERSON>, key)
}

func populateEncKeys(conf *viper.Viper) {
	for i := 1; i <= 15; i++ {
		key := conf.GetString(fmt.Sprintf("enc.key_%d", i))
		if len(key) == 32 {
			EncKeys = append(EncKeys, key)
		}
	}
}

// Config holds all configuration for the portal
type Config struct {
	// Server configuration
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	LogLevel int    `mapstructure:"log_level"`

	DBPath string `mapstructure:"dbpath"`

	// ETL configuration
	FileRoot string `mapstructure:"file_root"`
}

// Load loads configuration from file and environment variables
func Load() (*viper.Viper, error) {
	exePath, err := os.Executable()
	if err != nil {
		return nil, err
	}

	exeDir := filepath.Dir(exePath)
	dbPath := filepath.Join(exeDir, "portal.db")

	v := viper.New()

	v.SetConfigFile(".portal.yaml")
	v.SetTypeByDefaultValue(true)
	v.AddConfigPath(".")
	v.AddConfigPath("./config")

	// Environment variables
	v.SetEnvPrefix("BPCS")
	v.AutomaticEnv()

	v.SetDefault("dbpath", dbPath)
	v.SetDefault("host", "localhost")
	v.SetDefault("port", 8080)
	v.SetDefault("log_level", 1)

	// Read config file if it exists
	if err := v.ReadInConfig(); err != nil {
		// It's okay if config file doesn't exist
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}

	populateEncKeys(v)

	return v, nil
}

func GetConfig(v *viper.Viper) (*Config, error) {
	var conf Config
	if err := v.Unmarshal(&conf); err != nil {
		return nil, err
	}

	return &conf, nil
}
