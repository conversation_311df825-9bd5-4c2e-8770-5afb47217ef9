package http

import (
	"net/http"

	"github.com/csee-pm/etl/portal/backend/internal/idm"
	htx "github.com/likearthian/apikit/transport/http"
)

type IdmAPIHandlers struct {
	GetUserHandler    http.Handler
	CreateUserHandler http.Handler
}

func createIdmHandlers(idmAPI *idm.IdmAPI, options ...htx.ServerOption) IdmAPIHandlers {
	return IdmAPIHandlers{
		GetUserHandler: htx.NewServer(
			idmAPI.GetUserEndpoint,
			htx.CommonGetRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		CreateUserHandler: htx.NewServer(
			idmAPI.CreateUserEndpoint,
			htx.CommonPostRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
	}
}
