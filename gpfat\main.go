package main

import (
	"context"
	"os"

	"github.com/apex/log"
	apexcli "github.com/apex/log/handlers/cli"
	"github.com/apex/log/handlers/text"
	"github.com/csee-pm/etl/gpfat/cmd"
	ctx "github.com/csee-pm/etl/shared/context"
	"github.com/csee-pm/etl/shared/logger"

	"github.com/spf13/viper"
)

func main() {
	v := viper.New()
	v.SetTypeByDefaultValue(true)

	alog := &log.Logger{}
	alog.Handler = apexcli.New(os.Stdout)
	alog.Handler = text.New(os.Stdout)

	logger := logger.NewApexLogger(alog)

	root := cmd.InitCommand(v, logger)

	c := context.WithValue(context.Background(), ctx.ContextKeyConfig, v)
	c = context.WithValue(c, ctx.ContextKeyLogger, logger)
	err := root.ExecuteContext(c)
	if err != nil {
		logger.Error(err.Error())
		os.Exit(1)
	}
}
