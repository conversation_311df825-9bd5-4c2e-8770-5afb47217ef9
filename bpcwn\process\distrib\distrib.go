package distrib

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	cfg "github.com/csee-pm/etl/bpcwn/config"
	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type DistribProcess struct {
	procFS fs.ReadFileFS
}

func NewDistribProcess(procFS fs.ReadFileFS) DistribProcess {
	return DistribProcess{procFS}
}

func (dst DistribProcess) RunDistrib(c context.Context, workDate time.Time) (*DistribReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	logger.Debug("starting Distrib ETL")

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return nil, fmt.Errorf("failed to parse mtd_date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	fromMtdFile := cfg.UseMtdDistribFromFile
	fromFmFile := cfg.UseFmDistribFromFile

	var mtdData []DistribMtdData
	var fmData []DistribFmData
	var lrsData []LrsData

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var wg sync.WaitGroup

	wg.Add(1)
	mtdResult := channel.RunAsyncContext(cCancel, func() ([]DistribMtdData, error) {
		if fromMtdFile != "" {
			return dst.getMtdDataFromFile(fromMtdFile)
		}
		return dst.getMtdData(c, workDate)
	})

	go func() {
		defer wg.Done()
		for res := range mtdResult {
			res.Map(func(data []DistribMtdData) {
				mtdData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get MTD data")
					return
				}
				// logger.Error("failed to get MTD data", "error", er)
				err = fmt.Errorf("failed to get MTD data. %s", er)
			})
		}
	}()

	wg.Add(1)
	fmResult := channel.RunAsyncContext(cCancel, func() ([]DistribFmData, error) {
		if fromFmFile != "" {
			return dst.getFmDataFromFile(fromFmFile)
		}
		return dst.getFmData(c, workDate)
	})

	go func() {
		defer wg.Done()
		for res := range fmResult {
			res.Map(func(data []DistribFmData) {
				fmData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get FM data")
					return
				}
				err = fmt.Errorf("failed to get FM data. %s", er)
			})
		}
	}()

	wg.Add(1)
	lsrResult := channel.RunAsyncContext(cCancel, func() ([]LrsData, error) {
		return dst.getLrsData(c, workDate)
	})

	go func() {
		defer wg.Done()
		for res := range lsrResult {
			res.Map(func(data []LrsData) {
				lrsData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get LRS data")
					return
				}
				err = fmt.Errorf("failed to get LRS data. %s", er)
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to get data for Distrib Report. %s", err)
	}

	if fromMtdFile == "" {
		dst.writeMtdRawData(c, mtdData)
	}

	if fromFmFile == "" {
		dst.writeFmRawData(c, fmData)
	}

	reportData, err := dst.postProcessData(mtdData, fmData, lrsData)
	if err != nil {
		return nil, err
	}

	target, err := dst.getBrandSndTarget(c)
	if err != nil {
		return nil, err
	}

	reportData.Target = target

	return &reportData, nil
}

func (dst DistribProcess) getMtdDataFromFile(fpath string) ([]DistribMtdData, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var data []DistribMtdData
	// if err := apiutil.ReadFromCsv(f, &data, apiutil.WithHeaderLine(0)); err != nil {
	// 	return nil, err
	// }

	cr := csv.NewReader(f)

	cr.Read() // skip header
	for {
		record, err := cr.Read()

		if errors.Is(err, io.EOF) {
			break
		}

		if err != nil {
			return nil, err
		}

		mtd, err := strconv.ParseFloat(record[4], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse MTD: %s. %s", record[4], err)
		}

		lmtd, err := strconv.ParseFloat(record[3], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse LMTD: %s. %s", record[3], err)
		}

		data = append(data, DistribMtdData{
			Brand:  record[0],
			Circle: null.StringFrom(record[1]),
			Region: null.StringFrom(record[2]),
			DistribMtdParam: DistribMtdParam{
				MTD:       mtd,
				LMTD:      lmtd,
				DtID:      record[5],
				Parameter: record[6],
			},
		})
	}

	return data, nil
}

func (dst DistribProcess) getFmDataFromFile(fpath string) ([]DistribFmData, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var data []DistribFmData
	// if err := apiutil.ReadFromCsv(f, &data, apiutil.WithHeaderLine(0)); err != nil {
	// 	return nil, err
	// }

	cr := csv.NewReader(f)

	cr.Read() // skip header
	for {
		record, err := cr.Read()

		if errors.Is(err, io.EOF) {
			break
		}

		if err != nil {
			return nil, err
		}

		fmval, err := strconv.ParseFloat(record[5], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse FM value: %s. %s", record[5], err)
		}

		data = append(data, DistribFmData{
			MonthID:   record[0],
			Brand:     record[1],
			Circle:    null.StringFrom(record[2]),
			Region:    null.StringFrom(record[3]),
			Parameter: record[4],
			Value:     fmval,
		})
	}

	return data, nil
}

func (dst DistribProcess) getMtdData(c context.Context, workDate time.Time) ([]DistribMtdData, error) {
	logger := ctx.ExtractLogger(c)
	logger.Debug("querying data for Distrib")

	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var impalaData []DistribMtdData
	var gpfatData []DistribMtdData

	mtdDate, err := strconv.Atoi(workDate.Format("20060102"))
	if err != nil {
		return nil, err
	}

	wg.Add(1)
	impalaResult := channel.RunAsyncContext(cCancel, func() ([]DistribMtdData, error) {
		return dst.getImpalaData(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range impalaResult {
			res.Map(func(data []DistribMtdData) {
				impalaData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get Impala data for MTD Distrib")
					return
				}
				err = fmt.Errorf("failed to get Impala data for MTD Distrib. %s", er)
			})
		}
	}()

	wg.Add(1)
	gpfatResult := channel.RunAsyncContext(cCancel, func() ([]DistribMtdData, error) {
		return dst.get3idData(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range gpfatResult {
			res.Map(func(data []DistribMtdData) {
				gpfatData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get GP data for MTD Distrib")
					return
				}
				err = fmt.Errorf("failed to get GP data for MTD Distrib. %s", er)
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to get Distrib MTD data. %s", err)
	}

	return append(impalaData, gpfatData...), nil
}

func (dst DistribProcess) getFmData(c context.Context, workDate time.Time) ([]DistribFmData, error) {
	logger := ctx.ExtractLogger(c)
	logger.Debug("querying data for Distrib FM")

	var fmList []int
	y, m, _ := workDate.Date()
	firstDay := time.Date(y, m, 1, 0, 0, 0, 0, time.Local)
	for i := 0; i < 3; i++ {
		fmDate, err := strconv.Atoi(firstDay.AddDate(0, 0, -1).Format("20060102"))
		if err != nil {
			return nil, err
		}
		fmList = append(fmList, fmDate)
		firstDay = firstDay.AddDate(0, -1, 0)
	}

	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var impalaData []DistribFmData
	var gpfatData []DistribFmData
	var err error

	wg.Add(1)
	impalaResult := channel.RunAsyncContext(cCancel, func() ([]DistribFmData, error) {
		var res []DistribMtdData
		for _, dt := range fmList {
			data, err := dst.getImpalaData(cCancel, dt)
			if err != nil {
				return nil, err
			}
			res = append(res, data...)
		}

		return utils.SliceMap(res, func(d DistribMtdData) DistribFmData {
			return DistribFmData{
				MonthID:   d.DtID[:6],
				Brand:     d.Brand,
				Circle:    d.Circle,
				Region:    d.Region,
				Parameter: d.Parameter,
				Value:     d.MTD,
			}
		}), nil
	})

	go func() {
		defer wg.Done()
		for res := range impalaResult {
			res.Map(func(data []DistribFmData) {
				impalaData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get Impala data for FM Distrib")
					return
				}
				err = fmt.Errorf("failed to get Impala data for FM Distrib. %s", er)
			})
		}
	}()

	wg.Add(1)
	gpfatResult := channel.RunAsyncContext(cCancel, func() ([]DistribFmData, error) {
		var res []DistribMtdData
		for _, dt := range fmList {
			data, err := dst.get3idData(cCancel, dt)
			if err != nil {
				return nil, err
			}
			res = append(res, data...)
		}
		return utils.SliceMap(res, func(d DistribMtdData) DistribFmData {
			return DistribFmData{
				MonthID:   d.DtID[:6],
				Brand:     d.Brand,
				Circle:    d.Circle,
				Region:    d.Region,
				Parameter: d.Parameter,
				Value:     d.MTD,
			}
		}), nil
	})

	go func() {
		defer wg.Done()
		for res := range gpfatResult {
			res.Map(func(data []DistribFmData) {
				gpfatData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get GP data for FM Distrib")
					return
				}
				err = fmt.Errorf("failed to get GP data for FM Distrib. %s", er)
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to get Distrib FM data. %s", err)
	}

	return append(impalaData, gpfatData...), nil
}

func (dst DistribProcess) getLrsData(c context.Context, workDate time.Time) ([]LrsData, error) {
	logger := ctx.ExtractLogger(c)
	logger.Debug("querying data for LRS")

	buf, err := dst.procFS.ReadFile("files/lrs_sites.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: workDate.Format("20060102")},
	}

	return etlProc.QueryImpalaData[LrsData](c, string(buf), params)
}

func (dst DistribProcess) getImpalaData(c context.Context, mtdDate int) ([]DistribMtdData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := dst.procFS.ReadFile("files/im3_dist_mtd.sql")
	if err != nil {
		return nil, err
	}
	logger.Debug("querying data for IM3 Distrib")

	params := map[string]*etlDb.ParamValue{
		"dt_id": {Name: "dt_id", Value: strconv.Itoa(mtdDate)},
	}

	return etlProc.QueryImpalaData[DistribMtdData](c, string(buf), params)
}

func (dst DistribProcess) get3idData(c context.Context, mtdDate int) ([]DistribMtdData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := dst.procFS.ReadFile("files/3id_dist_mtd.sql")
	if err != nil {
		return nil, err
	}
	logger.Debug("querying data for 3ID Distrib")

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDate},
	}

	return etlProc.QueryGreenplumData[DistribMtdData](c, string(buf), params)
}

func (dst DistribProcess) writeMtdRawData(c context.Context, data []DistribMtdData) {
	logger := ctx.ExtractLogger(c)
	workDir := ctx.ExtractWorkDir(c)
	csvFilePath := fmt.Sprintf("%s/distrib_mtd_%s.csv", workDir, time.Now().Format("20060102150405"))

	logger.Debug("writing distrib mtd raw data", "path", csvFilePath)
	if err := utils.WriteToCsv(csvFilePath, data); err != nil {
		logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", csvFilePath)
	}
}

func (dst DistribProcess) writeFmRawData(c context.Context, data []DistribFmData) {
	logger := ctx.ExtractLogger(c)
	workDir := ctx.ExtractWorkDir(c)
	csvFilePath := fmt.Sprintf("%s/distrib_fm_%s.csv", workDir, time.Now().Format("20060102150405"))

	logger.Debug("writing distrib fm raw data", "path", csvFilePath)
	if err := utils.WriteToCsv(csvFilePath, data); err != nil {
		logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", csvFilePath)
	}
}

func (dst DistribProcess) writeLrsRawData(c context.Context, data []LrsData) {
	logger := ctx.ExtractLogger(c)
	workDir := ctx.ExtractWorkDir(c)
	csvFilePath := fmt.Sprintf("%s/distrib_lrs_%s.csv", workDir, time.Now().Format("20060102150405"))

	logger.Debug("writing distrib lrs raw data", "path", csvFilePath)
	if err := utils.WriteToCsv(csvFilePath, data); err != nil {
		logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", csvFilePath)
	}
}

func (dst DistribProcess) postProcessData(mtdData []DistribMtdData, fmData []DistribFmData, lrsData []LrsData) (DistribReport, error) {
	mtdParamData := dst.mapMtdBrandParamData(mtdData)
	fmParamData := dst.mapFmBrandParamData(fmData)

	brandData := map[string]*DistribBrandData{
		"IOH": {
			RegionMtdParamMap:   make(map[string]map[string]*DistribMtdData),
			CircleMtdParamMap:   make(map[string]map[string]*DistribMtdData),
			NationalMtdParamMap: make(map[string]*DistribMtdData),
			RegionFmParamMap:    make(map[string]map[string]map[string]*DistribFmData),
			CircleFmParamMap:    make(map[string]map[string]map[string]*DistribFmData),
			NationalFmParamMap:  make(map[string]map[string]*DistribFmData),
		},
		"IM3": {
			RegionMtdParamMap:   make(map[string]map[string]*DistribMtdData),
			CircleMtdParamMap:   make(map[string]map[string]*DistribMtdData),
			NationalMtdParamMap: make(map[string]*DistribMtdData),
			RegionFmParamMap:    make(map[string]map[string]map[string]*DistribFmData),
			CircleFmParamMap:    make(map[string]map[string]map[string]*DistribFmData),
			NationalFmParamMap:  make(map[string]map[string]*DistribFmData),
		},
		"3ID": {
			RegionMtdParamMap:   make(map[string]map[string]*DistribMtdData),
			CircleMtdParamMap:   make(map[string]map[string]*DistribMtdData),
			NationalMtdParamMap: make(map[string]*DistribMtdData),
			RegionFmParamMap:    make(map[string]map[string]map[string]*DistribFmData),
			CircleFmParamMap:    make(map[string]map[string]map[string]*DistribFmData),
			NationalFmParamMap:  make(map[string]map[string]*DistribFmData),
		},
	}

	iohBrandInfo := brandData["IOH"]

	for brand, _ := range mtdParamData {
		mtdData = mtdParamData[brand]
		fmData = fmParamData[brand]

		brandInfo, exists := brandData[brand]
		if !exists {
			continue
		}

		if err := dst.processMtdBrandData(mtdData, brandInfo, iohBrandInfo); err != nil {
			return DistribReport{}, err
		}

		if err := dst.processFmBrandData(fmData, brandInfo, iohBrandInfo); err != nil {
			return DistribReport{}, err
		}

		if err := dst.processLrsBrandData(lrsData, brandInfo); err != nil {
			return DistribReport{}, err
		}
	}

	if err := dst.processLrsBrandData(lrsData, iohBrandInfo); err != nil {
		return DistribReport{}, err
	}

	// Build report entries
	for _, brandInfo := range brandData {
		// Add region entries
		for region, regionMtdParams := range brandInfo.RegionMtdParamMap {
			regionFmParams, fmExists := brandInfo.RegionFmParamMap[region]
			repData := DistribReportData{
				EntityType:      "REGION",
				EntityName:      region,
				ParamMtdDataMap: utils.MapToMap(regionMtdParams, func(k string, v *DistribMtdData) (string, DistribMtdData) { return k, *v }),
			}

			if fmExists {
				repData.ParamFmDataMap = utils.MapToMap(regionFmParams, func(k string, v map[string]*DistribFmData) (string, map[string]DistribFmData) {
					return k, utils.MapToMap(v, func(k string, v *DistribFmData) (string, DistribFmData) { return k, *v })
				})
			}

			brandInfo.ReportEntries = append(brandInfo.ReportEntries, repData)
		}

		// Add circle entries
		for circle, circleMtdParams := range brandInfo.CircleMtdParamMap {
			repData := DistribReportData{
				EntityType:      "CIRCLE",
				EntityName:      circle,
				ParamMtdDataMap: utils.MapToMap(circleMtdParams, func(k string, v *DistribMtdData) (string, DistribMtdData) { return k, *v }),
			}

			circleFmParams, exists := brandInfo.CircleFmParamMap[circle]
			if exists {
				repData.ParamFmDataMap = utils.MapToMap(circleFmParams, func(k string, v map[string]*DistribFmData) (string, map[string]DistribFmData) {
					return k, utils.MapToMap(v, func(k string, v *DistribFmData) (string, DistribFmData) { return k, *v })
				})
			}
			brandInfo.ReportEntries = append(brandInfo.ReportEntries, repData)
		}

		// Add national entries
		nationalRepData := DistribReportData{
			EntityType:      "NATIONAL",
			EntityName:      "NATIONAL",
			ParamMtdDataMap: utils.MapToMap(brandInfo.NationalMtdParamMap, func(k string, v *DistribMtdData) (string, DistribMtdData) { return k, *v }),
			ParamFmDataMap: utils.MapToMap(brandInfo.NationalFmParamMap, func(k string, v map[string]*DistribFmData) (string, map[string]DistribFmData) {
				return k, utils.MapToMap(v, func(k string, v *DistribFmData) (string, DistribFmData) { return k, *v })
			}),
		}

		brandInfo.ReportEntries = append(brandInfo.ReportEntries, nationalRepData)

		//brandData[brand] = brandInfo
	}

	return DistribReport{
		IOH: brandData["IOH"].ReportEntries,
		IM3: brandData["IM3"].ReportEntries,
		Tri: brandData["3ID"].ReportEntries,
	}, nil
}

func (dst DistribProcess) processMtdBrandData(mtdData []DistribMtdData, brandInfo *DistribBrandData, iohBrandInfo *DistribBrandData) error {
	for i, d := range mtdData {
		circle := strings.TrimSpace(d.Circle.String)
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := strings.TrimSpace(d.Region.String)
		param := d.Parameter

		paramData := mtdData[i]
		if paramData.Parameter == "Secondary" {
			paramData.MTD = paramData.MTD / 1000_000
			paramData.LMTD = paramData.LMTD / 1000_000
		}

		if paramData.Parameter == "RGU GA" {
			paramData.MTD = paramData.MTD / 1000
			paramData.LMTD = paramData.LMTD / 1000
		}

		// process region data
		if _, ok := brandInfo.RegionMtdParamMap[region]; !ok {
			brandInfo.RegionMtdParamMap[region] = make(map[string]*DistribMtdData)
		}
		brandInfo.RegionMtdParamMap[region][param] = utils.PtrToValCopy(paramData)

		if _, ok := iohBrandInfo.RegionMtdParamMap[region]; !ok {
			iohBrandInfo.RegionMtdParamMap[region] = make(map[string]*DistribMtdData)
		}

		iohRegionParams, exists := iohBrandInfo.RegionMtdParamMap[region][param]
		if !exists {
			iohBrandInfo.RegionMtdParamMap[region][param] = &DistribMtdData{
				Brand:           "IOH",
				Circle:          paramData.Circle,
				Region:          paramData.Region,
				DistribMtdParam: paramData.DistribMtdParam,
			}
		} else {
			mtdParamValue := iohRegionParams.MTD + paramData.MTD
			lmtdParamValue := iohRegionParams.LMTD + paramData.LMTD
			if _, ok := distribSitePerfMap[param]; ok {
				mtdParamValue = iohRegionParams.MTD
				if mtdParamValue < paramData.MTD {
					mtdParamValue = paramData.MTD
				}
				lmtdParamValue = iohRegionParams.LMTD
				if lmtdParamValue < paramData.LMTD {
					lmtdParamValue = paramData.LMTD
				}
			}
			iohRegionParams.MTD = mtdParamValue
			iohRegionParams.LMTD = lmtdParamValue
		}

		// aggregate circle data
		if _, ok := brandInfo.CircleMtdParamMap[circle]; !ok {
			brandInfo.CircleMtdParamMap[circle] = make(map[string]*DistribMtdData)
		}

		circleParams, exists := brandInfo.CircleMtdParamMap[circle][param]
		if !exists {
			brandInfo.CircleMtdParamMap[circle][param] = utils.PtrToValCopy(paramData)
		} else {
			circleParams.MTD += paramData.MTD
			circleParams.LMTD += paramData.LMTD
		}

		if _, ok := iohBrandInfo.CircleMtdParamMap[circle]; !ok {
			iohBrandInfo.CircleMtdParamMap[circle] = make(map[string]*DistribMtdData)
		}

		// aggregate national data
		nationalParams, exists := brandInfo.NationalMtdParamMap[param]
		if !exists {
			brandInfo.NationalMtdParamMap[param] = utils.PtrToValCopy(paramData)
		} else {
			nationalParams.MTD += paramData.MTD
			nationalParams.LMTD += paramData.LMTD
		}
	}

	for circle, cval := range brandInfo.CircleMtdParamMap {
		for param, pval := range cval {
			iohCircleParams, exists := iohBrandInfo.CircleMtdParamMap[circle][param]
			if !exists {
				iohBrandInfo.CircleMtdParamMap[circle][param] = &DistribMtdData{
					Brand:           "IOH",
					Circle:          pval.Circle,
					DistribMtdParam: pval.DistribMtdParam,
				}
			} else {
				mtdParamValue := iohCircleParams.MTD + pval.MTD
				lmtdParamValue := iohCircleParams.LMTD + pval.LMTD
				if _, ok := distribSitePerfMap[param]; ok {
					mtdParamValue = iohCircleParams.MTD
					if mtdParamValue < pval.MTD {
						mtdParamValue = pval.MTD
					}
					lmtdParamValue = iohCircleParams.LMTD
					if lmtdParamValue < pval.LMTD {
						lmtdParamValue = pval.LMTD
					}
				}
				iohCircleParams.MTD = mtdParamValue
				iohCircleParams.LMTD = lmtdParamValue
			}
		}
	}

	for param, pval := range brandInfo.NationalMtdParamMap {
		iohNationalParams, exists := iohBrandInfo.NationalMtdParamMap[param]
		if !exists {
			iohBrandInfo.NationalMtdParamMap[param] = &DistribMtdData{
				Brand:           "IOH",
				DistribMtdParam: pval.DistribMtdParam,
			}
		} else {
			mtdParamValue := iohNationalParams.MTD + pval.MTD
			lmtdParamValue := iohNationalParams.LMTD + pval.LMTD
			if _, ok := distribSitePerfMap[param]; ok {
				mtdParamValue = iohNationalParams.MTD
				if mtdParamValue < pval.MTD {
					mtdParamValue = pval.MTD
				}
				lmtdParamValue = iohNationalParams.LMTD
				if lmtdParamValue < pval.LMTD {
					lmtdParamValue = pval.LMTD
				}
			}
			iohNationalParams.MTD = mtdParamValue
			iohNationalParams.LMTD = lmtdParamValue
		}
	}

	return nil
}

func (dst DistribProcess) processFmBrandData(fmData []DistribFmData, brandInfo *DistribBrandData, iohBrandInfo *DistribBrandData) error {
	for i, d := range fmData {
		circle := strings.TrimSpace(d.Circle.String)
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := strings.TrimSpace(d.Region.String)
		param := d.Parameter
		mth := d.MonthID

		paramData := fmData[i]
		if paramData.Parameter == "Secondary" {
			paramData.Value = paramData.Value / 1000_000
		}

		if paramData.Parameter == "RGU GA" {
			paramData.Value = paramData.Value / 1000
		}

		// process region data
		if _, ok := brandInfo.RegionFmParamMap[region]; !ok {
			brandInfo.RegionFmParamMap[region] = make(map[string]map[string]*DistribFmData)
		}
		if _, ok := brandInfo.RegionFmParamMap[region][param]; !ok {
			brandInfo.RegionFmParamMap[region][param] = make(map[string]*DistribFmData)
		}
		brandInfo.RegionFmParamMap[region][param][mth] = utils.PtrToValCopy(paramData)

		if _, ok := iohBrandInfo.RegionFmParamMap[region]; !ok {
			iohBrandInfo.RegionFmParamMap[region] = make(map[string]map[string]*DistribFmData)
		}
		if _, ok := iohBrandInfo.RegionFmParamMap[region][param]; !ok {
			iohBrandInfo.RegionFmParamMap[region][param] = make(map[string]*DistribFmData)
		}
		iohRegionParams, exists := iohBrandInfo.RegionFmParamMap[region][param][d.MonthID]
		if !exists {
			iohBrandInfo.RegionFmParamMap[region][param][d.MonthID] = &DistribFmData{
				MonthID:   d.MonthID,
				Brand:     "IOH",
				Circle:    paramData.Circle,
				Region:    paramData.Region,
				Parameter: paramData.Parameter,
				Value:     paramData.Value,
			}
		} else {
			fmParamValue := iohRegionParams.Value + paramData.Value
			if _, ok := distribSitePerfMap[param]; ok {
				fmParamValue = iohRegionParams.Value
				if fmParamValue < paramData.Value {
					fmParamValue = paramData.Value
				}
			}
			iohRegionParams.Value = fmParamValue
		}

		// aggregate circle data
		if _, ok := brandInfo.CircleFmParamMap[circle]; !ok {
			brandInfo.CircleFmParamMap[circle] = make(map[string]map[string]*DistribFmData)
		}
		if _, ok := brandInfo.CircleFmParamMap[circle][param]; !ok {
			brandInfo.CircleFmParamMap[circle][param] = make(map[string]*DistribFmData)
		}

		circleMonthData, exists := brandInfo.CircleFmParamMap[circle][param][d.MonthID]
		if !exists {
			brandInfo.CircleFmParamMap[circle][param][d.MonthID] = utils.PtrToValCopy(paramData)
		} else {
			circleMonthData.Value += paramData.Value
		}

		if _, ok := iohBrandInfo.CircleFmParamMap[circle]; !ok {
			iohBrandInfo.CircleFmParamMap[circle] = make(map[string]map[string]*DistribFmData)
		}
		if _, ok := iohBrandInfo.CircleFmParamMap[circle][param]; !ok {
			iohBrandInfo.CircleFmParamMap[circle][param] = make(map[string]*DistribFmData)
		}

		// aggregate national data
		if _, ok := brandInfo.NationalFmParamMap[param]; !ok {
			brandInfo.NationalFmParamMap[param] = make(map[string]*DistribFmData)
		}
		nationalMonthData, exists := brandInfo.NationalFmParamMap[param][d.MonthID]
		if !exists {
			brandInfo.NationalFmParamMap[param][d.MonthID] = utils.PtrToValCopy(paramData)
		} else {
			nationalMonthData.Value += paramData.Value
		}

		if _, ok := iohBrandInfo.NationalFmParamMap[param]; !ok {
			iohBrandInfo.NationalFmParamMap[param] = make(map[string]*DistribFmData)
		}
	}

	for circle, cval := range brandInfo.CircleFmParamMap {
		for param, pval := range cval {
			for mth, mval := range pval {
				iohCircleParams, exists := iohBrandInfo.CircleFmParamMap[circle][param][mth]
				if !exists {
					iohBrandInfo.CircleFmParamMap[circle][param][mth] = &DistribFmData{
						MonthID:   mth,
						Brand:     "IOH",
						Circle:    mval.Circle,
						Region:    mval.Region,
						Parameter: mval.Parameter,
						Value:     mval.Value,
					}
				} else {
					fmParamValue := iohCircleParams.Value + mval.Value
					if _, ok := distribSitePerfMap[param]; ok {
						fmParamValue = iohCircleParams.Value
						if fmParamValue < mval.Value {
							fmParamValue = mval.Value
						}
					}
					iohCircleParams.Value = fmParamValue
				}
			}
		}
	}

	for param, pval := range brandInfo.NationalFmParamMap {
		for mth, mval := range pval {
			iohNationalParams, exists := iohBrandInfo.NationalFmParamMap[param][mth]
			if !exists {
				iohBrandInfo.NationalFmParamMap[param][mth] = &DistribFmData{
					MonthID:   mth,
					Brand:     "IOH",
					Circle:    mval.Circle,
					Region:    mval.Region,
					Parameter: mval.Parameter,
					Value:     mval.Value,
				}
			} else {
				fmParamValue := iohNationalParams.Value + mval.Value
				if _, ok := distribSitePerfMap[param]; ok {
					fmParamValue = iohNationalParams.Value
					if fmParamValue < mval.Value {
						fmParamValue = mval.Value
					}
				}
				iohNationalParams.Value = fmParamValue
			}
		}
	}

	return nil
}

func (dst DistribProcess) processLrsBrandData(lrsData []LrsData, brandInfo *DistribBrandData) error {
	for i, d := range lrsData {
		circle := strings.TrimSpace(d.Circle.String)
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := strings.TrimSpace(d.Region.String)
		mth := d.MonthID

		// process region data
		if _, ok := brandInfo.RegionFmParamMap[region]; ok {
			if _, ok := brandInfo.RegionFmParamMap[region]["LRS"]; !ok {
				brandInfo.RegionFmParamMap[region]["LRS"] = make(map[string]*DistribFmData)
			}
			brandInfo.RegionFmParamMap[region]["LRS"][mth] = &DistribFmData{
				MonthID:   d.MonthID,
				Circle:    d.Circle,
				Region:    d.Region,
				Parameter: "LRS",
				Value:     float64(lrsData[i].GraduateCnt),
			}
		}

		if _, ok := brandInfo.CircleFmParamMap[circle]; ok {
			if _, ok := brandInfo.CircleFmParamMap[circle]["LRS"]; !ok {
				brandInfo.CircleFmParamMap[circle]["LRS"] = make(map[string]*DistribFmData)
			}
			circleParams, exists := brandInfo.CircleFmParamMap[circle]["LRS"][mth]
			if !exists {
				brandInfo.CircleFmParamMap[circle]["LRS"][mth] = &DistribFmData{
					MonthID:   d.MonthID,
					Circle:    d.Circle,
					Region:    d.Region,
					Parameter: "LRS",
					Value:     float64(lrsData[i].GraduateCnt),
				}
			} else {
				circleParams.Value += float64(lrsData[i].GraduateCnt)
			}
		}

		if _, ok := brandInfo.NationalFmParamMap["LRS"]; !ok {
			brandInfo.NationalFmParamMap["LRS"] = make(map[string]*DistribFmData)
		}
		nationalParams, exists := brandInfo.NationalFmParamMap["LRS"][mth]
		if !exists {
			brandInfo.NationalFmParamMap["LRS"][mth] = &DistribFmData{
				MonthID:   d.MonthID,
				Circle:    d.Circle,
				Region:    d.Region,
				Parameter: "LRS",
				Value:     float64(lrsData[i].GraduateCnt),
			}
		} else {
			nationalParams.Value += float64(lrsData[i].GraduateCnt)
		}
	}

	return nil
}

func (dst DistribProcess) mapMtdBrandParamData(data []DistribMtdData) map[string][]DistribMtdData {
	paramMap := make(map[string][]DistribMtdData)
	for i, d := range data {
		brand := d.Brand
		paramMap[brand] = append(paramMap[brand], data[i])
	}

	return paramMap
}

func (dst DistribProcess) mapFmBrandParamData(data []DistribFmData) map[string][]DistribFmData {
	paramMap := make(map[string][]DistribFmData)
	for i, d := range data {
		brand := d.Brand
		paramMap[brand] = append(paramMap[brand], data[i])
	}

	return paramMap
}

func (dst DistribProcess) getSndTarget(c context.Context) ([]SndTarget, error) {
	logger := ctx.ExtractLogger(c)
	exepath := ctx.ExtractRootDir(c)

	sndFPath := filepath.Join(exepath, "snd_target.csv")

	logger.Debug("reading snd target data", "path", sndFPath)

	// check if file exists
	if !utils.FileExists(sndFPath) {
		return nil, fmt.Errorf("file not found: %s", sndFPath)
	}

	f, err := os.Open(sndFPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var sndTargets []SndTarget
	cr := csv.NewReader(f)

	cr.Read() // skip header
	for {
		record, err := cr.Read()

		if errors.Is(err, io.EOF) {
			break
		}

		if err != nil {
			return nil, err
		}

		targetGA, err := strconv.Atoi(record[3])
		if err != nil {
			logger.Error("failed to parse target GA", "error", err)
			continue
		}

		targetSecMn, err := strconv.Atoi(record[4])
		if err != nil {
			logger.Error("failed to parse target SecMn", "error", err)
			continue
		}

		sndTargets = append(sndTargets, SndTarget{
			Circle:    record[0],
			Region:    record[1],
			Brand:     record[2],
			TargetKpi: TargetKpi{TargetGA: targetGA, TargetSecMn: targetSecMn},
		})

	}

	return sndTargets, nil
}

func (dst DistribProcess) getBrandSndTarget(c context.Context) (BrandSndTarget, error) {
	brandData := map[string]struct {
		RegionMap      map[string]*TargetKpi
		CircleMap      map[string]*TargetKpi
		NationalTarget *TargetKpi
		Entries        []EntityTarget
	}{
		"IOH": {
			RegionMap:      make(map[string]*TargetKpi),
			CircleMap:      make(map[string]*TargetKpi),
			NationalTarget: &TargetKpi{},
		},
		"IM3": {
			RegionMap:      make(map[string]*TargetKpi),
			CircleMap:      make(map[string]*TargetKpi),
			NationalTarget: &TargetKpi{},
		},
		"3ID": {
			RegionMap:      make(map[string]*TargetKpi),
			CircleMap:      make(map[string]*TargetKpi),
			NationalTarget: &TargetKpi{},
		},
	}

	sndTargets, err := dst.getSndTarget(c)
	iohBrandInfo := brandData["IOH"]

	if err != nil {
		return BrandSndTarget{}, err
	}

	for _, t := range sndTargets {
		circle := t.Circle
		region := t.Region
		brand := t.Brand

		target := t.TargetKpi

		brandInfo, exists := brandData[brand]
		if !exists {
			continue
		}

		// process region data
		brandInfo.RegionMap[region] = utils.PtrToValCopy(target)

		if _, ok := iohBrandInfo.RegionMap[region]; !ok {
			iohBrandInfo.RegionMap[region] = &TargetKpi{}
		}

		iohBrandInfo.RegionMap[region].TargetGA += target.TargetGA
		iohBrandInfo.RegionMap[region].TargetSecMn += target.TargetSecMn

		// aggregate circle data
		circleTarget := brandInfo.CircleMap[circle]
		if circleTarget == nil {
			brandInfo.CircleMap[circle] = utils.PtrToValCopy(target)
		} else {
			circleTarget.TargetGA += target.TargetGA
			circleTarget.TargetSecMn += target.TargetSecMn
		}

		if _, ok := iohBrandInfo.CircleMap[circle]; !ok {
			iohBrandInfo.CircleMap[circle] = &TargetKpi{}
		}

		iohBrandInfo.CircleMap[circle].TargetGA += target.TargetGA
		iohBrandInfo.CircleMap[circle].TargetSecMn += target.TargetSecMn

		// aggregate national data
		brandInfo.NationalTarget.TargetGA += target.TargetGA
		brandInfo.NationalTarget.TargetSecMn += target.TargetSecMn

		iohBrandInfo.NationalTarget.TargetGA += target.TargetGA
		iohBrandInfo.NationalTarget.TargetSecMn += target.TargetSecMn
	}

	// Build report entries
	for brand, brandInfo := range brandData {
		// Add region entries
		for region, target := range brandInfo.RegionMap {
			brandInfo.Entries = append(brandInfo.Entries, EntityTarget{
				EntityType: "REGION",
				EntityName: region,
				Target:     *target,
			})
		}

		// Add circle entries
		for circle, target := range brandInfo.CircleMap {
			brandInfo.Entries = append(brandInfo.Entries, EntityTarget{
				EntityType: "CIRCLE",
				EntityName: circle,
				Target:     *target,
			})
		}

		// Add national entries
		brandInfo.Entries = append(brandInfo.Entries, EntityTarget{
			EntityType: "NATIONAL",
			EntityName: "NATIONAL",
			Target:     *brandInfo.NationalTarget,
		})

		brandData[brand] = brandInfo
	}

	return BrandSndTarget{
		IOH: brandData["IOH"].Entries,
		IM3: brandData["IM3"].Entries,
		Tri: brandData["3ID"].Entries,
	}, nil
}

func (dst DistribProcess) WriteMtdReport(c context.Context, xl *excelize.File, report *DistribReport) error {
	logger := ctx.ExtractLogger(c)
	conf := ctx.ExtractConfig(c)

	logger.Debug("writing Distrib IM3")
	if err := dst.writeMtdReport(xl, report.IM3, report.Target.IM3, 38); err != nil {
		return err
	}

	logger.Debug("writing Distrib 3ID")
	if err := dst.writeMtdReport(xl, report.Tri, report.Target.Tri, 65); err != nil {
		return err
	}

	logger.Debug("writing Distrib IOH")
	if err := dst.writeMtdReport(xl, report.IOH, report.Target.IOH, 10); err != nil {
		return err
	}

	asofDate := conf.GetTime("work_date")
	asofDateStr := asofDate.Format("02-Jan-2006")

	shname := MTD_SHEET
	xl.SetCellValue(shname, "C2", "data as of "+asofDateStr)

	return nil
}

func (dst DistribProcess) writeMtdReport(xl *excelize.File, data []DistribReportData, target []EntityTarget, startRow int) error {
	shname := MTD_SHEET

	regionData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "REGION" })
	circleData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "CIRCLE" })
	nationalData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "NATIONAL" })

	regionTarget := utils.SliceFilter(target, func(d EntityTarget) bool { return d.EntityType == "REGION" })
	circleTarget := utils.SliceFilter(target, func(d EntityTarget) bool { return d.EntityType == "CIRCLE" })
	nationalTarget := utils.SliceFilter(target, func(d EntityTarget) bool { return d.EntityType == "NATIONAL" })

	regionMap := utils.SliceToMap(regionData, func(d DistribReportData) (string, map[string]DistribMtdData) {
		return d.EntityName, d.ParamMtdDataMap
	})
	circleMap := utils.SliceToMap(circleData, func(d DistribReportData) (string, map[string]DistribMtdData) {
		return d.EntityName, d.ParamMtdDataMap
	})
	nationalMap := utils.SliceToMap(nationalData, func(d DistribReportData) (string, map[string]DistribMtdData) {
		return d.EntityName, d.ParamMtdDataMap
	})

	regionTargetMap := utils.SliceToMap(regionTarget, func(d EntityTarget) (string, TargetKpi) {
		return d.EntityName, d.Target
	})
	circleTargetMap := utils.SliceToMap(circleTarget, func(d EntityTarget) (string, TargetKpi) {
		return d.EntityName, d.Target
	})
	nationalTargetMap := utils.SliceToMap(nationalTarget, func(d EntityTarget) (string, TargetKpi) {
		return d.EntityName, d.Target
	})

	getDistribData := func(entityName string) map[string]DistribMtdData {
		if entityName == "INDONESIA" {
			return nationalMap["NATIONAL"]
		}

		if entityName == "JAYA" {
			entityName = "JAKARTA RAYA"
		}

		distrib, exists := regionMap[entityName]
		if !exists {
			distrib = circleMap[entityName]
		}

		return distrib
	}

	getTargetData := func(entityName string) TargetKpi {
		if entityName == "INDONESIA" {
			return nationalTargetMap["NATIONAL"]
		}

		if entityName == "JAYA" {
			entityName = "JAKARTA RAYA"
		}

		target, exists := regionTargetMap[entityName]
		if !exists {
			target = circleTargetMap[entityName]
		}

		return target
	}

	for i := 0; i < 18; i++ {
		row := startRow + i
		entityName, err := xl.GetCellValue(shname, xlutil.Cell(row, 3).Address())
		if err != nil {
			return err
		}

		entityName = strings.TrimSpace(entityName)

		distrib := getDistribData(entityName)
		if distrib == nil {
			continue
		}

		target := getTargetData(entityName)

		for k, v := range distrib {
			col, ok := distribMtdColMap[k]
			if !ok {
				continue
			}

			xl.SetCellValue(shname, xlutil.Cell(row, col).Address(), v.MTD)
			if !utils.SliceContains([]string{"Secondary", "RGU GA", "DSE", "Addressable Site", "SDP/3KIOSK"}, k) {
				xl.SetCellValue(shname, xlutil.Cell(row, col-1).Address(), v.LMTD)
			}
		}

		xl.SetCellValue(shname, xlutil.Cell(row, distribMtdColMap["Target GA"]).Address(), target.TargetGA/1000)
		xl.SetCellValue(shname, xlutil.Cell(row, distribMtdColMap["Target SecMn"]).Address(), target.TargetSecMn)
	}

	return nil
}

func (dst DistribProcess) WriteFmReport(c context.Context, xl *excelize.File, report *DistribReport) error {
	logger := ctx.ExtractLogger(c)

	logger.Debug("writing FM Distrib IM3")
	if err := dst.writeFmReport(xl, report.IM3, 45); err != nil {
		return err
	}

	logger.Debug("writing FM Distrib 3ID")
	if err := dst.writeFmReport(xl, report.Tri, 75); err != nil {
		return err
	}

	logger.Debug("writing FM Distrib IOH")
	if err := dst.writeFmReport(xl, report.IOH, 9); err != nil {
		return err
	}

	return nil
}

func (dst DistribProcess) writeFmReport(xl *excelize.File, data []DistribReportData, startRow int) error {
	shname := FM_SHEET

	regionData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "REGION" })
	circleData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "CIRCLE" })
	nationalData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "NATIONAL" })

	fmMap := make(map[string]struct{})

	regionMap := utils.SliceToMap(regionData, func(d DistribReportData) (string, map[string]map[string]DistribFmData) {
		for _, v := range d.ParamFmDataMap {
			for month := range v {
				fmMap[month] = struct{}{}
			}
		}
		return d.EntityName, d.ParamFmDataMap
	})

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	sort.Slice(fmList, func(i, j int) bool {
		return fmList[i] > fmList[j]
	})

	circleMap := utils.SliceToMap(circleData, func(d DistribReportData) (string, map[string]map[string]DistribFmData) {
		return d.EntityName, d.ParamFmDataMap
	})
	nationalMap := utils.SliceToMap(nationalData, func(d DistribReportData) (string, map[string]map[string]DistribFmData) {
		return d.EntityName, d.ParamFmDataMap
	})

	getDistribData := func(entityName string) map[string]map[string]DistribFmData {
		if entityName == "INDONESIA" {
			return nationalMap["NATIONAL"]
		}

		if entityName == "JAYA" {
			entityName = "JAKARTA RAYA"
		}

		distrib, exists := regionMap[entityName]
		if !exists {
			distrib = circleMap[entityName]
		}

		return distrib
	}

	for i := 0; i < 18; i++ {
		row := startRow + i
		entityName, err := xl.GetCellValue(shname, xlutil.Cell(row, distrib_fm_entity_col).Address())
		if err != nil {
			return err
		}

		entityName = strings.TrimSpace(entityName)

		distrib := getDistribData(entityName)
		if distrib == nil {
			continue
		}

		for k, v := range distrib {
			col, ok := distribFmColMap[k]
			if !ok {
				continue
			}

			if i == 0 {
				for i, mth := range fmList {
					if i > 2 {
						break
					}

					mthDate, err := time.Parse("200601", mth)
					if err != nil {
						return err
					}

					mthName := mthDate.Format("Jan-06")

					if err := xl.SetCellValue(shname, xlutil.Cell(startRow-1, col+(2-i)).Address(), mthName); err != nil {
						return err
					}
				}
			}

			for i, mth := range fmList {
				if i > 2 {
					break
				}

				fmData, ok := v[mth]
				if !ok {
					continue
				}

				if err := xl.SetCellValue(shname, xlutil.Cell(row, col+(2-i)).Address(), fmData.Value); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

var MTD_SHEET = "LMTD VS MTD"
var FM_SHEET = "Monthly"

var distrib_mtd_entity_col = 3
var distribMtdColMap = map[string]int{
	"Site 3-QSSO":            6,
	"Site 5-QURO":            8,
	"Site w/ <1 GAD/Day":     10,
	"DSE w/ <12 GAD":         13,
	"DSE w/ <5 Mn Secondary": 15,
	"SDP <350 GA":            18,
	"SDP <75 Mn":             20,
	"Secondary":              23,
	"RGU GA":                 25,
	"DSE":                    38,
	"Addressable Site":       39,
	"SDP/3KIOSK":             40,
	"Target GA":              31,
	"Target SecMn":           30,
}

var distribSitePerfMap = map[string]struct{}{
	"Site 3-QSSO":        {},
	"Site 5-QURO":        {},
	"Site w/ <1 GAD/Day": {},
	"Addressable Site":   {},
}

var distrib_fm_entity_col = 3
var distribFmColMap = map[string]int{
	"Site 3-QSSO":            5,
	"Site 5-QURO":            8,
	"Site w/ <1 GAD/Day":     11,
	"DSE w/ <12 GAD":         15,
	"DSE w/ <5 Mn Secondary": 18,
	"LRS":                    21,
	"SDP <350 GA":            26,
	"SDP <75 Mn":             29,
	"Secondary":              33,
	"RGU GA":                 36,
}
