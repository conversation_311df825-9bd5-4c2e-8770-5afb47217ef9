package idm

import (
	"context"
	"fmt"

	"github.com/csee-pm/etl/portal/backend/config"
	"github.com/csee-pm/etl/portal/backend/pkg/errs"
	"github.com/go-ldap/ldap/v3"
	client "github.com/likearthian/go-http/client"
)

type ldapOptions struct {
	netClient client.HttpClient
}

type LdapOption func(o *ldapOptions)

func WithLdapNetClient(netClient client.HttpClient) LdapOption {
	return func(o *ldapOptions) {
		o.netClient = netClient
	}
}

type LdapService struct {
	config config.LdapConfig
}

func NewLdapService(conf config.LdapConfig) *LdapService {

	return &LdapService{config: conf}
}

func (ls *LdapService) Authenticate(c context.Context, username, password string) (*LdapUser, error) {
	l, err := ldap.DialURL(fmt.Sprintf("ldap://%s:%d", ls.config.Host, ls.config.Port))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ldap. %s", err)
	}
	defer l.Close()

	err = l.Bind(username+"@ioh.co.id", password)
	if err != nil {
		return nil, fmt.Errorf("failed to bind to ldap. %s", err)
	}

	searchRequest := ldap.NewSearchRequest(
		ls.config.SearchDN,
		ldap.ScopeWholeSubtree,
		ldap.NeverDerefAliases,
		0,
		0,
		false,
		fmt.Sprintf("(&(objectClass=organizationalPerson)(sAMAccountName=%s))", username),
		[]string{"dn", "cn", "mail", "givenName", "sn"},
		nil,
	)

	sr, err := l.Search(searchRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to search ldap. %s", err)
	}

	if len(sr.Entries) == 0 {
		return nil, fmt.Errorf("LDAP user not found")
	}

	entry := sr.Entries[0]

	err = l.Bind(entry.DN, password)
	if err != nil {
		return nil, fmt.Errorf("LDAP auth failed. %s", err)
	}

	data := new(LdapUser)
	data.ID = username

	for _, attr := range entry.Attributes {
		switch attr.Name {
		case "cn":
			data.Fullname = attr.Values[0]
		case "mail":
			data.Email = attr.Values[0]
		case "sn":
			data.Name = attr.Values[0]
		}
	}

	return data, nil
}

func (ls *LdapService) GetByUsername(c context.Context, username string) (*LdapUser, error) {
	l, err := ldap.DialURL(fmt.Sprintf("ldap://%s:%d", ls.config.Host, ls.config.Port))
	if err != nil {
		return nil, err
	}
	defer l.Close()

	err = l.Bind(ls.config.UserDn, ls.config.Password)
	if err != nil {
		return nil, err
	}

	searchRequest := ldap.NewSearchRequest(
		ls.config.SearchDN,
		ldap.ScopeWholeSubtree,
		ldap.NeverDerefAliases,
		0,
		0,
		false,
		fmt.Sprintf("(&(objectClass=organizationalPerson)(sAMAccountName=%s))", username),
		[]string{"dn", "cn", "sn", "mail"},
		nil,
	)

	sr, err := l.Search(searchRequest)
	if err != nil {
		return nil, fmt.Errorf("ldap failed on search. %s", err.Error())
	}

	if len(sr.Entries) == 0 {
		return nil, errs.ErrKeynotFound
	}
	entry := sr.Entries[0]

	data := new(LdapUser)
	data.ID = username

	for _, attr := range entry.Attributes {
		switch attr.Name {
		case "sn":
			data.Name = attr.Values[0]
		case "mail":
			data.Email = attr.Values[0]
		case "cn":
			data.Fullname = attr.Values[0]
		}
	}

	return data, nil
}
