with outlet as (
    select distinct
        case when trim(upper(circle)) = 'JAYA' then 'JAKARTA RAYA' else trim(upper(circle)) end circle,
        case
            when region = 'HOR_MALUKU PAPUA' then 'MAPA'
            else replace(replace(region,'HOR ',''),'ERN','')
        end region,
        split(replace(kabupaten, 'KAB ', ''), '|')[0] kabupaten,
        kecamatan,
        retailer_qrcode,
        se_partnerid dse,
        mp3_partnerid sdp,
        case when upper(se_category) like '%KIOSK%' then '3KIOSK' else upper(se_category) end se_category,
        cast(mth_id as string) month_id
    from
        `data-nationalslsdist-prd-986g.datamart.snd_outlet_mapping`
    where
        mth_id >= cast(format_date('%Y%m', date_add(date(${mtd_dt}), interval -3 month)) as int)
    and mth_id <= cast(format_date('%Y%m', date(${mtd_dt})) as int)
),
base as (
    select
        coalesce(ga.dt, sec.dt) dt,
        format_date('%Y%m', coalesce(ga.dt, sec.dt)) month_id,
        coalesce(ga.partner_qr_cd, sec.partner_qr_cd) partner_qr_code,
        coalesce(ga.ga,0) ga,
        coalesce (sec.secondary,0)/1.11 net_secondary
    from
    (
        select
            parse_date('%Y%m%d', cast(load_dt_sk_id as string)) dt,
            partner_qr_cd,
            count(distinct sbscrptn_ek_id) ga
        from
            `data-nationalslsdist-prd-986g.datamart.and_snd_rgu_ga_new_detail`
        where
            load_dt_sk_id >= cast(format_date('%Y%m%d', date_add(date_trunc(date(${mtd_dt}), month), interval -4 month)) as int)
        and load_dt_sk_id <= cast(format_date('%Y%m%d', date(${mtd_dt})) as int)
        group by 1,2
    ) ga
    full outer join
    (
        select
            dt_sk_id dt,
            partner_qr_cd,
            cast(sum(value) as float64) as secondary
        from
            `data-dtptechm-prd-c7ca.mis.project_ioh_secondary_outlet` a
        where
            dt_sk_id >= date_trunc(date_add(date(${mtd_dt}), interval -4 month), month)
        and dt_sk_id <= date(${mtd_dt})
        and secondary_category = 'SALDO'
        and hierarchy_type = 'ANGIE'
        and secondary_type in ('PRT_CUANWEB','Purchase from CAN','Purchase from MP3')
        group by partner_qr_cd, dt_sk_id
    )sec
    on
        ga.partner_qr_cd = sec.partner_qr_cd
    and ga.dt = sec.dt
)
select
    a.month_id,
    case
        when coalesce(b.month_id, a.month_id) = format_date('%Y%m', date(${mtd_dt})) then 'MTD'
        else 'LMTD'
    end period,
    '3ID' brand,
    a.circle,
    a.region,
    a.kabupaten,
    se_category flag,
    sdp dist_id,
    cast(format_date('%d', date(${mtd_dt})) as int) day_no,
    format_date('%Y%m%d', max(b.dt)) asof_date,
	sum(coalesce(ga,0)) ga,
	round(sum(coalesce(b.net_secondary, 0))/pow(10,6), 3) as net_secondary_mn,
from
    outlet a
    left join
    base b
    on
        a.retailer_qrcode = b.partner_qr_code
    and a.month_id = b.month_id
where
    b.dt >= date_trunc(date_add(date(${mtd_dt}), interval -1 month), month)
and format_date('%d', b.dt) <= format_date('%d', date(${mtd_dt}))
group by 1,2,4,5,6,7,8

UNION ALL

select
    a.month_id,
    'FM' period,
    '3ID' brand,
    a.circle,
    a.region,
    a.kabupaten,
    se_category flag,
    sdp dist_id,
    cast(format_date('%d', date_add(date_add(date_trunc(date(${mtd_dt}), month), interval 1 month), interval -1 day)) as int) day_no,
    format_date('%Y%m%d', max(b.dt)) asof_date,
	sum(coalesce(ga,0)) ga,
	round(sum(coalesce(b.net_secondary, 0))/pow(10,6), 3) as net_secondary_mn
from
    outlet a
    left join
    base b
    on
        a.retailer_qrcode = b.partner_qr_code
    and a.month_id = b.month_id
where
    b.dt < date_trunc(date(${mtd_dt}), month)
group by 1,4,5,6,7,8,9;