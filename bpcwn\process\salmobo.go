package process

// import (
// 	"context"
// 	"encoding/csv"
// 	"fmt"
// 	"io"
// 	"os"
// 	"strconv"
// 	"strings"
// 	"sync"
// 	"time"

// 	cfg "github.com/csee-pm/etl/bpcwn/config"
// 	"github.com/csee-pm/etl/shared/channel"
// 	ctx "github.com/csee-pm/etl/shared/context"
// 	etlDb "github.com/csee-pm/etl/shared/db"
// 	etlProc "github.com/csee-pm/etl/shared/process"
// 	"github.com/csee-pm/etl/shared/apiutil"
// 	"github.com/csee-pm/etl/shared/apiutil/xlutil"
// 	"github.com/xuri/excelize/v2"
// 	"gopkg.in/guregu/null.v4"
// )

// var pjpfilterfile = "pjp_outlets.csv"
// var salmoboBins = []float64{0, 10_000, 50_000, 100_000, 200_000, 500_000, 1_000_000}
// var salmoboLabels = []string{"0", "<10k", "10k-50k", "50k-100k", "100k-200k", "200k-500k", "500k-1M", ">1M"}
// var salmoboSlabColNames = []string{"slab_0", "slab_10k", "slab_10k_50k", "slab_50k_100k", "slab_100k_200k", "slab_200k_500k", "slab_500k_1m", "slab_1m"}

// type salmoboProcess struct {
// 	bins   []float64
// 	labels []string
// }

// func NewSalmoboProcess() salmoboProcess {
// 	return salmoboProcess{
// 		bins:   salmoboBins,
// 		labels: salmoboLabels,
// 	}
// }

// func (sal salmoboProcess) GetReportData(c context.Context) (*SalmoboReport, error) {
// 	conf := ctx.ExtractConfig(c)
// 	workDate := time.Now().AddDate(0, 0, -2)

// 	var err error

// 	if conf.Get("etl.work_date") != nil {
// 		workDate, err = time.Parse("20060102", conf.GetString("etl.work_date"))
// 		if err != nil {
// 			return nil, fmt.Errorf("failed to parse work_date. %s", err)
// 		}
// 	}

// 	endDt := workDate

// }

// func (sal salmoboProcess) RunSalmobo(c context.Context) (*SalmoboReport, error) {
// 	conf := ctx.ExtractConfig(c)
// 	workDate := time.Now().AddDate(0, 0, -2)

// 	var err error
// 	if conf.Get("etl.work_date") != nil {
// 		workDate, err = time.Parse("20060102", conf.GetString("etl.work_date"))
// 		if err != nil {
// 			return nil, fmt.Errorf("failed to parse work_date. %s", err)
// 		}
// 	}

// 	endDt := workDate

// 	logger := ctx.ExtractLogger(c)

// 	fromFile := cfg.UseSalmoboFromFile
// 	if fromFile != "" {
// 		return sal.RunSalmoboFromFile(c)
// 	}

// 	var wg sync.WaitGroup
// 	cCancel, cancel := context.WithCancel(c)

// 	wg.Add(1)
// 	var salmoData []SalmoboData
// 	go func() {
// 		defer wg.Done()
// 		salmoData, err = sal.getSalmoboData(cCancel, startDt, endDt)
// 		if err != nil {
// 			logger.Error("failed to get Salmobo data", "error", err)
// 			logger.Debug("calling cancel", "caller", "sal.getSalmoboData")
// 			cancel()
// 		}
// 	}()

// 	wg.Wait()
// 	if err != nil {
// 		return nil, err
// 	}

// 	logger.Debug("Salmobo data fetched", "count", len(salmoData))

// 	return sal.createReportData(c, salmoData)
// }

// func (sal salmoboProcess) RunSalmoboFromFile(c context.Context) (*SalmoboReport, error) {
// 	fromFile := cfg.UseSalmoboFromFile
// 	if fromFile == "" {
// 		return nil, fmt.Errorf("from_salmobo_file is not set")
// 	}

// 	salmoData, err := sal.readSalmoboDataFromFile(fromFile)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to read salmobo data from file. %s", err)
// 	}

// 	return sal.createReportData(c, salmoData)
// }

// func (sal salmoboProcess) createReportData(c context.Context, salmoData []SalmoboData) (*SalmoboReport, error) {
// 	conf := ctx.ExtractConfig(c)
// 	logger := ctx.ExtractLogger(c)

// 	workDir := ctx.ExtractWorkDir(c)

// 	if conf.Get("pjpfilterfile") != nil {
// 		pjpfilterfile = conf.GetString("pjpfilterfile")
// 	}

// 	logger.Debug("starting data post-process")
// 	processedData, err := sal.postProcessData(salmoData)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to post-process salmobo data. %s", err)
// 	}
// 	logger.Debug("data post-process finished", "count", len(processedData))

// 	csvFilePath := fmt.Sprintf("%s/salmobo_%s.csv", workDir, time.Now().Format("20060102150405"))
// 	processedCsvFilePath := fmt.Sprintf("%s/mean_salmobo_%s.csv", workDir, time.Now().Format("20060102150405"))

// 	if err := apiutil.WriteToCsv(csvFilePath, salmoData); err != nil {
// 		logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
// 		return nil, err
// 	}
// 	logger.Info("CSV file written", "path", csvFilePath)

// 	if err := apiutil.WriteToCsv(processedCsvFilePath, processedData); err != nil {
// 		logger.Error("failed to write CSV file", "path", processedCsvFilePath, "error", err)
// 	}
// 	logger.Info("CSV file written", "path", processedCsvFilePath)

// 	reportData := sal.createRegionalSalmoboData(processedData)
// 	return &reportData, nil
// }

// func (sal salmoboProcess) readSalmoboDataFromFile(fromFile string) ([]SalmoboData, error) {
// 	f, err := os.Open(fromFile)
// 	if err != nil {
// 		return nil, err
// 	}
// 	defer f.Close()

// 	cr := csv.NewReader(f)
// 	cr.Comma = ','
// 	cr.LazyQuotes = true

// 	cr.Read()
// 	var salmoData []SalmoboData
// 	for {
// 		record, err := cr.Read()
// 		if err == io.EOF {
// 			break
// 		}

// 		if err != nil {
// 			return nil, err
// 		}

// 		amount, err := strconv.ParseFloat(record[5], 64)
// 		if err != nil {
// 			return nil, fmt.Errorf("failed to parse saldo amount. %s", err)
// 		}

// 		salmoData = append(salmoData, SalmoboData{
// 			Brand:          record[1],
// 			OrganizationId: record[2],
// 			Circle:         null.StringFrom(record[3]),
// 			Region:         null.StringFrom(record[4]),
// 			SaldoAmount:    amount,
// 			DtID:           record[0],
// 		})
// 	}

// 	return salmoData, nil
// }

// func (sal salmoboProcess) getSalmoboData(c context.Context, endDt time.Time) ([]SalmoboData, error) {
// 	logger := ctx.ExtractLogger(c)

// 	var im3Data []SalmoboData
// 	var threeData []SalmoboData
// 	var err error

// 	cCancel, cancel := context.WithCancel(c)
// 	defer cancel()

// 	var wg sync.WaitGroup

// 	wg.Add(1)
// 	im3Result := channel.RunAsyncContext(cCancel, func() ([]SalmoboData, error) {
// 		return sal.getIM3Salmobo(cCancel, endDt.Format("20060102"))
// 	})

// 	go func() {
// 		defer wg.Done()
// 		for res := range im3Result {
// 			res.Map(func(data []SalmoboData) {
// 				im3Data = data
// 			}).MapErr(func(er error) {
// 				err = fmt.Errorf("failed to get IM3 data. %s", er)
// 				logger.Debug("calling cancel", "caller", "salmobo.getIM3Salmobo", "error", err)
// 				cancel()
// 			})
// 		}
// 	}()

// 	wg.Add(1)
// 	threeResult := channel.RunAsyncContext(cCancel, func() ([]SalmoboData, error) {
// 		end, er := strconv.Atoi(endDt.Format("20060102"))
// 		if er != nil {
// 			return nil, er
// 		}
// 		return sal.get3idSalmobo(cCancel, end)
// 	})

// 	go func() {
// 		defer wg.Done()
// 		for res := range threeResult {
// 			res.Map(func(data []SalmoboData) {
// 				threeData = data
// 			}).MapErr(func(er error) {
// 				err = fmt.Errorf("failed to get 3ID data. %s", er)
// 				logger.Debug("calling cancel", "caller", "salmobo.get3idSalmobo", "error", err)
// 				cancel()
// 			})
// 		}
// 	}()

// 	wg.Wait()
// 	if err != nil {
// 		return nil, err
// 	}

// 	return append(im3Data, threeData...), nil
// }

// func (sal salmoboProcess) getIM3Salmobo(c context.Context, endDt string) ([]SalmoboData, error) {
// 	buf, err := procFS.ReadFile("files/im3_salmobo.sql")
// 	if err != nil {
// 		return nil, err
// 	}

// 	params := map[string]*etlDb.ParamValue{
// 		"start_dt_id": {Name: "start_dt_id", Value: startDt},
// 		"end_dt_id":   {Name: "end_dt_id", Value: endDt},
// 		"dt_id_list":  {Name: "dt_id_list", Value: dtIdList},
// 	}

// 	return etlProc.QueryImpalaData[SalmoboData](c, string(buf), params)
// }

// func (sal salmoboProcess) get3idSalmobo(c context.Context, endDt int) ([]SalmoboData, error) {
// 	buf, err := procFS.ReadFile("files/3id_salmobo.sql")
// 	if err != nil {
// 		return nil, err
// 	}

// 	params := map[string]*etlDb.ParamValue{
// 		"start_dt_int": {Name: "start_dt_int", Value: startDt},
// 		"end_dt_int":   {Name: "end_dt_int", Value: endDt},
// 		"dt_list":      {Name: "dt_list", Value: dtList},
// 	}

// 	return etlProc.QueryGreenplumData[SalmoboData](c, string(buf), params)
// }

// func (sal salmoboProcess) postProcessData(data []SalmoboData) ([]FinalSalmoboData, error) {
// 	var dataMap = make(map[string]*FinalSalmoboData)
// 	for _, d := range data {
// 		key := fmt.Sprintf("%s|%s|%s|%s", d.Brand, d.OrganizationId, d.Circle.String, d.Region.String)
// 		if _, ok := dataMap[key]; !ok {
// 			dataMap[key] = &FinalSalmoboData{
// 				Brand:          d.Brand,
// 				OrganizationId: d.OrganizationId,
// 				Circle:         d.Circle.String,
// 				Region:         d.Region.String,
// 			}
// 		}
// 		dataItem := dataMap[key]
// 		dataItem.AddSaldoAmount(d.SaldoAmount)
// 		dataItem.DtIds = append(dataItem.DtIds, d.DtID)
// 	}

// 	ret := make([]FinalSalmoboData, len(dataMap))
// 	for i := range dataMap {
// 		d := dataMap[i]
// 		bin := sal.getBins(d.AvgSaldoAmount)
// 		d.Slab = sal.labels[bin]

// 		ret = append(ret, *d)
// 	}
// 	return sal.filterPjp(ret)
// }

// func (sal salmoboProcess) filterPjp(data []FinalSalmoboData) ([]FinalSalmoboData, error) {
// 	f, err := os.Open(pjpfilterfile)
// 	if err != nil {
// 		return nil, err
// 	}
// 	defer f.Close()

// 	cr := csv.NewReader(f)
// 	cr.Comma = ','
// 	cr.LazyQuotes = true

// 	var pjpMap = make(map[string]struct{})
// 	cr.Read()
// 	for {
// 		record, err := cr.Read()
// 		if err == io.EOF {
// 			break
// 		}
// 		if err != nil {
// 			return nil, err
// 		}
// 		key := strings.Join(record, "|")
// 		pjpMap[key] = struct{}{}
// 	}

// 	var filteredData []FinalSalmoboData
// 	for i, d := range data {
// 		if _, ok := pjpMap[fmt.Sprintf("%s|%s", d.OrganizationId, d.Brand)]; ok {
// 			filteredData = append(filteredData, data[i])
// 		}
// 	}

// 	return filteredData, nil
// }

// func (sal salmoboProcess) getBins(saldoAmount float64) int {
// 	for i, b := range sal.bins {
// 		if saldoAmount <= b {
// 			return i
// 		}
// 	}
// 	return len(sal.labels) - 1
// }

// func (sal salmoboProcess) createRegionalSalmoboData(data []FinalSalmoboData) SalmoboReport {
// 	dIM3 := &SalmoboReportData{
// 		Regional: make(map[string]map[string]int),
// 		Circle:   make(map[string]map[string]int),
// 	}

// 	d3ID := &SalmoboReportData{
// 		Regional: make(map[string]map[string]int),
// 		Circle:   make(map[string]map[string]int),
// 	}

// 	dtIds := data[0].DtIds
// 	dt, _ := time.Parse("20060102", dtIds[0])
// 	var start = dt
// 	var end = dt
// 	for _, d := range dtIds {
// 		dt, _ = time.Parse("20060102", d)
// 		if start.After(dt) {
// 			start = dt
// 		}

// 		if end.Before(dt) {
// 			end = dt
// 		}
// 	}

// 	for _, d := range data {
// 		dmap := dIM3
// 		if d.Brand == "3ID" {
// 			dmap = d3ID
// 		}

// 		regmap := dmap.Regional
// 		circlemap := dmap.Circle

// 		if _, ok := regmap[d.Region]; !ok {
// 			regmap[d.Region] = make(map[string]int)
// 			for i := range sal.labels {
// 				regmap[d.Region][sal.labels[i]] = 0
// 			}
// 		}

// 		if _, ok := circlemap[d.Circle]; !ok {
// 			circlemap[d.Circle] = make(map[string]int)
// 			for i := range sal.labels {
// 				circlemap[d.Circle][sal.labels[i]] = 0
// 			}
// 		}

// 		regmap[d.Region][d.Slab]++
// 		circlemap[d.Circle][d.Slab]++
// 	}

// 	return SalmoboReport{
// 		ReportStartDate: start,
// 		ReportEndDate:   end,
// 		IM3:             *dIM3,
// 		Three:           *d3ID,
// 	}
// }

// func (sal salmoboProcess) writeReport(xl *excelize.File, data *SalmoboReport) error {
// 	shname := "SALDO MOBO"
// 	startIM3 := xlutil.Cell(7, 3)
// 	start3ID := xlutil.Cell(28, 3)

// 	dIM3 := data.IM3
// 	d3ID := data.Three

// 	//do IM3 report
// 	rownum := len(dIM3.Regional) + len(dIM3.Circle)
// 	startRow := startIM3.Row
// 	startCol := startIM3.Col
// 	dNational := make(map[string]int)
// 	for _, slab := range sal.labels {
// 		dNational[slab] = 0
// 	}

// 	for i := 0; i < rownum; i++ {
// 		entity, err := xl.GetCellValue(shname, xlutil.Cell(startRow+i, startCol-1).Address())
// 		if err != nil {
// 			return err
// 		}
// 		entity = strings.TrimSpace(entity)
// 		if entity == "" {
// 			continue
// 		}

// 		var slabs map[string]int
// 		if _, ok := dIM3.Regional[entity]; ok {
// 			slabs = dIM3.Regional[entity]
// 		}

// 		if _, ok := dIM3.Circle[entity]; ok {
// 			slabs = dIM3.Circle[entity]
// 			for _, slab := range sal.labels {
// 				dNational[slab] += slabs[slab]
// 			}
// 		}

// 		if slabs != nil {
// 			total := 0
// 			for idx, slab := range sal.labels {
// 				total += slabs[slab]
// 				if err := xl.SetCellValue(shname, xlutil.Cell(startRow+i, startCol+idx).Address(), slabs[slab]); err != nil {
// 					return err
// 				}
// 			}
// 			if err := xl.SetCellValue(shname, xlutil.Cell(startRow+i, startCol+len(sal.labels)).Address(), total); err != nil {
// 				return err
// 			}
// 		}

// 		if entity == "INDONESIA" {
// 			total := 0
// 			for idx, slab := range sal.labels {
// 				total += dNational[slab]
// 				if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+idx).Address(), dNational[slab]); err != nil {
// 					return err
// 				}
// 			}
// 			if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+len(sal.labels)).Address(), total); err != nil {
// 				return err
// 			}
// 		}
// 	}

// 	//do 3ID report
// 	rownum = len(d3ID.Regional) + len(d3ID.Circle)
// 	startRow = start3ID.Row
// 	startCol = start3ID.Col
// 	dNational = make(map[string]int)
// 	for _, slab := range sal.labels {
// 		dNational[slab] = 0
// 	}
// 	for i := 0; i < rownum; i++ {
// 		entity, err := xl.GetCellValue(shname, xlutil.Cell(startRow+i, startCol-1).Address())
// 		if err != nil {
// 			return err
// 		}
// 		entity = strings.TrimSpace(entity)

// 		if entity == "" {
// 			continue
// 		}

// 		var slabs map[string]int
// 		if _, ok := d3ID.Regional[entity]; ok {
// 			slabs = d3ID.Regional[entity]
// 		}

// 		if _, ok := d3ID.Circle[entity]; ok {
// 			slabs = d3ID.Circle[entity]
// 			for _, slab := range sal.labels {
// 				dNational[slab] += slabs[slab]
// 			}
// 		}

// 		if slabs != nil {
// 			total := 0
// 			for idx, slab := range sal.labels {
// 				total += slabs[slab]
// 				if err := xl.SetCellValue(shname, xlutil.Cell(startRow+i, startCol+idx).Address(), slabs[slab]); err != nil {
// 					return err
// 				}
// 			}
// 			if err := xl.SetCellValue(shname, xlutil.Cell(startRow+i, startCol+len(sal.labels)).Address(), total); err != nil {
// 				return err
// 			}
// 		}

// 		if entity == "INDONESIA" {
// 			total := 0
// 			for idx, slab := range sal.labels {
// 				total += dNational[slab]
// 				if err := xl.SetCellValue(shname, xlutil.Cell(startRow+i, startCol+idx).Address(), dNational[slab]); err != nil {
// 					return err
// 				}
// 			}
// 			if err := xl.SetCellValue(shname, xlutil.Cell(startRow+i, startCol+len(sal.labels)).Address(), total); err != nil {
// 				return err
// 			}
// 		}
// 	}

// 	dateStr := fmt.Sprintf("%s - %s", data.ReportStartDate.Format("02 Jan"), data.ReportEndDate.Format("02 Jan 2006"))
// 	if err := xl.SetCellValue(shname, "B3", dateStr); err != nil {
// 		return err
// 	}

// 	return nil
// }

// func getDateList(startDate, endDate time.Time) []time.Time {
// 	var dateList []time.Time

// 	for date := startDate; date.Before(endDate) || date.Equal(endDate); date = date.AddDate(0, 0, 1) {
// 		dateList = append(dateList, date)
// 	}
// 	return dateList
// }

// func getWeeklyDateList(startDate, endDate time.Time) []time.Time {
// 	var dateList []time.Time
// 	dayList := []int{7, 14, 21, 28}

// 	for date := startDate; date.Before(endDate); date = date.AddDate(0, 0, 1) {
// 		if apiutil.SliceContains(dayList, date.Day()) {
// 			dateList = append(dateList, date)
// 		}
// 	}
// 	return dateList
// }
