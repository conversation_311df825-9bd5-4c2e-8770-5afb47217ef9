package process

import (
	"strconv"

	"gopkg.in/guregu/null.v4"
)

type PstKabuKpi struct {
	Secondary null.Float `db:"secondary"`
	Tertiary  null.Float `db:"tertiary"`
	Site3QSSO null.Float `db:"site_3qsso"`
}

func NewPstKabuKpi() *PstKabuKpi {
	return &PstKabuKpi{
		Secondary: null.FloatFrom(0),
		Tertiary:  null.FloatFrom(0),
		Site3QSSO: null.FloatFrom(0),
	}
}

type PstKabuData struct {
	MonthID   string      `db:"month_id"`
	Period    string      `db:"period"`
	AsofDate  string      `db:"asof_date"`
	Circle    null.String `db:"circle"`
	Region    null.String `db:"region"`
	Kabupaten null.String `db:"kabupaten"`
	KabuFlag  null.String `db:"flag"`
	Brand     string      `db:"brand"`
	PstKabuKpi
}

func (p PstKabuData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "kabupaten", "flag", "brand", "secondary", "tertiary", "site_3qsso"}
}

func (p PstKabuData) GetRowValues() []string {
	secondary := ""
	if p.Secondary.Valid {
		secondary = strconv.FormatFloat(p.Secondary.ValueOrZero(), 'f', -1, 64)
	}

	tertiary := ""
	if p.Tertiary.Valid {
		tertiary = strconv.FormatFloat(p.Tertiary.ValueOrZero(), 'f', -1, 64)
	}
	site3qsso := ""
	if p.Site3QSSO.Valid {
		site3qsso = strconv.FormatFloat(p.Site3QSSO.ValueOrZero(), 'f', -1, 64)
	}

	return []string{p.MonthID, p.Period, p.AsofDate, p.Circle.String, p.Region.String, p.Kabupaten.String, p.KabuFlag.String, p.Brand, secondary, tertiary, site3qsso}
}

type RegionalPstKabuData struct {
	EntityType string
	EntityName string
	KabuCount  int
	MTD        *PstKabuKpi
	LMTD       *PstKabuKpi
	FM         map[string]*PstKabuKpi
}

func NewRegionalPstKabuData(entityType, entityName string) *RegionalPstKabuData {
	return &RegionalPstKabuData{
		EntityType: entityType,
		EntityName: entityName,
		MTD:        NewPstKabuKpi(),
		LMTD:       NewPstKabuKpi(),
		FM:         make(map[string]*PstKabuKpi),
		KabuCount:  0,
	}
}

type PstKabuReportData struct {
	AsofDate        string
	FmList          []string
	CircleMap       map[string]*RegionalPstKabuData
	RegionalMap     map[string]*RegionalPstKabuData
	RegionalKabuMap map[string]map[string]*RegionalPstKabuData
	NationalData    *RegionalPstKabuData
	NationalKabuMap map[string]*RegionalPstKabuData
}

func NewPstKabuReportData() *PstKabuReportData {
	return &PstKabuReportData{
		CircleMap:       make(map[string]*RegionalPstKabuData),
		RegionalMap:     make(map[string]*RegionalPstKabuData),
		RegionalKabuMap: make(map[string]map[string]*RegionalPstKabuData),
		NationalData:    NewRegionalPstKabuData("NATIONAL", "INDONESIA"),
		NationalKabuMap: make(map[string]*RegionalPstKabuData),
	}
}

type PstKabuReport struct {
	IOH   *PstKabuReportData
	IM3   *PstKabuReportData
	Three *PstKabuReportData
}
