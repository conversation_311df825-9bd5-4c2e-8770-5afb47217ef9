package salmobo

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"io/fs"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	cfg "github.com/csee-pm/etl/bpcwn/config"
	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

var (
	bins  = []float64{0, 10_000, 50_000, 100_000, 200_000, 500_000, 1_000_000}
	slabs = []string{"slab_0", "slab_10k", "slab_10k_50k", "slab_50k_100k", "slab_100k_200k", "slab_200k_500k", "slab_500k_1m", "slab_1m"}
)

type SalmoboProcess struct {
	procFS fs.ReadFileFS
}

func NewSalmoboProcess(procFS fs.ReadFileFS) *SalmoboProcess {
	return &SalmoboProcess{
		procFS: procFS,
	}
}

func (sal SalmoboProcess) GetReportData(c context.Context, workDate time.Time) ([]SalmoboData, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		workDate, err = time.Parse("20060102", conf.GetString("etl.work_date"))
		if err != nil {
			return nil, fmt.Errorf("failed to parse work_date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	var data []SalmoboData

	if cfg.UseSalmoboFromFile != "" {
		data, err = sal.getSalmoboDataFromFile(c, cfg.UseSalmoboFromFile)
	} else {
		data, err = sal.getSalmoboData(c)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get salmobo data. %s", err)
	}

	workDir := ctx.ExtractWorkDir(c)
	csvFilePath := fmt.Sprintf("%s/salmobo_%s.csv", workDir, time.Now().Format("20060102150405"))

	if cfg.UseSalmoboFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, data); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	return data, nil
}

func (sal SalmoboProcess) getSalmoboDataFromFile(c context.Context, fromFile string) ([]SalmoboData, error) {
	f, err := os.Open(fromFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()
	var salmoData []SalmoboData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}

		wk, err := strconv.Atoi(record[1])
		if err != nil {
			return nil, fmt.Errorf("failed to parse wk. %s", err)
		}

		slab0, err := strconv.Atoi(record[6])
		if err != nil {
			return nil, fmt.Errorf("failed to parse slab_0. %s", err)
		}

		slab10k, err := strconv.Atoi(record[7])
		if err != nil {
			return nil, fmt.Errorf("failed to parse slab_10k. %s", err)
		}

		slab10k_50k, err := strconv.Atoi(record[8])
		if err != nil {
			return nil, fmt.Errorf("failed to parse slab_10k_50k. %s", err)
		}

		slab50k_100k, err := strconv.Atoi(record[9])
		if err != nil {
			return nil, fmt.Errorf("failed to parse slab_50k_100k. %s", err)
		}

		slab100k_200k, err := strconv.Atoi(record[10])
		if err != nil {
			return nil, fmt.Errorf("failed to parse slab_100k_200k. %s", err)
		}

		slab200k_500k, err := strconv.Atoi(record[11])
		if err != nil {
			return nil, fmt.Errorf("failed to parse slab_200k_500k. %s", err)
		}

		slab500k_1m, err := strconv.Atoi(record[12])
		if err != nil {
			return nil, fmt.Errorf("failed to parse slab_500k_1m. %s", err)
		}

		slab1m, err := strconv.Atoi(record[13])
		if err != nil {
			return nil, fmt.Errorf("failed to parse slab_1m. %s", err)
		}

		salmoData = append(salmoData, SalmoboData{
			MonthID:       record[0],
			Wk:            wk,
			Circle:        null.StringFrom(record[2]),
			Region:        null.StringFrom(record[3]),
			Kabupaten:     null.StringFrom(record[4]),
			Brand:         record[5],
			Slab0:         slab0,
			Slab10k:       slab10k,
			Slab10k_50k:   slab10k_50k,
			Slab50k_100k:  slab50k_100k,
			Slab100k_200k: slab100k_200k,
			Slab200k_500k: slab200k_500k,
			Slab500k_1m:   slab500k_1m,
			Slab1m:        slab1m,
		})
	}

	return salmoData, nil
}

func (sal SalmoboProcess) getSalmoboData(c context.Context) ([]SalmoboData, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	end_date := conf.GetTime("work_date")
	endDateInt, err := strconv.Atoi(end_date.Format("20060102"))
	if err != nil {
		return nil, err
	}

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var wg sync.WaitGroup
	var im3Data []SalmoboData
	var threeData []SalmoboData

	wg.Add(1)
	im3Result := channel.RunAsyncContext(cCancel, func() ([]SalmoboData, error) {
		return sal.getIm3SalmoboData(cCancel, endDateInt)
	})

	go func() {
		defer wg.Done()
		for res := range im3Result {
			res.Map(func(data []SalmoboData) {
				im3Data = data
			}).MapErr(func(er error) {
				err = er
				logger.Error("failed to get IM3 Salmobo data", "error", er)
				cancel()
			})
		}
	}()

	wg.Add(1)
	threeResult := channel.RunAsyncContext(cCancel, func() ([]SalmoboData, error) {
		return sal.get3idSalmoboData(cCancel, endDateInt)
	})

	go func() {
		defer wg.Done()
		for res := range threeResult {
			res.Map(func(data []SalmoboData) {
				threeData = data
			}).MapErr(func(er error) {
				err = er
				logger.Error("failed to get 3ID Salmobo data", "error", er)
				cancel()
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, err
	}

	return append(im3Data, threeData...), nil
}

func (sal SalmoboProcess) getIm3SalmoboData(c context.Context, endDt int) ([]SalmoboData, error) {
	logger := ctx.ExtractLogger(c)

	logger.Info("Getting IM3 Salmobo data")
	qry, err := sal.CreateIm3SalmoboQuery()
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"end_dt_id": {Name: "end_dt_id", Value: strconv.Itoa(endDt)},
	}

	return etlProc.QueryImpalaData[SalmoboData](c, qry, params)
}

func (sal SalmoboProcess) CreateIm3SalmoboQuery() (string, error) {
	var binIm3 []string
	var binIm3Cnt []string

	for i, thr := range bins {
		slab := slabs[i]

		binIm3 = append(binIm3, fmt.Sprintf("when avg(a.saldo_amount) <= %v then '%s'", thr, slab))
		binIm3Cnt = append(binIm3Cnt, fmt.Sprintf("sum(case when slab = '%s' then 1 else 0 end) as \"%s\"", slab, slab))
	}

	binIm3 = append(binIm3, fmt.Sprintf("else '%s' end ", slabs[len(slabs)-1]))
	binIm3Cnt = append(binIm3Cnt, fmt.Sprintf("sum(case when slab = '%s' then 1 else 0 end) as \"%s\"", slabs[len(slabs)-1], slabs[len(slabs)-1]))

	binIm3Str := fmt.Sprintf("CASE %s", strings.Join(binIm3, " "))
	binIm3CntStr := strings.Join(binIm3Cnt, ", ")

	buf, err := sal.procFS.ReadFile("files/im3_salmobo_kabu.sql")
	if err != nil {
		return "", err
	}

	qry := strings.Replace(string(buf), "${binIm3Str}", binIm3Str, -1)
	qry = strings.Replace(qry, "${binIm3CntStr}", binIm3CntStr, -1)

	return qry, nil
}

func (sal SalmoboProcess) get3idSalmoboData(c context.Context, endDt int) ([]SalmoboData, error) {
	logger := ctx.ExtractLogger(c)

	logger.Info("Getting 3ID Salmobo data")
	qry, err := sal.Create3idSalmoboQuery()
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"end_dt_int": {Name: "end_dt_int", Value: endDt},
	}

	return etlProc.QueryGreenplumData[SalmoboData](c, qry, params)
}

func (sal SalmoboProcess) Create3idSalmoboQuery() (string, error) {
	var bin3id []string
	var bin3idCnt []string

	for i, thr := range bins {
		slab := slabs[i]

		bin3id = append(bin3id, fmt.Sprintf("when avg(a.saldo_amount) <= %v then '%s'", thr, slab))
		bin3idCnt = append(bin3idCnt, fmt.Sprintf("sum(case when slab = '%s' then 1 else 0 end) as \"%s\"", slab, slab))
	}

	bin3id = append(bin3id, fmt.Sprintf("else '%s' end ", slabs[len(slabs)-1]))
	bin3idCnt = append(bin3idCnt, fmt.Sprintf("sum(case when slab = '%s' then 1 else 0 end) as \"%s\"", slabs[len(slabs)-1], slabs[len(slabs)-1]))

	bin3idStr := fmt.Sprintf("CASE %s", strings.Join(bin3id, " "))
	bin3idCntStr := strings.Join(bin3idCnt, ", ")

	buf, err := sal.procFS.ReadFile("files/3id_salmobo_kabu.sql")
	if err != nil {
		return "", err
	}

	qry := strings.Replace(string(buf), "${bin3idStr}", bin3idStr, -1)
	qry = strings.Replace(qry, "${bin3idCntStr}", bin3idCntStr, -1)

	return qry, nil
}

var (
	SALMOBO_DATA_SHEET = "salmo_tracker_data"
	TABLENAME          = "salmo_tracker"
)

func (sal SalmoboProcess) WriteSalmoReport(c context.Context, xl *excelize.File, data []SalmoboData) error {
	var tbl excelize.Table
	tableFound := false
	tables, err := xl.GetTables(SALMOBO_DATA_SHEET)
	if err != nil {
		return err
	}

	for _, t := range tables {
		if t.Name == TABLENAME {
			tbl = t
			tableFound = true
			break
		}
	}

	if !tableFound {
		return fmt.Errorf("table salmo_tracker not found")
	}

	//tblRange, err := xlutil.StringToRange(tbl.Range)
	//if err != nil {
	//	return err
	//}
	//
	//endRow := tblRange.EndCell.Row
	//newRows := len(data) - endRow + 1
	//if newRows > 0 {
	//	if err := xl.InsertRows(SALMOBO_DATA_SHEET, endRow+1, newRows); err != nil {
	//		return err
	//	}
	//}

	r := 1
	for _, d := range data {
		r++
		values := d.GetValues()
		if err := xl.SetSheetRow(SALMOBO_DATA_SHEET, xlutil.Cell(r, 1).Address(), &values); err != nil {
			return err
		}
	}

	if err := xl.DeleteTable(tbl.Name); err != nil {
		return err
	}

	tbl.Range = xlutil.Cell(1, 1).Address() + ":" + xlutil.Cell(r, 14).Address()

	if err := xl.AddTable(SALMOBO_DATA_SHEET, &tbl); err != nil {
		return err
	}

	return nil
}
