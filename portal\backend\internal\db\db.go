package db

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/csee-pm/etl/shared/utils"
	"github.com/jmoiron/sqlx"
)

type DBModel interface {
	Columns() []string
	TableName() string
	SchemaName() string
	FullTableName() string
}

type genericModel struct {
	columns    []string
	tableName  string
	schemaName string
}

func (gm *genericModel) Columns() []string {
	return gm.columns
}

func (gm *genericModel) TableName() string {
	return gm.tableName
}

func (gm *genericModel) SchemaName() string {
	return gm.schemaName
}

func (gm *genericModel) FullTableName() string {
	fullName := gm.schemaName + "." + gm.tableName
	if gm.schemaName == "" {
		fullName = gm.tableName
	}
	return fullName
}

func CreateGenericModel[T any](tableName string, schemaName string) *genericModel {
	var model = new(T)

	// T should be a struct, and try to read the tag `db` from it and collect them into column names
	t := reflect.TypeOf(model)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	if t.Kind() != reflect.Struct {
		return nil
	}

	var columns []string
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		tag := field.Tag.Get("db")
		if tag == "" {
			tag = utils.ToSnakeCase(field.Name)
		}

		columns = append(columns, tag)
	}

	return &genericModel{
		columns:    columns,
		tableName:  tableName,
		schemaName: schemaName,
	}
}

func GenerateInsertQuery[T any](model DBModel, data T) (qry string, args []any, err error) {
	dval := reflect.ValueOf(data)
	if dval.Kind() == reflect.Ptr {
		dval = dval.Elem()
	}

	if dval.Kind() != reflect.Struct {
		err = fmt.Errorf("data is not a struct")
		return
	}

	columns := model.Columns()
	colMap := make(map[string]any)
	var values []any
	for i := 0; i < dval.NumField(); i++ {
		field := dval.Field(i)
		tfield := dval.Type().Field(i)
		tag := tfield.Tag.Get("db")
		if tag == "" {
			tag = utils.ToSnakeCase(tfield.Name)
		}

		colMap[tag] = field.Interface()
	}

	for _, col := range columns {
		value, ok := colMap[col]
		if !ok {
			err = fmt.Errorf("column %s not found in data", col)
			return
		}
		values = append(values, value)
	}

	qry = fmt.Sprintf("INSERT INTO %s (%s) VALUES (?)", model.FullTableName(), strings.Join(model.Columns(), ","))
	qry, args, err = sqlx.In(qry, values)

	return
}
