package utils

import (
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"reflect"
	"strconv"
	"strings"
)

type CSVRow interface {
	GetColumns() []string
	GetRowValues() []string
}

type CSVReader interface {
	ReadRow(row []string) error
}

func WriteToCsv[T CSVRow](fpath string, rows []T) error {
	if len(rows) == 0 {
		return nil
	}

	f, err := os.Create(fpath)
	if err != nil {
		return err
	}
	defer f.Close()

	cw := csv.NewWriter(f)
	defer cw.Flush()

	cw.Write(rows[0].GetColumns())
	for _, row := range rows {
		cw.Write(row.GetRowValues())
	}

	return nil
}

type csvReaderOption struct {
	skipline   int
	headerLine int
	headers    []string
}

type CSVReaderOption func(o *csvReaderOption)

func WithSkipLine(skipline int) CSVReaderOption {
	return func(o *csvReaderOption) {
		o.skipline = skipline
	}
}

func WithHeaderLine(headerLine int) CSVReaderOption {
	return func(o *csvReaderOption) {
		o.headerLine = headerLine
	}
}

func WithHeaders(headers []string) CSVReaderOption {
	return func(o *csvReaderOption) {
		o.headers = headers
	}
}

// ReadCSVToStruct reads a CSV file and parses it into a slice of the given struct type
func ReadFromCsv[T any](f io.Reader, dest *[]T, options ...CSVReaderOption) error {

	opts := csvReaderOption{}
	for _, op := range options {
		op(&opts)
	}

	if len(opts.headers) == 0 && opts.headerLine == 0 {
		// if no headers are provided, assume the header line is the first line after the skip line
		opts.headerLine = opts.skipline
	}

	// Read CSV
	reader := csv.NewReader(f)
	rows, err := reader.ReadAll()
	if err != nil {
		return err
	}

	if len(rows) < 1 {
		return fmt.Errorf("empty CSV file")
	}

	if opts.skipline > 0 && opts.skipline < len(rows) {
		rows = rows[opts.skipline:]
		opts.headerLine -= opts.skipline
	}

	headers := opts.headers
	if len(headers) == 0 && opts.headerLine > 0 && opts.headerLine < len(rows) {
		headers = rows[opts.headerLine]
		rows = rows[opts.headerLine+1:]
	}

	return parseRows(rows, dest, headers)

}

func parseRows[T any](rows [][]string, dest *[]T, headers []string) error {
	fieldMap := map[string]int{}
	for i, h := range headers {
		fieldMap[h] = i
	}

	// Get struct type information
	var model T
	modelType := reflect.TypeOf(model)
	if modelType.Kind() != reflect.Struct {
		return fmt.Errorf("dest must be a struct")
	}

	// Map struct fields to CSV columns using `csv` tags
	fieldIndexes := map[int]int{}
	for i := 0; i < modelType.NumField(); i++ {
		field := modelType.Field(i)
		tag := field.Tag.Get("csv")
		if tag == "" {
			tag = strings.ToLower(field.Name)
		}

		if idx, ok := fieldMap[tag]; ok {
			fieldIndexes[i] = idx
		}
	}

	// Parse rows
	for _, row := range rows {
		var entry T
		entryValue := reflect.ValueOf(&entry).Elem()

		for structIdx, csvIdx := range fieldIndexes {
			field := entryValue.Field(structIdx)
			if !field.CanSet() {
				continue
			}

			// Convert CSV string to the appropriate type
			value := row[csvIdx]
			switch field.Kind() {
			case reflect.String:
				field.SetString(value)
			case reflect.Int, reflect.Int64:
				intVal, _ := strconv.ParseInt(value, 10, 64)
				field.SetInt(intVal)
			case reflect.Float64:
				floatVal, _ := strconv.ParseFloat(value, 64)
				field.SetFloat(floatVal)
			case reflect.Bool:
				boolVal, _ := strconv.ParseBool(value)
				field.SetBool(boolVal)
			}
		}
		*dest = append(*dest, entry)
	}

	return nil

}
