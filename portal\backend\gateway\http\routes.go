package http

import (
	"encoding/json"
	"net/http"
	"os"
	"path/filepath"

	"github.com/csee-pm/etl/shared/utils/apiutil"

	"github.com/csee-pm/etl/portal/backend/api"
	"github.com/csee-pm/etl/portal/backend/config"
	"github.com/go-chi/chi/v5"
	"github.com/likearthian/apikit"
	htx "github.com/likearthian/apikit/transport/http"
)

func createApiRoutes(appapi api.APIEndpoints, options ...htx.ServerOption) http.Handler {
	authHandler := createAuthHandlers(appapi.Auth, options...)
	idmHandler := createIdmHandlers(appapi.Idm, options...)
	jobHandler := createJobServiceHandlers(appapi.Register, options...)

	apikeyMap := getApikeys()

	r := chi.NewRouter()

	r.Route("/auth", func(sr chi.Router) {
		sr.Method(http.MethodPost, "/authenticate", authHandler.AuthenticateHandler)
	})

	r.Group(func(gr chi.Router) {
		authmd := apikit.MakeHttpJwtAndApikeyMiddleware(apikit.CreateJwtKeyGetterFunc(config.EncKeys), apiutil.MakeApikeyValidationFn(apikeyMap))
		gr.Use(authmd)

		gr.Route("/auth", func(sr chi.Router) {
			sr.Method(http.MethodGet, "/validate", authHandler.ValidateTokenHandler)
			sr.Method(http.MethodGet, "/refresh", authHandler.RefreshTokenHandler)
		})

		gr.Route("/idm", func(sr chi.Router) {
			sr.Method(http.MethodGet, "/user", idmHandler.GetUserHandler)
			sr.Method(http.MethodPost, "/user", idmHandler.CreateUserHandler)
		})

		gr.Route("/register/job", func(sr chi.Router) {
			sr.Method(http.MethodGet, "/{id}", jobHandler.GetJobByIDHandler)
			sr.Method(http.MethodGet, "/", jobHandler.GetJobHandler)
			sr.Method(http.MethodPost, "/", jobHandler.RegisterJobStartHandler)
			sr.Method(http.MethodDelete, "/{id}", jobHandler.UpdateJobStatusHandler)
		})

		gr.Route("/register/etl", func(sr chi.Router) {
			sr.Method(http.MethodGet, "/{id}", jobHandler.GetEtlByIDHandler)
			sr.Method(http.MethodGet, "/", jobHandler.GetEtlHandler)
		})
	})

	return r
}

func getApikeys() map[string]*apikit.AuthClaims {
	exePath, err := os.Executable()
	if err != nil {
		return nil
	}

	exeDir := filepath.Dir(exePath)
	apikeyFile := filepath.Join(exeDir, "apikey.json")

	if _, err := os.Stat(apikeyFile); os.IsNotExist(err) {
		return nil
	}

	f, err := os.Open(apikeyFile)
	if err != nil {
		return nil
	}

	defer f.Close()

	var apikeys map[string]*apikit.AuthClaims
	if err := json.NewDecoder(f).Decode(&apikeys); err != nil {
		return nil
	}

	return apikeys
}
