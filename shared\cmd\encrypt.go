package cmd

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"

	"github.com/csee-pm/etl/shared/enc"
)

var keyString string

var encryptCmd = &cobra.Command{
	Use:     "encrypt",
	Aliases: []string{"e"},
	Short:   "encrypt supplied string",
	Long:    "encrypt supplied string",
	Run:     ecryptAction,
}

func NewEncryptCmd(v *viper.Viper) *cobra.Command {

	return encryptCmd
}

func ecryptAction(cmd *cobra.Command, args []string) {
	str := strings.Join(args, " ")

	en, err := enc.Encrypt(str)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(en)
}

// func Encrypt(str string) (string, error) {
// 	rand.Seed(time.Now().UnixNano())
// 	kid := rand.Intn(len(encKeys) - 1)

// 	buf, err := crypto.EncryptWithGCM([]byte(str), []byte(encKeys[kid]))
// 	if err != nil {
// 		return "", err
// 	}

// 	payload := fmt.Sprintf("enc/%d:%s", kid, crypto.EncodeBASE64(buf))
// 	return crypto.EncodeBASE64URL([]byte(payload)), nil
// }

// func Decrypt(str string) (string, error) {
// 	buf, err := crypto.DecodeBASE64URL(str)
// 	if err != nil {
// 		return "", err
// 	}

// 	arr := strings.Split(string(buf), ":")
// 	if len(arr) < 2 {
// 		return "", fmt.Errorf("invalid encrypted payload")
// 	}

// 	if !strings.HasPrefix(arr[0], "enc/") {
// 		return "", fmt.Errorf("invalid encrypted payload")
// 	}

// 	kid, err := strconv.ParseInt(arr[0][4:], 10, 64)
// 	if err != nil {
// 		return "", fmt.Errorf("invalid encrypted payload")
// 	}

// 	if int(kid) > len(encKeys)-1 {
// 		return "", fmt.Errorf("invalid encrypted payload")
// 	}

// 	key := encKeys[int(kid)]

// 	buf, err = crypto.DecodeBASE64(arr[1])
// 	if err != nil {
// 		return "", fmt.Errorf("invalid encrypted payload")
// 	}

// 	res, err := crypto.DecryptWithGCM(buf, []byte(key))
// 	if err != nil {
// 		return "", err
// 	}

// 	return string(res), nil
// }

// var encKeys = []string{
// 	"g9l1CXD7re35OPBFC9bVLMtO2SguEs9U",
// 	"VgGDUUMKKCjJ99mFqi9A8UV5Czi85PZG",
// 	"bSnKU1xyw7O0IeAnunpM9NRWghhruy56",
// 	"meuQx7x1UwlDbhU0cjAh2Wy5I3wWZGur",
// 	"pdrywyC8CAuZAdbcjNwSXlQIMB0rkf6x",
// 	"7TiWbWQjmlsa6NSXiFcze8wrVoddpBfr",
// 	"uQLFs0nuvjpxQoZOgJIukBWZelgl9hPb",
// 	"WVbdWm0TKB4B9N79z6JS64HtVE6MKF7Q",
// }
