package main

import (
	"bytes"
	"context"
	"fmt"
	"os"

	"github.com/csee-pm/etl/bpcwn/cmd"
	cfg "github.com/csee-pm/etl/bpcwn/config"
	"github.com/csee-pm/etl/bpcwn/process"
	etlcmd "github.com/csee-pm/etl/shared/cmd"
	etlcfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/spf13/cobra"
)

var logBuffer *bytes.Buffer
var executedCmd *cobra.Command

func main() {
	logBuffer = new(bytes.Buffer)

	root := CreateRootCmd()

	err := root.Execute()
	process := ""
	if executedCmd != nil {
		process = executedCmd.Name()
	}

	err = etlProc.NotifyProcessFinished(root.Context(), process, logBuffer.String(), err)
	if err != nil {
		fmt.Println(err.Error())
		os.Exit(1)
	}
}

func CreateRootCmd() *cobra.Command {
	root := etlcmd.InitCommonRootCommand(
		"bpcwn",
		cfg.SetNewConfig,
		etlcmd.WithRootProcess("bpcwn", process.RunAll),
		etlcmd.WithDescription("ETL job for getting bpcwn report"),
		etlcmd.WithLogBuffer(logBuffer),
	)

	root.Flags().StringVar(&cfg.UsePstMtdFromFile, "pst-mtd-from-file", "", "Use PST MTD data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UsePstFmFromFile, "pst-fm-from-file", "", "Use PST FM data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UsePstKabuFromFile, "pst-kabu-from-file", "", "Use PST Kabu data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseSalmoboFromFile, "salmobo-from-file", "", "Use Salmobo data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseMtdDistribFromFile, "distrib-mtd-from-file", "", "Use MTD Distrib data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseFmDistribFromFile, "distrib-fm-from-file", "", "Use FM Distrib data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseSDPFromFile, "sdp-from-file", "", "Use SDP data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseDSEFromFile, "dse-from-file", "", "Use DSE data from file instead of querying from database")
	root.Flags().StringVar(&cfg.UseGaM2sFromFile, "ga-m2s-from-file", "", "Use GA M2S data from file instead of querying from database")

	root.PersistentFlags().BoolVar(&cfg.UseNoMailer, "no-mailer", false, "Do not send email")
	root.PersistentFlags().BoolVar(&cfg.NotInteractive, "not-interactive", false, "Do not prompt for configuration")
	//root.Flags().BoolVar(&cfg.SkipPST, "skip-pst", false, "Skip PST ETL")
	//root.Flags().BoolVar(&cfg.SkipDistrib, "skip-distrib", false, "Skip Distrib ETL")

	root.PreRunE = createPrerun(func() bool {
		return true
	})

	pstCmd := CreatePstCmd()
	distribCmd := CreateDistribCmd()
	topKpiCmd := CreateTopKpiCmd()
	sdpCmd := CreateSdpCmd()
	salmoboCmd := CreateSalmoboCmd()
	gaM2sCmd := CreateGaM2sCmd()

	root.AddCommand(pstCmd, distribCmd, topKpiCmd, sdpCmd, salmoboCmd, gaM2sCmd)
	return root
}

func CreatePstCmd() *cobra.Command {
	pstCmd := cmd.InitPstCmd()
	pstCmd.Flags().StringVar(&cfg.UsePstMtdFromFile, "pst-mtd-from-file", "", "Use PST MTD data from file instead of querying from database")
	pstCmd.Flags().StringVar(&cfg.UsePstFmFromFile, "pst-fm-from-file", "", "Use PST FM data from file instead of querying from database")
	pstCmd.PreRunE = createPrerun(func() bool {
		return false
	})
	return pstCmd
}

func CreateDistribCmd() *cobra.Command {
	distribCmd := cmd.InitDistribCmd()
	distribCmd.Flags().StringVar(&cfg.UseMtdDistribFromFile, "distrib-from-file", "", "Use Distrib data from file instead of querying from database")
	distribCmd.PreRunE = createPrerun(func() bool {
		return cfg.UseMtdDistribFromFile == ""
	})
	return distribCmd
}

func CreateTopKpiCmd() *cobra.Command {
	topKpiCmd := cmd.InitTopKpiCmd()
	topKpiCmd.PreRunE = createPrerun(func() bool {
		return false
	})
	return topKpiCmd
}

func CreateSdpCmd() *cobra.Command {
	sdpCmd := cmd.InitSdpCmd()
	sdpCmd.Flags().StringVar(&cfg.UseSDPFromFile, "sdp-from-file", "", "Use SDP data from file instead of querying from database")
	sdpCmd.Flags().StringVar(&cfg.UseDSEFromFile, "dse-from-file", "", "Use DSE data from file instead of querying from database")
	sdpCmd.PreRunE = createPrerun(func() bool {
		return cfg.UseSDPFromFile == "" || cfg.UseDSEFromFile == ""
	})

	return sdpCmd
}

func CreateSalmoboCmd() *cobra.Command {
	salmoboCmd := cmd.InitSalmoboCmd()
	salmoboCmd.Flags().StringVar(&cfg.UseSalmoboFromFile, "salmobo-from-file", "", "Use Salmobo data from file instead of querying from database")
	salmoboCmd.PreRunE = createPrerun(func() bool {
		return cfg.UseSalmoboFromFile == ""
	})
	return salmoboCmd
}

func CreateGaM2sCmd() *cobra.Command {
	gaM2sCmd := cmd.InitGaM2sCmd()
	gaM2sCmd.Flags().StringVar(&cfg.UseGaM2sFromFile, "ga-m2s-from-file", "", "Use GA M2S data from file instead of querying from database")
	gaM2sCmd.PreRunE = createPrerun(func() bool {
		return false
	})
	return gaM2sCmd
}

func createPrerun(useTunnelCheckFn func() bool) func(*cobra.Command, []string) error {
	return func(cmd *cobra.Command, args []string) error {
		executedCmd = cmd
		conf := ctx.ExtractConfig(cmd.Context())
		logger := ctx.ExtractLogger(cmd.Context())
		tunnel := conf.Get("tunnel")
		var useTunnel = false
		if tunnel != nil {
			useTunnel = true
			if useTunnelCheckFn != nil {
				useTunnel = useTunnelCheckFn()
			}
		}

		if useTunnel {
			tunConfig, err := etlcfg.GetTunnelConfig(conf, "tunnel")
			if err != nil {
				return fmt.Errorf("failed to get tunnel config. %s", err)
			}

			if err := etlProc.StartTunnel(tunConfig, logger); err != nil {
				return fmt.Errorf("failed to start tunnel. %s", err)
			}
		}

		workDir := conf.GetString("work_dir")
		if workDir == "" {
			workDir = "workdir"
		}

		exepath := ctx.ExtractRootDir(cmd.Context())
		workDir = exepath + "/" + workDir
		conf.Set("work_dir", workDir)
		c := context.WithValue(cmd.Context(), ctx.ContextKeyWorkDir, workDir)
		cmd.SetContext(c)

		if err := os.MkdirAll(workDir, os.ModePerm); err != nil {
			return fmt.Errorf("failed to create work dir. %s", err)
		}

		return nil
	}
}
