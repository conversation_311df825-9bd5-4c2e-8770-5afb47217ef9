package config

import (
	cfg "github.com/csee-pm/etl/shared/config"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/spf13/viper"
)

type Config struct {
	Tunnel *cfg.TunnelConfig `yaml:"tunnel"`
	GP     cfg.GPConfig      `yaml:"gp"`
}

func (c Config) ToMap() map[string]any {
	var cfMap = make(map[string]any)
	if c.Tunnel != nil {
		cfMap["tunnel"] = c.Tunnel.ToMap()
	}

	cfMap["gp"] = c.GP.ToMap()

	return cfMap
}

type QueryConfig struct {
	SQL    string         `yaml:"sql"`
	Params map[string]any `yaml:"params"`
}

func PromptNewConfig(v *viper.Viper) (Config, error) {
	cf := Config{}

	var tunCfg *cfg.TunnelConfig
	var err error

	useTun := utils.StringToBool(cfg.GetPromptValue("Use SSH Tunnel To connect to GP[y/N]: ", "N", cfg.ShouldBeYesOrNo))
	if useTun {
		tunCfg, err = cfg.PromptNewTunnelConfig(v, "tunnel")
		if err != nil {
			return cf, err
		}
	}

	gpCfg, err := cfg.PromptNewGPConfig(v, "gpfat")
	if err != nil {
		return cf, err
	}

	return Config{
		Tunnel: tunCfg,
		GP:     gpCfg,
	}, nil
}
