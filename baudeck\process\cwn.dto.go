package process

import (
	"strconv"
	"time"

	"gopkg.in/guregu/null.v4"
)

type CwnKpi struct {
	Primary   null.Float `db:"primary"`
	Secondary null.Float `db:"secondary"`
	Tertiary  null.Float `db:"tertiary"`
	Site3QSSO null.Float `db:"site_3qsso"`
}

func NewCwnKpi() *CwnKpi {
	return &CwnKpi{
		Primary:   null.FloatFrom(0),
		Secondary: null.FloatFrom(0),
		Tertiary:  null.FloatFrom(0),
		Site3QSSO: null.FloatFrom(0),
	}
}

type CwnData struct {
	MonthID  string      `db:"month_id"`
	Period   string      `db:"period"`
	AsofDate string      `db:"asof_date"`
	Circle   null.String `db:"circle"`
	Region   null.String `db:"region"`
	Brand    string      `db:"brand"`
	CwnKpi
}

func (cw CwnData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "brand", "primary", "secondary", "tertiary", "site_3qsso"}
}

func (cw CwnData) GetRowValues() []string {
	primary := ""
	if cw.Primary.Valid {
		primary = strconv.FormatFloat(cw.Primary.ValueOrZero(), 'f', -1, 64)
	}

	secondary := ""
	if cw.Secondary.Valid {
		secondary = strconv.FormatFloat(cw.Secondary.ValueOrZero(), 'f', -1, 64)
	}

	tertiary := ""
	if cw.Tertiary.Valid {
		tertiary = strconv.FormatFloat(cw.Tertiary.ValueOrZero(), 'f', -1, 64)
	}

	site3qss := ""
	if cw.Site3QSSO.Valid {
		site3qss = strconv.FormatFloat(cw.Site3QSSO.ValueOrZero(), 'f', -1, 64)
	}

	return []string{cw.MonthID, cw.Period, cw.AsofDate, cw.Circle.String, cw.Region.String, cw.Brand, primary, secondary, tertiary, site3qss}
}

type RegionalCwnData struct {
	EntityType string
	EntityName string
	MTD        *CwnKpi
	LMTD       *CwnKpi
	FM         map[string]*CwnKpi
}

func NewRegionalCwnData(entityType, entityName string) *RegionalCwnData {
	return &RegionalCwnData{
		EntityType: entityType,
		EntityName: entityName,
		MTD:        NewCwnKpi(),
		LMTD:       NewCwnKpi(),
		FM:         make(map[string]*CwnKpi),
	}
}

type CwnReportData struct {
	CircleData   map[string]*RegionalCwnData
	RegionalData map[string]*RegionalCwnData
	NationalData *RegionalCwnData
}

func NewCwnReportData() *CwnReportData {
	return &CwnReportData{
		CircleData:   make(map[string]*RegionalCwnData),
		RegionalData: make(map[string]*RegionalCwnData),
		NationalData: NewRegionalCwnData("NATIONAL", "INDONESIA"),
	}
}

type CwnReport struct {
	MtdDate time.Time
	IM3     *CwnReportData
	Three   *CwnReportData
	IOH     *CwnReportData
}
