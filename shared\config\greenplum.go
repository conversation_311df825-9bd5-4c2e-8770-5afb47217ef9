package config

type GPConfig struct {
	Host       string `yaml:"host"`
	Port       int    `yaml:"port"`
	Credential string `yaml:"credential"`
	Database   string `yaml:"database"`
	User       string `yaml:"-" config:"user"`
	Password   string `yaml:"-" config:"password"`
}

func (gc GPConfig) ToMap() map[string]any {
	var cfMap = make(map[string]any)
	cfMap["host"] = gc.Host
	cfMap["port"] = gc.Port
	cfMap["credential"] = gc.Credential
	cfMap["database"] = gc.Database
	return cfMap
}
