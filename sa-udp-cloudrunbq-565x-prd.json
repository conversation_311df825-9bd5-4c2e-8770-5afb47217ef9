{
  "type": "service_account",
  "project_id": "data-commstrexe-prd-565x",
  "private_key_id": "598d74e2856d47c98027a1837ba8331c0b18f304",
  "private_key": "-----B<PERSON><PERSON> PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3+DtqE2gbRNr8
gh8KuQFeoauxu/Opvwpw70Nzntpjdyx3/Ua3xLW2bPFmQISe/MVsHfzOZ99v4801
lOXoUKX2orT8QDdySKHSAdyYry2qNrSMro3ubkulTf56x/8uOd5J5OHbchb862tE
dzjHURs784HPIZp/W/5yx6SqylzJiaDDuVV2gGxiNEa07GpGdTfjPwYgWc/Em4pj
QCnO9oc+/Q4xKWs3r1P9TtgF1ZDnv2oRvqIv/6eyIbShCX7qF9pjUuk/R0mTfzm/
oQgq92kD4OhQVvooT3gBlSOAh5Bba4taZ6ybwrlEHnyspKlPshWcaxqwbHC/beC6
7ueXTcp9AgMBAAECggEAEKhLSOmE7aMoaOCnSuNBen4M+8TW5THFEkC3zMo/X+40
epDWTGFlSqVGnkmNOJAOmCGG+HFzS8BGnmli/8v9mv1UgdqWqqRk8oz0mmaSLe1S
H8LWWxZlhwwu1JMzSc43kF1HDgWgizoNtl9NJs7zNlRwFn8mbA26Vk45XLWB/UVv
jy43NMwPmpR6bQtGdnIG6HfpPweYWn57dgB7qolgAe6EkVHiLKeJSzFs11ZTrMpp
MBi54sRA8suam8sRds2N965WV78GQ/eXuWJB2EXkShqYXwqEHgZAvcc1jN/L3UMF
GrSlynLpaXw1QQcoiKoKs1/dgsWG2KeUWD8sUBw/gQKBgQDjc9oHVVthVGckVDw3
yvLM+IepffmHfSYGHYmPQ2neD41qDmcVy7AdAuO1pPwLs0VB1C7yzq0/1WjQY6As
60W8qryO9LPWzqFuJMbRr81db4w7oMiSAHCA0NJuriAo3PQYhV7V7fKAS2nLcjCk
6H2vX37R0x0eMI9VUPm1DlyxDQKBgQDPD0GmhnjOXqT4NMiM1QyrhWKlEilu7gjN
gVY9ky8FXWbns7Gz9tZ93vMdKyGx7/cYV7VkKewsXktsw8RhW/Y8NjOO64Uf+zr+
YhRQbeNbC7UeKbGleq+mV60UEvisCvuH+7MjgmILvgzXT9Vc2a044Ovmu9+wgwe+
lj3StL/DMQKBgQClaJ7qLlziDyti9A9G08R241ZlIEPTEGQSlnBvSys2XkENG6RD
6XkN4AgRFQZmy9xV3yS1VzAO9/fddFYL1J1GczPlEnemg2ZKX1xnKV5D0Nc+ldLG
uqMWSsZ9zwt8LBMnsfYhxYVRX4Z0eXp+ssSUQPJNTSYjylSySs0NqoAmEQKBgCg9
DsUN3Ow9+FFD8LVtscijY0gw/1xPZeaJ+/h7Yefo/taIVUBj98r+SMCiYSnzOFHC
ub19aCg3KvE4GjHRbUjGP77GhlhwukY1ST1YHZbT756s+5js/rWU1E3SYgENIVSL
1YRREjy18kjletFQfBlRnIZ6fD4ROLARAnKwLZQBAoGAGXK8bylmDjAtYSdmQSu0
mvCgYJ02UGNFz8yLO5LbVf5//eThDuwTOn8PD9lm4G9zd7EUa7xfAKsd2UgfqCE0
VVPtVrwPFFAJLnJ2baRghzjvX0WWavwzWZ4pyngqktNr//O4/VoVUm0/qJeTXXxo
osDbb/KGv0QDjNkuumPZT+s=
-----END PRIVATE KEY-----",
  "client_email": "<EMAIL>",
  "client_id": "111912042710070242696",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>",
  "universe_domain": "googleapis.com"
}