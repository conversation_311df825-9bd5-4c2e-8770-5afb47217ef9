package process

import (
	"context"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	"github.com/jmoiron/sqlx"
)

func QueryGreenplumData[T any](c context.Context, query string, params map[string]*etlDb.ParamValue) ([]T, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	// cf, err := cfg.GetGPConfig(conf, "gpfat")
	cf, err := cfg.GetConfig[cfg.GPConfig](conf, "gpfat")
	if err != nil {
		return nil, err
	}

	db, err := etlDb.ConnectToGP(cf.Host, cf.Port, cf.User, cf.Password, cf.Database)
	if err != nil {
		return nil, err
	}

	err = db.<PERSON>()
	if err != nil {
		return nil, err
	}

	query, args, err := etlDb.ParseSqlWithParams(query, params)
	if err != nil {
		return nil, err
	}

	logger.Debug("successfully connected to greenplum")

	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}

	query = db.Rebind(query)

	// logger.Debug("executing greenplum query", "qry", query, "args", args)

	var data []T
	if err := db.Select(&data, query, args...); err != nil {
		return nil, err
	}

	return data, nil
}
