with base as (
    SELECT
        site_id
    FROM
        biadm.ds_lrs_ioh_2025
    WHERE
        as_of_date = '20241231'
    and lrs_flag_ioh = 'LRS'
)
SELECT
    substr(a.as_of_date, 1, 6) month_id,
    a.circle,
    a.region_circle region,
    count(*) - sum(case when a.lrs_flag_ioh = 'LRS' then 1 else 0 end) graduate_cnt
FROM
    biadm.ds_lrs_ioh_2025 a
    inner join
    base b
    on
        a.site_id = b.site_id
WHERE
    a.as_of_date >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
and a.as_of_date < from_timestamp(trunc(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), 'month'), 'yyyyMMdd')
and a.as_of_date = from_timestamp(date_add(trunc(date_add(to_timestamp(a.as_of_date, 'yyyyMMdd'), interval 1 month), 'month'), interval -1 day), 'yyyyMMdd')
group by 1,2,3
