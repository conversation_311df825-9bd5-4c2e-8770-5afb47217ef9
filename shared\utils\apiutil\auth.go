package apiutil

import (
	"context"
	"time"

	"github.com/dgrijalva/jwt-go/v4"
	"github.com/likearthian/apikit"
)

const (
	bearer       string = "bearer"
	bearerFormat string = "Bearer %s"
)

func IsAdmin(ctx context.Context) bool {
	claims, ok := ctx.Value(apikit.ContextKeyAuthClaims).(*JwtClaims)
	if !ok {
		return false
	}

	return claims.IsAdmin
}

func MakeClaimFactory(issuer string, username, fullname, email string, isAdmin bool, aud jwt.ClaimStrings, expiry time.Duration) apikit.ClaimsFactory {
	return func() jwt.Claims {
		return &apikit.AuthClaims{
			StandardClaims: jwt.StandardClaims{
				ExpiresAt: jwt.NewTime(float64(time.Now().Add(expiry).Unix())),
				Issuer:    issuer,
				Audience:  aud,
			},
			Username: username,
			IsAdmin:  isAdmin,
		}
	}
}

func MakeApikeyValidationFn(apikeyMap map[string]*apikit.AuthClaims) func(string) any {
	return func(apikey string) any {
		if claims, ok := apikeyMap[apikey]; ok {
			return claims
		}

		return nil
	}
}
