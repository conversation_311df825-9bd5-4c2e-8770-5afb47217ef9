package register

import (
	"context"
	"errors"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/likearthian/store"
	"gopkg.in/guregu/null.v4"
)

type JobRegister struct {
	store.DBTable `name:"job_register"`
	ID            int64       `db:"id" json:"id"`
	IsRunning     int         `db:"is_running" json:"is_running"`
	EtlID         string      `db:"etl_id" json:"etl_id"`
	Status        string      `db:"status" json:"status"`
	StartTime     int64       `db:"start_time" json:"start_time"`
	EndTime       null.Int    `db:"end_time,allownull" json:"end_time"`
	Options       null.String `db:"options,allownull" json:"options"`
	Output        null.String `db:"output,allownull" json:"output"`
	StartedBy     string      `db:"started_by" json:"started_by"`
}

type JobDAO struct {
	store.Repository[int64, JobRegister]
}

func NewJobDAO(client *sqlx.DB) (*JobDAO, error) {
	repo, err := store.CreateSqliteRepository[int64, JobRegister](client)
	if err != nil {
		return nil, err
	}

	jobDAO := &JobDAO{Repository: repo}
	return jobDAO, nil
}

func (dao *JobDAO) RegisterJobStart(c context.Context, etlID string, optionsJson null.String, outputID null.String, startedBy string) (JobRegister, error) {
	process := JobRegister{
		ID:        time.Now().UnixNano(),
		EtlID:     etlID,
		IsRunning: 1,
		Status:    "started",
		StartTime: time.Now().UnixMilli(),
		Options:   optionsJson,
		Output:    outputID,
		StartedBy: startedBy,
	}

	_, err := dao.Insert(c, process)
	return process, err
}

func (dao *JobDAO) GetJob(c context.Context, req GetJobRequestDTO) ([]JobRegister, error) {
	filter := make(map[string]any)
	if len(req.ID) > 0 {
		filter["id"] = req.ID
	}

	if req.IsRunning.Valid {
		isRunning := 0
		if req.IsRunning.ValueOrZero() {
			isRunning = 1
		}
		filter["is_running"] = isRunning
	}

	if len(req.Status) > 0 {
		filter["status"] = req.Status
	}

	if len(req.EtlID) > 0 {
		filter["etl_id"] = req.EtlID
	}

	var jobs []JobRegister
	err := dao.Select(c, filter, &jobs)
	if err != nil && !errors.Is(err, store.ErrNoRow) {
		return nil, err
	}
	return jobs, nil
}

func (dao *JobDAO) GetActiveJobs(c context.Context) ([]JobRegister, error) {
	filter := map[string]any{
		"is_running": 1,
	}
	var jobs []JobRegister
	err := dao.Select(c, filter, &jobs)
	if err != nil {
		return nil, err
	}
	return jobs, nil
}

func (dao *JobDAO) GetEtlRunningStatus(c context.Context, etlID string) (isRunning bool, id int64, err error) {
	filter := map[string]any{
		"is_running": 1,
		"etl_id":     etlID,
	}
	var processes []JobRegister
	err = dao.Select(c, filter, &processes, store.WithSorter([]string{"-start_time"}), store.WithLimit(1))
	if err != nil && !errors.Is(err, store.ErrNoRow) {
		return false, 0, err
	}

	if len(processes) == 0 {
		return false, 0, nil
	}

	return true, processes[0].ID, nil
}

func (dao *JobDAO) GetJobStatus(c context.Context, id int64) (string, error) {
	var process JobRegister
	err := dao.Get(c, id, &process)
	if err != nil {
		return "", err
	}
	return process.Status, nil
}

func (dao *JobDAO) UpdateJobStatus(c context.Context, req UpdateJobStatusRequestDTO, options ...store.QueryOption) error {
	keyvals := make(map[string]any)
	if req.Status.Valid {
		keyvals["status"] = req.Status.ValueOrZero()
	}

	if req.EndTime.Valid {
		keyvals["end_time"] = req.EndTime.ValueOrZero()
	}

	if req.Output.Valid {
		keyvals["output"] = req.Output.ValueOrZero()
	}

	return dao.Update(c, req.JobID, keyvals, options...)
}
