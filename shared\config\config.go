package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/csee-pm/etl/shared/enc"
	"github.com/spf13/viper"
	"gopkg.in/yaml.v2"
)

type ConfigOption func(*configOption)

type configOption struct {
	tag string
}

func WithKeyName(tag string) ConfigOption {
	return func(o *configOption) {
		o.tag = tag
	}
}

func GetConfigFilePath(cfgFileName string) string {
	home, err := os.UserHomeDir()
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	return filepath.Join(home, cfgFileName)
}

func WriteConfig(cf any, path string) error {
	buf, err := yaml.Marshal(cf)
	if err != nil {
		return err
	}

	return os.WriteFile(path, buf, 0644)
}

func ParseTunnelConfig(v *viper.Viper, cf *TunnelConfig, key string) error {
	return v.UnmarshalKey(key, cf)
}

func GetTunnelConfig(v *viper.Viper, key string) (TunnelConfig, error) {
	// var cf TunnelConfig
	return GetConfig[TunnelConfig](v, key)
	// err := ParseTunnelConfig(v, &cf, key)
	// return cf, err
}

func ParseGPConfig(v *viper.Viper, cf *GPConfig, key string) error {
	if err := v.UnmarshalKey(key, cf); err != nil {
		return err
	}

	if cf.Credential == "" {
		return nil
	}

	creds, err := enc.Decrypt(cf.Credential)
	if err != nil {
		return err
	}

	credsArr := strings.Split(creds, ":")
	if len(credsArr) != 2 {
		return fmt.Errorf("invalid credential format")
	}

	cf.User = credsArr[0]
	cf.Password = credsArr[1]

	return nil
}

func GetGPConfig(v *viper.Viper, key string) (GPConfig, error) {
	return GetConfig[GPConfig](v, key)
	// var cf GPConfig
	// err := ParseGPConfig(v, &cf, key)
	// return cf, err
}

func ParseSSHConfig(v *viper.Viper, cf *SSHConfig, key string) error {
	return v.UnmarshalKey(key, cf)
}

func GetSSHConfig(v *viper.Viper, key string) (SSHConfig, error) {
	return GetConfig[SSHConfig](v, key)
	// var cf SSHConfig
	// err := ParseSSHConfig(v, &cf, key)
	// return cf, err
}

func ParseImpalaConfig(v *viper.Viper, cf *ImpalaConfig, key string) error {
	if err := v.UnmarshalKey(key, cf); err != nil {
		return err
	}

	if cf.Credential == "" {
		return nil
	}

	creds, err := enc.Decrypt(cf.Credential)
	if err != nil {
		return err
	}

	credsArr := strings.Split(creds, ":")
	if len(credsArr) != 2 {
		return fmt.Errorf("invalid credential format")
	}

	cf.User = credsArr[0]
	cf.Password = credsArr[1]

	return nil
}

func GetImpalaConfig(v *viper.Viper, key string) (ImpalaConfig, error) {
	return GetConfig[ImpalaConfig](v, key)
	// var cf ImpalaConfig
	// err := ParseImpalaConfig(v, &cf, key)
	// return cf, err
}

func ParseEmailConfig(v *viper.Viper, cf *EmailConfig, key string) error {
	if err := v.UnmarshalKey(key, cf); err != nil {
		return err
	}

	if cf.Credential == "" {
		return nil
	}

	creds, err := enc.Decrypt(cf.Credential)
	if err != nil {
		return err
	}

	credsArr := strings.Split(creds, ":")
	if len(credsArr) != 2 {
		return fmt.Errorf("invalid credential format")
	}

	cf.User = credsArr[0]
	cf.Pass = credsArr[1]

	return nil
}

func GetEmailConfig(v *viper.Viper, key string) (EmailConfig, error) {
	return GetConfig[EmailConfig](v, key)
	// var cf EmailConfig
	// err := ParseEmailConfig(v, &cf, key)
	// return cf, err
}

func Override(v *viper.Viper, ovrMap map[string]string) error {
	ovr := viper.New()
	for key, val := range ovrMap {
		if val, err := convertValue(val, v.Get(key)); err == nil {
			ovr.Set(key, val)
		} else {
			fmt.Println("error convertValue: ", err)
		}
	}

	if err := v.MergeConfigMap(ovr.AllSettings()); err != nil {
		return err
	}

	return nil
}

func PatchConfig(v *viper.Viper, patchMap map[string]string) error {
	for key, val := range patchMap {
		if val, err := convertValue(val, v.Get(key)); err == nil {
			v.Set(key, val)
		}
	}

	return nil
}

func convertValue(val string, existing interface{}) (interface{}, error) {
	switch existing.(type) {
	case int:
		return strconv.Atoi(val)
	case int64:
		return strconv.ParseInt(val, 10, 64)
	case string:
		return val, nil
	case float32, float64:
		return strconv.ParseFloat(val, 64)
	}

	return val, nil
}

func ArraySetsToMap(configSets []string) map[string]string {
	ovrMap := make(map[string]string)
	for _, cfg := range configSets {
		cfgArr := strings.Split(cfg, "=")

		if len(cfgArr) < 2 {
			continue
		}

		key := strings.ToLower(cfgArr[0])
		val := cfgArr[1]
		ovrMap[key] = val
	}

	return ovrMap
}
