package context

import (
	"context"
	"net/http"

	"github.com/csee-pm/etl/shared/logger"
	"github.com/spf13/viper"
)

type ContextKey string

const (
	ContextKeyConfig     ContextKey = "config"
	ContextKeyLogger     ContextKey = "logger"
	ContextKeyCryptKey   ContextKey = "crypt"
	ContextKeyHttpClient ContextKey = "httpClient"
	ContextKeyRootDir    ContextKey = "root_dir"
	ContextKeyWorkDir    ContextKey = "work_dir"
)

func ExtractLogger(c context.Context) logger.Logger {
	l, ok := c.Value(ContextKeyLogger).(logger.Logger)
	if !ok {
		return logger.NewNoopLogger()
	}

	return l
}

func ExtracthttpClient(c context.Context) *http.Client {
	l, ok := c.Value(ContextKeyHttpClient).(*http.Client)
	if !ok {
		return http.DefaultClient
	}

	return l
}

func ExtractConfig(c context.Context) *viper.Viper {
	return c.Value(ContextKeyConfig).(*viper.Viper)
}

func ExtractRootDir(c context.Context) string {
	return c.Value(ContextKeyRootDir).(string)
}

func ExtractWorkDir(c context.Context) string {
	return c.Value(ContextKeyWorkDir).(string)
}
