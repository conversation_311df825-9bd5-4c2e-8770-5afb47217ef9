package process

import (
	"strconv"

	"gopkg.in/guregu/null.v4"
)

type DistribMtdParam struct {
	DtID      string  `db:"dt_id"`
	Parameter string  `db:"parameter"`
	LMTD      float64 `db:"lmtd"`
	MTD       float64 `db:"mtd"`
}

type DistribData struct {
	Brand  string      `db:"brand"`
	Circle null.String `db:"circle"`
	Region null.String `db:"region"`
	DistribMtdParam
}

func (d DistribData) GetColumns() []string {
	return []string{"brand", "circle", "region", "lmtd", "mtd", "dt_id", "parameter"}
}

func (d DistribData) GetRowValues() []string {
	return []string{d.Brand, d.Circle.String, d.Region.String, strconv.FormatFloat(d.LMTD, 'f', -1, 64), strconv.FormatFloat(d.MTD, 'f', -1, 64), d.DtID, d.Parameter}
}

func (d *DistribData) ReadRow(row []string) error {
	d.Brand = row[0]
	d.Circle = null.StringFrom(row[1])
	d.Region = null.StringFrom(row[2])
	d.LMTD, _ = strconv.ParseFloat(row[3], 64)
	d.MTD, _ = strconv.ParseFloat(row[4], 64)
	d.DtID = row[5]
	d.Parameter = row[6]
	return nil
}

type DistribReportData struct {
	EntityType   string
	EntityName   string
	ParamDataMap map[string]DistribData
}

type DistribReport struct {
	IOH    []DistribReportData
	IM3    []DistribReportData
	Tri    []DistribReportData
	Target BrandSndTarget
}

type TargetKpi struct {
	TargetGA    int
	TargetSecMn int
}

type SndTarget struct {
	Circle string
	Region string
	Brand  string
	TargetKpi
}

type EntityTarget struct {
	EntityType string
	EntityName string
	Target     TargetKpi
}

type BrandSndTarget struct {
	IOH []EntityTarget
	IM3 []EntityTarget
	Tri []EntityTarget
}
