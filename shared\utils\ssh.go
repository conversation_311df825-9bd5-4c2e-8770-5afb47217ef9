package utils

import (
	"fmt"
	"io"
	"os"
	"time"

	"golang.org/x/crypto/ssh"
)

type SSHClient struct {
	client *ssh.Client
}

func (s *SSHClient) Close() error {
	return s.client.Close()
}

func (s *SSHClient) RunCommand(cmd string) (string, error) {
	session, err := s.client.NewSession()
	if err != nil {
		return "", err
	}
	defer session.Close()

	output, err := session.CombinedOutput(cmd)
	if err != nil {
		return "", fmt.Errorf("%s. %s", err, string(output))
	}

	return string(output), nil
}

func (s *SSHClient) RunCommandAsync(cmd string) (*ssh.Session, error) {
	session, err := s.client.NewSession()
	if err != nil {
		return nil, err
	}

	err = session.Start(cmd)
	if err != nil {
		return nil, err
	}

	return session, nil
}

func (s *SSHClient) SendFile(file *os.File, remotePath string) error {
	session, err := s.client.NewSession()
	if err != nil {
		return err
	}
	defer session.Close()

	stat, err := file.Stat()
	if err != nil {
		return err
	}

	go func() {
		w, _ := session.StdinPipe()
		defer func() {
			fmt.Println("Closing sendfile session")
			w.Close()
		}()

		fmt.Fprintln(w, "C0644", stat.Size(), stat.Name())
		io.Copy(w, file)
		fmt.Fprint(w, "\x00")
	}()

	fmt.Println("Running scp")
	stdout, err := session.StdoutPipe()
	if err != nil {
		fmt.Println("Error getting stdout pipe.", err)
		return err
	}

	if err := session.Run(fmt.Sprintf("scp -t %q", remotePath)); err != nil {
		msg, err := io.ReadAll(stdout)
		if err != nil {
			fmt.Println("Error reading stdout.", err)
		}

		fmt.Println("Error running scp.", string(msg))
		return err
	}

	return nil
}

func NewSSHClient(host string, port int, user string, privateKeyPath string) (*SSHClient, error) {
	client, err := ssh.Dial("tcp", fmt.Sprintf("%s:%d", host, port), &ssh.ClientConfig{
		User: user,
		Auth: []ssh.AuthMethod{
			PrivateKeyFile(privateKeyPath),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         10 * time.Second,
	})

	if err != nil {
		return nil, err
	}

	return &SSHClient{client: client}, nil
}

func PrivateKeyFile(file string) ssh.AuthMethod {
	buffer, err := os.ReadFile(file)
	if err != nil {
		return nil
	}

	key, err := ssh.ParsePrivateKey(buffer)
	if err != nil {
		return nil
	}

	return ssh.PublicKeys(key)
}
