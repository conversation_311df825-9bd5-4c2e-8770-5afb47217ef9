package cmd

import (
	cfg "github.com/csee-pm/etl/gcpsync/config"
	prc "github.com/csee-pm/etl/gcpsync/process"
	"github.com/spf13/cobra"
)

var gaProdCmd = &cobra.Command{
	Use:   "ga-prod",
	Short: "ETL job for loading ga_prod data to BigQuery",
	RunE:  gaProdAction,
}

func InitGaProdCmd() *cobra.Command {
	gaProdCmd.Flags().StringVar(&cfg.GaProdStartDate, "start-date", "", "Start date of the data for loading. Date string in format YYYYMMDD")
	gaProdCmd.Flags().StringVar(&cfg.GaProdEndDateExc, "end-date-exc", "", "End date of the data for loading (exclusive). Date string in format YYYYMMDD")
	return gaProdCmd
}

func gaProdAction(cmd *cobra.Command, args []string) error {
	return prc.RunGaProd(cmd.Context())
}
