package process

import (
	"context"
	"embed"
	"fmt"
	"time"

	cfg "github.com/csee-pm/etl/gcpsync/config"
	prc "github.com/csee-pm/etl/shared/process"
	"github.com/spf13/viper"
)

//go:embed all:files
var procFS embed.FS

func cloneViper(v *viper.Viper) *viper.Viper {
	newV := viper.New()
	err := newV.MergeConfigMap(v.AllSettings())
	if err != nil {
		panic(err)
	}
	return newV
}

func RunGaProd(c context.Context, options ...prc.ProcessOption) error {
	if cfg.GaProdStartDate == "" {
		return fmt.Errorf("start date is required")
	}
	if cfg.GaProdEndDateExc == "" {
		return fmt.Errorf("end date is required")
	}

	startDate, err := time.Parse("20060102", cfg.GaProdStartDate)
	if err != nil {
		return fmt.Errorf("failed to parse start date. %s", err)
	}

	endDateExc, err := time.Parse("20060102", cfg.GaProdEndDateExc)
	if err != nil {
		return fmt.Errorf("failed to parse end date. %s", err)
	}

	gaProdProc := NewGaProdProcess(procFS)
	return gaProdProc.StartProcess(c, startDate, endDateExc)
}
