package utils

func SliceContains[T comparable](s []T, e T) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func SliceContainsAny[T comparable](s []T, e []T) bool {
	for _, a := range s {
		if SliceContains(e, a) {
			return true
		}
	}
	return false
}

func RemoveFromSlice[T comparable](s []T, e T) []T {
	for i, a := range s {
		if a == e {
			return append(s[:i], s[i+1:]...)
		}
	}
	return s
}

func RemoveFromSliceAny[T comparable](s []T, e []T) []T {
	for i, a := range s {
		if SliceContains(e, a) {
			return append(s[:i], s[i+1:]...)
		}
	}
	return s
}

func SliceMap[T, U any](s []T, fn func(T) U) []U {
	mapped := make([]U, len(s))
	for i, v := range s {
		mapped[i] = fn(v)
	}
	return mapped
}

func SliceFilter[T any](s []T, fn func(T) bool) []T {
	var filtered []T
	for i, v := range s {
		if fn(v) {
			filtered = append(filtered, s[i])
		}
	}
	return filtered
}

func SliceToMap[T any, K comparable, V any](s []T, fn func(T) (K, V)) map[K]V {
	m := make(map[K]V)
	for _, v := range s {
		k, v := fn(v)
		m[k] = v
	}
	return m
}

func SplitBatch[T any](list []T, chunk int) [][]T {
	total := len(list)
	rem := total % chunk
	batch := total / chunk

	if rem > 0 {
		batch++
	}

	var newList = make([][]T, batch)
	start := 0
	end := chunk
	for i := 0; i < batch; i++ {
		if end > total {
			end = total
		}

		newList[i] = list[start:end]
		start += chunk
		end += chunk
	}

	return newList
}
