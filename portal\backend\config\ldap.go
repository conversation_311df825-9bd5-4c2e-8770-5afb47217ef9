package config

type LdapConfig struct {
	Host       string `yaml:"host"`
	Port       int    `yaml:"port"`
	BindDN     string `yaml:"bind_dn" mapstructure:"bind_dn"`
	SearchDN   string `yaml:"search_dn" mapstructure:"search_dn"`
	Credential string `yaml:"credential"`
	UserDn     string `yaml:"user" mapstructure:"-"`
	Password   string `yaml:"password" mapstructure:"-"`
}

func (lc LdapConfig) ToMap() map[string]any {
	var cfMap = make(map[string]interface{})
	cfMap["host"] = lc.Host
	cfMap["port"] = lc.Port
	cfMap["bind_dn"] = lc.BindDN
	cfMap["search_dn"] = lc.SearchDN
	cfMap["credential"] = lc.Credential
	return cfMap
}
