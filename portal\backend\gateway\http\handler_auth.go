package http

import (
	"net/http"

	"github.com/csee-pm/etl/portal/backend/internal/idm"
	htx "github.com/likearthian/apikit/transport/http"
)

type AuthAPIHandlers struct {
	AuthenticateHandler  http.Handler
	ValidateTokenHandler http.Handler
	RefreshTokenHandler  http.Handler
}

func createAuthHandlers(authAPI *idm.AuthAPI, options ...htx.ServerOption) AuthAPIHandlers {
	return AuthAPIHandlers{
		AuthenticateHandler: htx.NewServer(
			authAPI.AuthenticateEndpoint,
			htx.CommonPostRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		ValidateTokenHandler: htx.NewServer(
			authAPI.ValidateTokenEndpoint,
			htx.CommonGetRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
		RefreshTokenHandler: htx.NewServer(
			authAPI.RefreshTokenEndpoint,
			htx.CommonGetRequestDecoder,
			htx.CommonJSONResponseEncoder,
			options...,
		),
	}
}
