package config

type EmailServer struct {
	Host          string `yaml:"host"`
	Port          int    `yaml:"port"`
	Credential    string `yaml:"credential"`
	User          string `yaml:"-" config:"user"`
	Pass          string `yaml:"-" config:"password"`
	SenderAddress string `yaml:"sender_address" mapstructure:"sender_address"`
	SenderName    string `yaml:"sender_name" mapstructure:"sender_name"`
}

func (es EmailServer) ToMap() map[string]any {
	var cfMap = make(map[string]interface{})
	cfMap["host"] = es.Host
	cfMap["port"] = es.Port
	cfMap["credential"] = es.Credential
	cfMap["sender_address"] = es.SenderAddress
	cfMap["sender_name"] = es.SenderName
	return cfMap
}

type EmailConfig struct {
	Host          string   `yaml:"host"`
	Port          int      `yaml:"port"`
	Credential    string   `yaml:"credential"`
	User          string   `yaml:"-"`
	Pass          string   `yaml:"-"`
	SenderAddress string   `yaml:"sender_address" mapstructure:"sender_address"`
	SenderName    string   `yaml:"sender_name" mapstructure:"sender_name"`
	Receiver      []string `yaml:"receiver" mapstructure:"receiver"`
}

func (ec EmailConfig) ToMap() map[string]any {
	var cfMap = make(map[string]interface{})
	cfMap["host"] = ec.Host
	cfMap["port"] = ec.Port
	cfMap["credential"] = ec.Credential
	cfMap["sender_address"] = ec.SenderAddress
	cfMap["sender_name"] = ec.SenderName
	cfMap["receiver"] = ec.Receiver
	return cfMap
}
