with outlet_ref as (
    select
        x.id outlet_id,
        y.circle,
        y.region
    from
        (
            select
                id,
                kec_unik,
                row_number() over (partition by id order by mth desc) rnk
            from
                rdm.spa_tbl_ref_lowlevel_mth
            where
                mth >= substr(${start_dt_id},1,6)
            and mth <= substr(${end_dt_id},1,6)
            and flag = 'OUTLET'
        ) x
        inner join
        biadm.ref_kecamatan y
        on
            x.kec_unik = y.kec_kabkot
    where
        x.rnk = 1
)
select
    a.dt_id,
    'IM3' brand,
    a.organization_id,
    r.circle,
    r.region,
    sum(a.amount) saldo_amount
from
    rdm.bai_trade_ssd_v1 a
    left join
    outlet_ref r
    on
        a.organization_id = r.outlet_id
where
    1 = 1
and a.dt_id >= ${start_dt_id}
and a.dt_id <= ${end_dt_id}
and a.type = 'Stock'
and a.category = 'SALDO MOBO-OUTLET'
group by 1,3,4,5;