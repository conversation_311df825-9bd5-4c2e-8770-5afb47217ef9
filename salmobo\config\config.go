package config

import (
	"fmt"

	cfg "github.com/csee-pm/etl/shared/config"
	"github.com/csee-pm/etl/shared/logger"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/spf13/viper"
)

type Config struct {
	Tunnel    *cfg.TunnelConfig `yaml:"tunnel"`
	GP        cfg.GPConfig      `yaml:"gpfat"`
	Impala    cfg.ImpalaConfig  `yaml:"impala"`
	EtlConfig *EtlConfig        `yaml:"etl_config"`
	Notifier  *NotifierConfig   `yaml:"notifier"`
	LogLevel  logger.Level      `yaml:"log_level"`
	WorkDir   string            `yaml:"work_dir"`
}

func (c Config) ToMap() map[string]interface{} {
	var cfMap = make(map[string]interface{})
	if c.Tunnel != nil {
		cfMap["tunnel"] = c.Tunnel.ToMap()
	}

	cfMap["gpfat"] = c.GP.ToMap()
	cfMap["impala"] = c.Impala.ToMap()

	if c.EtlConfig != nil {
		cfMap["etl_config"] = c.EtlConfig.ToMap()
	}

	cfMap["log_level"] = c.LogLevel
	cfMap["work_dir"] = c.WorkDir

	return cfMap
}

type EtlConfig struct {
	WorkDate *int `yaml:"work_date"`
}

func (et EtlConfig) ToMap() map[string]interface{} {
	var etMap = make(map[string]interface{})
	if et.WorkDate != nil {
		etMap["mtd_date"] = *et.WorkDate
	}
	return etMap
}

type NotifierConfig struct {
	Email *cfg.EmailConfig `yaml:"email"`
}

func (n NotifierConfig) ToMap() map[string]interface{} {
	var nMap = make(map[string]interface{})
	if n.Email != nil {
		nMap["email"] = n.Email.ToMap()
	}

	return nMap
}

func GetEtlConfig(v *viper.Viper, key string) (*EtlConfig, error) {
	var cf EtlConfig
	err := v.UnmarshalKey(key, &cf)
	return &cf, err
}

func SetNewConfig(v *viper.Viper) error {
	useTun := utils.StringToBool(cfg.GetPromptValue("Use SSH Tunnel To connect to GP[y/N]: ", "N", cfg.ShouldBeYesOrNo))
	if useTun {
		tunCfg, err := cfg.PromptNewTunnelConfig(v, "tunnel")
		if err != nil {
			return err
		}

		v.Set("tunnel", tunCfg.ToMap())
	}

	fmt.Println("\nPlease provide Greenplum configuration")
	gpCfg, err := cfg.PromptNewGPConfig(v, "gpfat")
	if err != nil {
		return err
	}
	v.Set("gpfat", gpCfg.ToMap())

	fmt.Println("\nPlease provide Impala configuration")
	impalaCfg, err := cfg.PromptNewImpalaConfig(v, "impala")
	if err != nil {
		return err
	}
	v.Set("impala", impalaCfg.ToMap())

	useNotify := utils.StringToBool(cfg.GetPromptValue("Do you want to use email notifier?[y/N]: ", "N", cfg.ShouldBeYesOrNo))
	if useNotify {
		fmt.Println("\nPlease provide Email configuration")
		emailCfg, err := cfg.PromptNewEmailConfig(v, "notifier.email")
		if err != nil {
			return err
		}

		notifyConfig := &NotifierConfig{
			Email: &emailCfg,
		}
		v.Set("notifier", notifyConfig.ToMap())
	}

	return nil
}
