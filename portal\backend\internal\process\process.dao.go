package process

import (
	"github.com/csee-pm/etl/portal/backend/internal/db"
	"github.com/jmoiron/sqlx"
	"gopkg.in/guregu/null.v4"
)

type Process struct {
	ID        string      `db:"id" json:"id"`
	Name      null.String `db:"name" json:"name"`
	Status    string      `db:"status" json:"status"`
	StartTime int64       `db:"start_time" json:"start_time"`
	EndTime   null.Int    `db:"end_time,omitempty" json:"end_time"`
	Command   null.String `db:"command" json:"command"`
	Options   string      `db:"args" json:"args"`
	Output    string      `db:"output" json:"output"`
	StartedBy string      `db:"started_by" json:"started_by"`
}

type ProcessDAO struct {
	db    *sqlx.DB
	model db.DBModel
}

func NewProcessDAO(cl *sqlx.DB) (*ProcessDAO, error) {
	model := db.CreateGenericModel[Process]("process", "")
	processDAO := &ProcessDAO{db: cl, model: model}
	err := processDAO.Init()
	if err != nil {
		return nil, err
	}
	return processDAO, nil
}

func (dao *ProcessDAO) Init() error {
	_, err := dao.db.Exec(`
		CREATE TABLE IF NOT EXISTS process (
			id TEXT PRIMARY KEY,
			name TEXT,
			status TEXT NOT NULL,
			start_time INTEGER NOT NULL,
			end_time INTEGER,
			command TEXT,
			args TEXT,
			output TEXT,
			started_by TEXT
		)
	`)
	return err
}
