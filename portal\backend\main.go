package main

import (
	"embed"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/coreos/go-systemd/v22/daemon"
	"github.com/csee-pm/etl/portal/backend/api"
	"github.com/csee-pm/etl/portal/backend/config"
	htgw "github.com/csee-pm/etl/portal/backend/gateway/http"
	"github.com/jmoiron/sqlx"
	"github.com/joho/godotenv"
	log "github.com/likearthian/apikit/logger"
	"github.com/sirupsen/logrus"
	_ "modernc.org/sqlite"
)

//go:embed web
var webFS embed.FS

func main() {
	out := os.Stdout

	// Load environment variables from .env file
	if err := godotenv.Load(); err != nil {
		fmt.Fprintf(out, "Failed to load .env file: %v", err)
	}

	// Load configuration
	v, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("Failed to load configuration: %v", err))
	}

	cfg, err := config.GetConfig(v)
	if err != nil {
		panic(fmt.Sprintf("Failed to get configuration: %v", err))
	}

	logger := createLogger(log.Level(cfg.LogLevel), out)

	// Initialize database
	database, err := initializeDB(cfg.DBPath)
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize database: %v", err))
	}
	defer database.Close()

	// Initialize API endpoints
	apiEndpoints, err := api.CreateAPIEndpoints(v, database, logger)
	if err != nil {
		panic(fmt.Sprintf("Failed to create api endpoints: %v", err))
	}

	// initialize web server
	server := htgw.NewHTTPServer(apiEndpoints, webFS, logger)

	errChan := make(chan error)
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errChan <- fmt.Errorf("%s", <-c)
	}()

	// Activate liveness watchdog for systemd
	go func() {
		interval, err := daemon.SdWatchdogEnabled(false)
		if err != nil || interval == 0 {
			return
		}
		for {
			res, err := http.Get(fmt.Sprintf("http://%s:%d/ping", cfg.Host, cfg.Port))
			if err == nil {
				daemon.SdNotify(false, daemon.SdNotifyWatchdog)
			}

			res.Body.Close()
			time.Sleep(interval / 2)
		}
	}()

	// Start server
	addr := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)
	go func() {
		logger.Info("bpcs portal server started", "host", cfg.Host, "port", cfg.Port)
		errChan <- http.ListenAndServe(addr, server)
	}()

	daemon.SdNotify(false, daemon.SdNotifyReady)
	logger.Info("exit", <-errChan)
}

func initializeDB(dbPath string) (*sqlx.DB, error) {
	db, err := sqlx.Open("sqlite", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

func createLogger(level log.Level, out io.Writer) log.Logger {
	ruslog := logrus.New()
	ruslog.SetFormatter(&logrus.TextFormatter{FullTimestamp: true})
	ruslog.SetOutput(out)

	logger := log.NewRusLog(ruslog)
	logger.SetLevel(level)

	return logger
}
