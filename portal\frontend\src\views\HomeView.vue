<template>
  <div class="home">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">ETL Portal</h1>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Processes Card -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Processes</h2>
          <p class="text-gray-600 mb-4">Monitor and manage ETL processes</p>
          <router-link 
            to="/processes" 
            class="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            View Processes
          </router-link>
        </div>

        <!-- Files Card -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Files</h2>
          <p class="text-gray-600 mb-4">Browse and manage generated files</p>
          <router-link 
            to="/files" 
            class="inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
          >
            View Files
          </router-link>
        </div>

        <!-- ETL Control Card -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">ETL Control</h2>
          <p class="text-gray-600 mb-4">Start and stop ETL operations</p>
          <router-link 
            to="/etl" 
            class="inline-block bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors"
          >
            ETL Control
          </router-link>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="mt-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Recent Activity</h2>
        <div class="bg-white rounded-lg shadow-md p-6">
          <p class="text-gray-600">No recent activity to display.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// This is a basic home view for the ETL Portal
</script>

<style scoped>
.home {
  min-height: 100vh;
  background-color: #f9fafb;
}
</style>
