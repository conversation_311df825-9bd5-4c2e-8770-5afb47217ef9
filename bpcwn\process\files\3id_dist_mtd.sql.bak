with quro as (
SELECT load_dt_sk_id AS dt_id,
		case when load_dt_sk_id/100 = to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int then 'lmtd'
					when load_dt_sk_id/100 = ${mtd_dt_int}/100 then 'mtd'
					end mthf,
           a.site_id,
           circle,
           region_circle region,
           CASE
               WHEN kpi_code = 'qsso_any' THEN 'qsso'
               WHEN kpi_code = 'quro_any' THEN 'quro'
               ELSE kpi_code
           END AS kpi_code,
           SUM(value) AS value
    FROM mis.project_ioh_kpi_daily_tracker_site a
    LEFT OUTER JOIN ioh_biadm.ref_site_h3i c
               ON CASE WHEN LENGTH(a.site_id) <= 5 THEN LPAD(a.site_id, 6, '0')
                       ELSE a.site_id
                  END = CASE WHEN LENGTH(c.site_id) <= 5 THEN LPAD(c.site_id, 6, '0')
                             ELSE c.site_id
                        END
WHERE kpi_code IN ('qsso_any','quro_any')
   and load_dt_sk_id >= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMMDD')::integer
   	and substring(load_dt_sk_id::text ,7,2) = substring(${mtd_dt_int}::text,7,2)  
GROUP BY 1, 2, 3, 4, 5,6
),
dse AS 
(
			select 		distinct se_partnerid
						,circle
						,region
						,mth_id::int mth_id
			from 		marketing.snd_outlet_mapping a
			where 		a.mth_id >= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')
),
kec as 
(
			select 	distinct partner_id pt_id, kecamatan, kabkot, region, circle, mth_id
			from 	marketing.snd_kec_teritorry
			where 	mth_id >= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int
			and 	partner_type='3KIOSK'
),
kec_smy as 
(
	select 		kec.pt_id
				,kec.circle
				,kec.region
				,case when dt_id/100 = to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int then 'lmtd'
							when dt_id/100 = ${mtd_dt_int}/100 then 'mtd' end mthf
				,sum(case when kpi_name='RGUGA-Trad' then kpi_value else null end) rgu_ga
				,sum(case when kpi_name='secondary' then kpi_value else null end)secondary
	from 		(select * from marketing.and_snd_kecamatan_summary_dly  
					where dt_id >= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMMDD')::int
					and dt_id <= ${mtd_dt_int}
					and substring(dt_id::text,7,2) <= substring(${mtd_dt_int}::text,7,2)
				)a
	left join   kec on a.kecamatan||'|'||a.kabupaten =kec.kecamatan||'|'||kec.kabkot		
	group by 	1,2,3,4
)
select 		'3ID' brand
			,kec.circle
			,kec.region
			,count(distinct case when mthf='lmtd' and  (coalesce(a.rgu_ga,0)/substring(${mtd_dt_int}::text,7,2)::int)<1 then a.site_id else null end) lmtd
			,count(distinct case when mthf='mtd' and (coalesce(a.rgu_ga,0)/substring(${mtd_dt_int}::text,7,2)::int)<1 then a.site_id else null end) mtd
			,${mtd_dt_int} dt_id			
			,'Site w/ <1 GAD/Day' parameter			
from 		(
			select 	distinct site_id
			from 	marketing.nshim3_tbl_ref_addrsite_gtm_mth site_ref
			where 	site_ref.mth >= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')
			and 	trim(addressable_type) = 'ADDRESSABLE SITE'
			and 	list_3id = 'Y'
			) site_ref
left join (
			select site_id
				,dt_id
				,case when dt_id/100 = to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int then 'lmtd'
						when dt_id/100 = ${mtd_dt_int}/100 then 'mtd'
						end mthf
				,sum(value) rgu_ga
			from marketing.trd_hor_racing_kpi_src a
			where dt_id >= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMMDD')::int
					and dt_id <=${mtd_dt_int}
				and substring(dt_id::text,7,2) = substring(${mtd_dt_int}::text,7,2) 
				and kpi_name='rgu_ga_trade'
			group by 1,2,3
		 ) a on site_ref.site_id=a.site_id
left join 	ioh_biadm.ref_site_h3i site on a.site_id =site.site_id
left join   marketing.snd_kec_teritorry kec on site.kecamatan_nm||'|'||site.kabkot_nm=kec.kecamatan||'|'||kec.kabkot and kec.mth_id='202501' 			
where 		kec.circle is not null
group by 	1,2,3,6,7
 
union all
 
--DSE WITH <12 GAD/DAY
select 		'3ID' brand			
			,dse.circle
			,dse.region
			,count(distinct case when mthf='lmtd' and (coalesce(a.rgu_ga,0)/substring(${mtd_dt_int}::text,7,2)::int)<12 then dse.se_partnerid else null end) lmtd			
			,count(distinct case when mthf='mtd' and (coalesce(a.rgu_ga,0)/substring(${mtd_dt_int}::text,7,2)::int)<12 then dse.se_partnerid else null end) mtd
			,${mtd_dt_int} dt_id
			,'DSE w/ <12 GAD' parameter
from 		 dse
left join 	(
			select 	se_partnerid
			,dt_id/100 mth_id
			,case when dt_id/100 = to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int then 'lmtd'
				when dt_id/100 = ${mtd_dt_int}/100 then 'mtd' end mthf
			,sum(value) rgu_ga
				from (select * from marketing.trd_hor_racing_kpi_src
						where dt_id >= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMMDD')::int
								and dt_id <=${mtd_dt_int}
						and substring(dt_id::text,7,2) = substring(${mtd_dt_int}::text,7,2)
						and kpi_name='rgu_ga_trade'
					) a
				left join (select * from dwh.retailer_hierarchy
							where periode_data=${mtd_dt_int}
							and trx_type='Owner'
						)b on a.partner_qr_cd=b.retailer_qrcode
				group by 1,2,3
			) a on dse.se_partnerid=a.se_partnerid and dse.mth_id=a.mth_id
group by 	1,2,3,6,7
 
union all

--DSE WITH <5 MN/DAY
select 		'3ID' brand
			,dse.circle
			,dse.region
			,count(distinct case when mthf='lmtd' and (coalesce(a.amount,0)/substring(${mtd_dt_int}::text,7,2)::int)<5000000 then dse.se_partnerid else null end) lmtd
			,count(distinct case when mthf='mtd' and (coalesce(a.amount,0)/substring(${mtd_dt_int}::text,7,2)::int)<5000000 then dse.se_partnerid else null end) mtd
			,${mtd_dt_int} dt_id			
			,'DSE w/ <5 Mn Secondary' parameter

from dse
left join 	(
			select 		se_partnerid, dt_id
						,case when dt_id/100 = to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int then 'lmtd'
							when dt_id/100 = ${mtd_dt_int}/100 then 'mtd' end mthf
						,sum(a.value)/1.11 amount
			from 		marketing.bai_snd_prisec_detail a
			left join 	(select * from dwh.retailer_hierarchy 
							where periode_data=${mtd_dt_int}
							and trx_type='Owner'
						)b on a.qr_code=b.retailer_qrcode
			where 		trim(lower(kpi_name)) in ('secondary')
			and 		dt_id >= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMMDD')::int
									and dt_id <=${mtd_dt_int}
							and substring(dt_id::text,7,2) = substring(${mtd_dt_int}::text,7,2)
			group by 	1,2,3
			) a on dse.se_partnerid=a.se_partnerid
group by 	1,2,3,6,7
 
union all
 
 
--TOTAL DSE
select 		'3ID' brand
			,dse.circle
			,dse.region
			,count(distinct case when mth_id= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int then dse.se_partnerid else null end)lmtd
			,count(distinct case when mth_id= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')),'YYYYMM')::int then dse.se_partnerid else null end) mtd
			,${mtd_dt_int} dt_id		
			,'DSE' parameter
			from dse
group by 	1,2,3,6,7
 
union all
 
--insert into trd_dcco_tracker_smy
--ADDRESSABLE SITE
select 		'IM3' brand
			,kec.circle
			,kec.region
			,count(distinct case when mth_id= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int then site_id else null end) lmtd
			,count(distinct case when mth_id= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')),'YYYYMM')::int then site_id else null end) mtd
			,${mtd_dt_int} dt_id
			,'Addressable Site' parameter			
from 		marketing.nshim3_tbl_ref_addrsite_gtm_mth site_ref
left join   marketing.snd_kec_teritorry kec on site_ref.kecamatan||'|'||site_ref.kabupaten=kec.kecamatan||'|'||kec.kabkot and
			kec.mth_id >= cast(to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM') as int)
where 		site_ref.mth = substring(${mtd_dt_int}::text,1,6)
and 		trim(addressable_type) = 'ADDRESSABLE SITE'
and 		list_ioh = 'Y'
group by 	1,2,3,6,7

 
union all
 
--ADDRESSABLE SITE
select 		'3ID' brand
			,kec.circle
			,kec.region
			,count(distinct case when mth_id= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM')::int then site_id else null end) lmtd
			,count(distinct case when mth_id= to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')),'YYYYMM')::int then site_id else null end) mtd
			,${mtd_dt_int} dt_id
			,'Addressable Site' parameter			
from 		marketing.nshim3_tbl_ref_addrsite_gtm_mth site_ref
left join   marketing.snd_kec_teritorry kec on site_ref.kecamatan||'|'||site_ref.kabupaten=kec.kecamatan||'|'||kec.kabkot and
			kec.mth_id >= cast(to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')) - interval '1 month','YYYYMM') as int)
where 		site_ref.mth =substring(${mtd_dt_int}::text,1,6)
and 		trim(addressable_type) = 'ADDRESSABLE SITE'
and 		list_ioh = 'Y'
group by 	1,2,3,6,7

union all
 

--SDP<350 GA
select 		'3ID' brand
			,a.circle
			,a.region
			,count(distinct case when mthf='lmtd' and coalesce(rgu_ga,0)<350 then kec.pt_id else null end) lmtd
			,count(distinct case when mthf='mtd' and coalesce(rgu_ga,0)<350 then kec.pt_id else null end) mtd
			,${mtd_dt_int} dt_id
			,'SDP <350 GA' kpi_name
from  kec
left join kec_smy a on kec.pt_id=a.pt_id
group by 	1,2,3,6,7
 
union all
 
--SDP<75 MN
select 		'3ID' brand
			,a.circle
			,a.region
			,count(distinct case when mthf='lmtd' and coalesce(secondary,0)<75000000 then kec.pt_id else null end) lmtd
			,count(distinct case when mthf='mtd' and coalesce(secondary,0)<75000000 then kec.pt_id else null end) mtd
			,${mtd_dt_int} dt_id
			,'SDP <75 Mn' kpi_name
from kec
left join kec_smy a on kec.pt_id=a.pt_id
group by 	1,2,3,6,7
;