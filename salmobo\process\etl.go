package process

import (
	"context"
	"embed"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/csee-pm/etl/shared/channel"
	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

//go:embed all:sql
var sqlFS embed.FS

var pjpfilterfile = "pjp_outlets.csv"

func RunETL(c context.Context) error {
	conf := ctx.ExtractConfig(c)
	workDate := time.Now().AddDate(0, 0, -2)
	startDt, _ := strconv.Atoi(workDate.AddDate(0, 0, -2).Format("20060102"))
	endDt, _ := strconv.Atoi(workDate.Format("20060102"))

	fromFile := conf.GetString("etl_config.from_file")
	if fromFile != "" {
		return RunETLFromFile(c)
	}

	var err error
	if conf.Get("etl_config.work_date") != nil {
		workDate, err = time.Parse("20060102", conf.GetString("etl_config.work_date"))
		if err != nil {
			return fmt.Errorf("failed to parse work_date. %s", err)
		}

		startDt, _ = strconv.Atoi(workDate.AddDate(0, 0, -2).Format("20060102"))
		endDt, _ = strconv.Atoi(workDate.Format("20060102"))
	}

	logger := ctx.ExtractLogger(c)

	tunnel := conf.Get("tunnel")
	var useTunnel = false
	if tunnel != nil {
		useTunnel = true
	}

	if useTunnel {
		tunConfig, err := cfg.GetTunnelConfig(conf, "tunnel")
		if err != nil {
			return fmt.Errorf("failed to get tunnel config. %s", err)
		}

		if err := etlProc.StartTunnel(tunConfig, logger); err != nil {
			return fmt.Errorf("failed to start tunnel. %s", err)
		}
	}

	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)

	wg.Add(1)
	var salmoData []SalmoboData
	go func() {
		defer wg.Done()
		salmoData, err = getSalmoboData(cCancel, startDt, endDt)
		if err != nil {
			logger.Error("failed to get Salmobo data", "error", err)
			cancel()
		}
	}()

	wg.Wait()
	if err != nil {
		return err
	}

	logger.Debug("Salmobo data fetched", "count", len(salmoData))

	return createReport(c, salmoData)
}

func RunETLFromFile(c context.Context) error {
	conf := ctx.ExtractConfig(c)
	fromFile := conf.GetString("etl_config.from_file")
	if fromFile == "" {
		return fmt.Errorf("from_file is not set")
	}

	salmoData, err := readSalmoboDataFromFile(fromFile)
	if err != nil {
		return fmt.Errorf("failed to read salmobo data from file. %s", err)
	}

	return createReport(c, salmoData)
}

func createReport(c context.Context, salmoData []SalmoboData) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	workDir, ok := conf.Get("work_dir").(string)
	if !ok {
		workDir = "workdir"
	}

	if conf.Get("pjpfilterfile") != nil {
		pjpfilterfile = conf.GetString("pjpfilterfile")
	}

	if err := os.MkdirAll(workDir, os.ModePerm); err != nil {
		return fmt.Errorf("failed to create work dir. %s", err)
	}

	logger.Debug("starting data post-process")
	processedData, err := postProcessData(salmoData)
	if err != nil {
		return fmt.Errorf("failed to post-process salmobo data. %s", err)
	}
	logger.Debug("data post-process finished", "count", len(processedData))

	csvFilePath := fmt.Sprintf("%s/salmobo_%s.csv", workDir, time.Now().Format("20060102150405"))
	processedCsvFilePath := fmt.Sprintf("%s/mean_salmobo_%s.csv", workDir, time.Now().Format("20060102150405"))

	if err := utils.WriteToCsv(csvFilePath, salmoData); err != nil {
		logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
	}
	logger.Info("CSV file written", "path", csvFilePath)

	if err := utils.WriteToCsv(processedCsvFilePath, processedData); err != nil {
		logger.Error("failed to write CSV file", "path", processedCsvFilePath, "error", err)
	}
	logger.Info("CSV file written", "path", processedCsvFilePath)

	xlFilePath := fmt.Sprintf("%s/OUTLET_SALMOBO_%s.xlsx", workDir, time.Now().Format("20060102150405"))

	reportData := createRegionalSalmoboData(processedData)

	logger.Debug("writing report", "path", xlFilePath)
	return writeReport(reportData, xlFilePath)
}

func readSalmoboDataFromFile(fromFile string) ([]SalmoboData, error) {
	f, err := os.Open(fromFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()
	var salmoData []SalmoboData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		amount, err := strconv.ParseFloat(record[4], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse saldo amount. %s", err)
		}

		salmoData = append(salmoData, SalmoboData{
			Brand:          record[0],
			OrganizationId: record[1],
			Circle:         null.StringFrom(record[2]),
			Region:         null.StringFrom(record[3]),
			SaldoAmount:    amount,
		})
	}

	return salmoData, nil
}

func getSalmoboData(c context.Context, startDt int, endDt int) ([]SalmoboData, error) {
	var data []SalmoboData
	logger := ctx.ExtractLogger(c)
	task1 := channel.RunAsync(func() ([]SalmoboData, error) {
		res, err := get3idSalmobo(c, startDt, endDt)
		if err != nil {
			return nil, fmt.Errorf("failed to get 3ID salmobo data. %s", err)
		}
		logger.Debug("3ID salmobo data fetched", "count", len(res))
		return res, nil
	})

	task2 := channel.RunAsync(func() ([]SalmoboData, error) {
		res, err := getIM3Salmobo(c, startDt, endDt)
		if err != nil {
			return nil, fmt.Errorf("failed to get IM3 salmobo data. %s", err)
		}
		logger.Debug("IM3 salmobo data fetched", "count", len(res))
		return res, nil
	})

	mtdChan := channel.MergeChan(c, task1, task2)
	for mtd := range mtdChan {
		if mtd.IsErr() {
			return nil, mtd.UnwrapErr()
		}

		data = append(data, mtd.Unwrap()...)
	}

	return data, nil
}

func getIM3Salmobo(c context.Context, startDt int, endDt int) ([]SalmoboData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sqlFS.ReadFile("sql/im3_salmobo.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"start_dt_id": {Name: "start_dt_id", Value: strconv.Itoa(startDt)},
		"end_dt_id":   {Name: "end_dt_id", Value: strconv.Itoa(endDt)},
	}

	logger.Debug("querying data for IM3 Salmobo")
	return etlProc.QueryImpalaData[SalmoboData](c, string(buf), params)
}

func get3idSalmobo(c context.Context, startDt int, endDt int) ([]SalmoboData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sqlFS.ReadFile("sql/3id_salmobo.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"start_dt_int": {Name: "start_dt_int", Value: startDt},
		"end_dt_int":   {Name: "end_dt_int", Value: endDt},
	}

	logger.Debug("querying data for 3ID Salmobo")

	return etlProc.QueryGreenplumData[SalmoboData](c, string(buf), params)
}

func postProcessData(data []SalmoboData) ([]FinalSalmoboData, error) {
	var dataMap = make(map[string]*FinalSalmoboData)
	for _, d := range data {
		key := fmt.Sprintf("%s|%s|%s|%s", d.Brand, d.OrganizationId, d.Circle.String, d.Region.String)
		if _, ok := dataMap[key]; !ok {
			dataMap[key] = &FinalSalmoboData{
				Brand:          d.Brand,
				OrganizationId: d.OrganizationId,
				Circle:         d.Circle.String,
				Region:         d.Region.String,
			}
		}
		dataItem := dataMap[key]
		dataItem.AddSaldoAmount(d.SaldoAmount)
		dataItem.DtIds = append(dataItem.DtIds, d.DtID)
	}

	ret := make([]FinalSalmoboData, len(dataMap))
	for i := range dataMap {
		d := dataMap[i]
		bin := getBins(d.AvgSaldoAmount)
		d.Slab = labels[bin]

		ret = append(ret, *d)
	}
	return filterPjp(ret)
}

func filterPjp(data []FinalSalmoboData) ([]FinalSalmoboData, error) {
	f, err := os.Open(pjpfilterfile)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	var pjpMap = make(map[string]struct{})
	cr.Read()
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}
		key := strings.Join(record, "|")
		pjpMap[key] = struct{}{}
	}

	var filteredData []FinalSalmoboData
	for i, d := range data {
		if _, ok := pjpMap[fmt.Sprintf("%s|%s", d.OrganizationId, d.Brand)]; ok {
			filteredData = append(filteredData, data[i])
		}
	}

	return filteredData, nil
}

var bins = []float64{0, 10_000, 50_000, 100_000, 200_000, 500_000, 1_000_000}
var labels = []string{"0", "<10k", "10k-50k", "50k-100k", "100k-200k", "200k-500k", "500k-1M", ">1M"}

func getBins(saldoAmount float64) int {
	for i, b := range bins {
		if saldoAmount <= b {
			return i
		}
	}
	return len(labels) - 1
}

func createRegionalSalmoboData(data []FinalSalmoboData) SalmoboReport {
	dIM3 := &SalmoboReportData{
		Regional: make(map[string]map[string]int),
		Circle:   make(map[string]map[string]int),
	}

	d3ID := &SalmoboReportData{
		Regional: make(map[string]map[string]int),
		Circle:   make(map[string]map[string]int),
	}

	dtIds := data[0].DtIds
	dt, _ := time.Parse("20060102", dtIds[0])
	var start = dt
	var end = dt
	for _, d := range dtIds {
		dt, _ = time.Parse("20060102", d)
		if start.After(dt) {
			start = dt
		}

		if end.Before(dt) {
			end = dt
		}
	}

	for _, d := range data {
		dmap := dIM3
		if d.Brand == "3ID" {
			dmap = d3ID
		}

		regmap := dmap.Regional
		circlemap := dmap.Circle

		if _, ok := regmap[d.Region]; !ok {
			regmap[d.Region] = make(map[string]int)
			for i := range labels {
				regmap[d.Region][labels[i]] = 0
			}
		}

		if _, ok := circlemap[d.Circle]; !ok {
			circlemap[d.Circle] = make(map[string]int)
			for i := range labels {
				circlemap[d.Circle][labels[i]] = 0
			}
		}

		regmap[d.Region][d.Slab]++
		circlemap[d.Circle][d.Slab]++
	}

	return SalmoboReport{
		ReportStartDate: start,
		ReportEndDate:   end,
		IM3:             *dIM3,
		Three:           *d3ID,
	}
}

func writeReport(data SalmoboReport, xlFile string) error {
	f, err := sqlFS.Open("sql/OUTLET_SALMOBO.xlsx")
	if err != nil {
		return err
	}
	defer f.Close()

	xl, err := excelize.OpenReader(f)
	if err != nil {
		return err
	}

	shname := "Summary"
	startIM3 := xlutil.Cell(7, 3)
	start3ID := xlutil.Cell(28, 3)

	dIM3 := data.IM3
	d3ID := data.Three

	//do IM3 report
	rownum := len(dIM3.Regional) + len(dIM3.Circle)
	startRow := startIM3.Row
	startCol := startIM3.Col
	dNational := make(map[string]int)
	for _, slab := range labels {
		dNational[slab] = 0
	}
	for i := 0; i < rownum; i++ {
		entity, err := xl.GetCellValue(shname, xlutil.Cell(startRow+i, startCol-1).Address())
		if err != nil {
			return err
		}
		entity = strings.TrimSpace(entity)
		if entity == "" {
			continue
		}

		var slabs map[string]int
		if _, ok := dIM3.Regional[entity]; ok {
			slabs = dIM3.Regional[entity]
		}

		if _, ok := dIM3.Circle[entity]; ok {
			slabs = dIM3.Circle[entity]
			for _, slab := range labels {
				dNational[slab] += slabs[slab]
			}
		}

		if slabs != nil {
			total := 0
			for idx, slab := range labels {
				total += slabs[slab]
				if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+idx).Address(), slabs[slab]); err != nil {
					return err
				}
			}
			if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+len(labels)).Address(), total); err != nil {
				return err
			}
		}

		if entity == "INDONESIA" {
			total := 0
			for idx, slab := range labels {
				total += dNational[slab]
				if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+idx).Address(), dNational[slab]); err != nil {
					return err
				}
			}
			if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+len(labels)).Address(), total); err != nil {
				return err
			}
		}
	}

	//do 3ID report
	rownum = len(d3ID.Regional) + len(d3ID.Circle)
	startRow = start3ID.Row
	startCol = start3ID.Col
	dNational = make(map[string]int)
	for _, slab := range labels {
		dNational[slab] = 0
	}

	for i := 0; i < rownum; i++ {
		entity, err := xl.GetCellValue(shname, xlutil.Cell(startRow+i, startCol-1).Address())
		if err != nil {
			return err
		}
		entity = strings.TrimSpace(entity)

		if entity == "" {
			continue
		}

		var slabs map[string]int
		if _, ok := d3ID.Regional[entity]; ok {
			slabs = d3ID.Regional[entity]
		}

		if _, ok := d3ID.Circle[entity]; ok {
			slabs = d3ID.Circle[entity]
			for _, slab := range labels {
				dNational[slab] += slabs[slab]
			}
		}

		if slabs != nil {
			total := 0
			for idx, slab := range labels {
				total += slabs[slab]
				if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+idx).Address(), slabs[slab]); err != nil {
					return err
				}
			}
			if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+len(labels)).Address(), total); err != nil {
				return err
			}
		}

		if entity == "INDONESIA" {
			total := 0
			for idx, slab := range labels {
				total += dNational[slab]
				if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+idx).Address(), dNational[slab]); err != nil {
					return err
				}
			}
			if err := xl.SetCellInt(shname, xlutil.Cell(startRow+i, startCol+len(labels)).Address(), total); err != nil {
				return err
			}
		}
	}

	dateStr := fmt.Sprintf("%s - %s", data.ReportStartDate.Format("02 Jan"), data.ReportEndDate.Format("02 Jan 2006"))
	if err := xl.SetCellValue(shname, "B3", dateStr); err != nil {
		return err
	}

	return xl.SaveAs(xlFile)
}
