package process

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"io"
	"strings"
	"time"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	"github.com/csee-pm/etl/shared/notify"
)

type Attachment struct {
	FileName string
	Content  io.Reader
}

func SendReportEmailMessage(c context.Context, reportKey string, dataDate time.Time, attachments ...Attachment) error {
	conf := ctx.ExtractConfig(c)

	mailConfig, err := cfg.GetConfig[cfg.EmailServer](conf, "email_server")
	if err != nil {
		return fmt.Errorf("failed to get email server config. %s", err)
	}

	receivers, err := GetReportRecipients(c, reportKey)
	if err != nil {
		return fmt.Errorf("failed to get mail recipients. %s", err)
	}

	title, err := getReportTitle(c, reportKey)
	if err != nil {
		return err
	}

	htmlMsg, err := getReportEmailMessage(c, reportKey, dataDate)
	if err != nil {
		return err
	}

	mailer := notify.NewEmailNotifier(mailConfig.Host, mailConfig.Port, mailConfig.User, mailConfig.Pass, mailConfig.SenderAddress, mailConfig.SenderName, receivers...)

	var attachs []notify.FileAttachment
	for _, a := range attachments {
		attachs = append(attachs, notify.FileAttachment{
			FileName: a.FileName,
			Content:  a.Content,
		})
	}

	return mailer.Notify(title, htmlMsg, attachs...)
}

func SendReportEmailWithRecipients(c context.Context, reportKey string, recipients []string, dataDate time.Time, attachments ...Attachment) error {
	conf := ctx.ExtractConfig(c)

	mailConfig, err := cfg.GetConfig[cfg.EmailServer](conf, "email_server")
	if err != nil {
		return fmt.Errorf("failed to get email server config. %s", err)
	}

	title, err := getReportTitle(c, reportKey)
	if err != nil {
		return err
	}

	htmlMsg, err := getReportEmailMessage(c, reportKey, dataDate)
	if err != nil {
		return err
	}

	mailer := notify.NewEmailNotifier(mailConfig.Host, mailConfig.Port, mailConfig.User, mailConfig.Pass, mailConfig.SenderAddress, mailConfig.SenderName, recipients...)

	var attachs []notify.FileAttachment
	for _, a := range attachments {
		attachs = append(attachs, notify.FileAttachment{
			FileName: a.FileName,
			Content:  a.Content,
		})
	}

	return mailer.Notify(title, htmlMsg, attachs...)
}

func GetReportRecipients(c context.Context, reportKey string) ([]string, error) {
	conf := ctx.ExtractConfig(c)

	defaultKey := "report_recipients.default"
	key := fmt.Sprintf("report_recipients.%s", reportKey)

	defaultRecipients := conf.GetStringSlice(defaultKey)
	recipients := conf.GetStringSlice(key)

	return append(defaultRecipients, recipients...), nil
}

func getReportTitle(c context.Context, reportKey string) (string, error) {
	switch strings.ToLower(reportKey) {
	case "baudeck":
		return "BAU Deck Data Update", nil
	default:
		return "", fmt.Errorf("unknown reportKey %q", reportKey)
	}
}

func getReportEmailMessage(c context.Context, reportKey string, dataDate time.Time) (string, error) {
	switch strings.ToLower(reportKey) {
	case "baudeck":
		return createBauDeckEmailMessage(dataDate)
	default:
		return "", fmt.Errorf("unknown reportKey %q", reportKey)

	}
}

func createBauDeckEmailMessage(dataDate time.Time) (string, error) {
	t, err := template.ParseFS(procFS, "files/email_baudeck_data.html")
	if err != nil {
		return "", err
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
		DataDate   string
	}{
		ReportDate: time.Now().Format("2-Jan-2006 15:04:05"),
		DataDate:   dataDate.Format("2-Jan-2006"),
	}); err != nil {
		return "", err
	}

	return buf.String(), nil
}
