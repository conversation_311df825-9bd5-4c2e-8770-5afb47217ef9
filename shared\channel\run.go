package channel

import (
	"context"
	"fmt"
)

var ContextCancelledError = fmt.<PERSON><PERSON><PERSON>("context cancelled")

func RunAsync[T any](f func() (T, error)) <-chan Result[T] {
	resChan := make(chan Result[T])
	go func() {
		defer close(resChan)
		res, err := f()
		if err != nil {
			resChan <- ErrResult[T](err)
			return
		}
		resChan <- OkResult(res)
	}()
	return resChan
}

func RunAsyncE(f func() error) <-chan error {
	resChan := make(chan error)
	go func() {
		defer close(resChan)
		resChan <- f()
	}()
	return resChan
}

func RunAsyncContext[T any](c context.Context, fn func() (T, error)) <-chan Result[T] {
	resChan := make(chan Result[T], 1)
	name, ok := c.Value("name").(string)
	if !ok {
		name = "unnamed function"
	}

	go func() {
		defer close(res<PERSON>han)

		done := make(chan Result[T], 1)
		go func() {
			res, err := fn()
			if err != nil {
				done <- ErrResult[T](err)
				return
			}
			done <- OkResult(res)
		}()

		select {
		case <-c.Done():
			fmt.Printf("%s is cancelled\n", name)
			resChan <- ErrResult[T](c.Err())
			return
		case result := <-done:
			resChan <- result
		}
	}()
	return resChan
}

func RunAsyncContextE(c context.Context, fn func() error) <-chan error {
	resChan := make(chan error, 1)
	name, ok := c.Value("name").(string)
	if !ok {
		name = "unnamed function"
	}

	go func() {
		defer close(resChan)

		done := make(chan error, 1)
		go func() {
			done <- fn()
		}()

		select {
		case <-c.Done():
			fmt.Printf("%s is cancelled\n", name)
			resChan <- c.Err()
			return
		case result := <-done:
			resChan <- result
		}
	}()
	return resChan
}
