package topKpi

import (
	"context"
	"fmt"
	"io/fs"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"

	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type TopKpiBqProcess struct {
	procFS fs.ReadFileFS
}

func NewTopKpiBqProcess(procFS fs.ReadFileFS) TopKpiBqProcess {
	return TopKpiBqProcess{procFS}
}

func (topKpi TopKpiBqProcess) GetReportData(c context.Context, workDate time.Time) (NewTopKpiReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return NewTopKpiReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	data, err := topKpi.getData(c, workDate)
	if err != nil {
		return NewTopKpiReport{}, err
	}

	workDir := ctx.ExtractWorkDir(c)

	csvFilePath := fmt.Sprintf("%s/top_kpi_%s.csv", workDir, time.Now().Format("20060102150405"))
	err = utils.WriteToCsv(csvFilePath, data)
	if err != nil {
		logger.Error("failed to write Top KPI CSV file", "path", csvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", csvFilePath)
	}

	return topKpi.postProcessData(c, data)
}

func (topKpi TopKpiBqProcess) getData(c context.Context, mtdDate time.Time) ([]MtdTopKpiKabuData, error) {
	logger := ctx.ExtractLogger(c)
	var im3Data []MtdTopKpiKabuData
	var threeData []MtdTopKpiKabuData
	var osaData []MtdTopKpiKabuData

	kabumap, err := etlProc.GetMicroMktMap(c)
	if err != nil {
		return nil, err
	}

	g, c := errgroup.WithContext(c)

	g.Go(func() error {
		var errg error
		im3Data, errg = topKpi.getIm3Data(c, mtdDate)
		if errg != nil {
			logger.Error("failed to get TopKPI IM3 data", "error", errg.Error())
			return fmt.Errorf("failed to get TopKPI IM3 data. %s", errg.Error())
		}

		logger.Info("IM3 data fetched", "count", len(im3Data))
		return nil
	})

	g.Go(func() error {
		var errg error
		threeData, errg = topKpi.get3idData(c, mtdDate)
		if errg != nil {
			logger.Error("failed to get TopKPI 3ID data", "error", errg.Error())
			return fmt.Errorf("failed to get TopKPI 3ID data. %s", errg.Error())
		}

		logger.Info("3ID data fetched", "count", len(threeData))
		return nil
	})

	g.Go(func() error {
		var errg error
		osaData, errg = topKpi.getOsa(c, mtdDate)
		if errg != nil {
			logger.Error("failed to get TopKPI OSA data", "error", errg.Error())
			return fmt.Errorf("failed to get TopKPI OSA data. %s", errg.Error())
		}

		logger.Info("OSA data fetched", "count", len(osaData))
		return nil
	})

	if err := g.Wait(); err != nil {
		return nil, err
	}

	kpiData := append(im3Data, threeData...)
	osaMap := make(map[string]float64)

	for _, d := range osaData {
		key := d.Brand + "|" + d.Circle.String + "|" + d.Region.String + "|" + d.Kabupaten.String + "|" + d.Period
		osaMap[key] = d.OSA
	}

	for i := range kpiData {
		kabu := strings.TrimSpace(strings.ToUpper(kpiData[i].Kabupaten.String))
		if segment, ok := kabumap[kabu]; ok {
			kpiData[i].KabuFlag = null.StringFrom(segment)
		}
		key := kpiData[i].Brand + "|" + kpiData[i].Circle.String + "|" + kpiData[i].Region.String + "|" + kpiData[i].Kabupaten.String + "|" + kpiData[i].Period
		kpiData[i].OSA = osaMap[key]
	}

	return kpiData, nil
}

func (topKpi TopKpiBqProcess) getIm3Data(c context.Context, mtdDate time.Time) ([]MtdTopKpiKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/im3_topkpi_bq.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt": {Name: "mtd_dt", Value: mtdDate.Format("2006-01-02")},
	}

	logger.Info("Getting IM3 TopKPI data")
	res, err := etlProc.QueryBigQueryData[MtdTopKpiDataBQ](c, string(buf), etlProc.WithBqParams(params))
	if err != nil {
		return nil, err
	}

	return utils.SliceMap(res, func(d MtdTopKpiDataBQ) MtdTopKpiKabuData {
		return d.ToMtdTopKpiKabuData()
	}), nil
}

func (topKpi TopKpiBqProcess) get3idData(c context.Context, mtdDate time.Time) ([]MtdTopKpiKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/3id_topkpi_bq.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt": {Name: "mtd_dt", Value: mtdDate.Format("2006-01-02")},
	}

	logger.Info("Getting 3ID TopKpi data")
	res, err := etlProc.QueryBigQueryData[MtdTopKpiDataBQ](c, string(buf), etlProc.WithBqParams(params))
	if err != nil {
		return nil, err
	}

	return utils.SliceMap(res, func(d MtdTopKpiDataBQ) MtdTopKpiKabuData {
		return d.ToMtdTopKpiKabuData()
	}), nil
}

func (topKpi TopKpiBqProcess) getOsa(c context.Context, mtdDate time.Time) ([]MtdTopKpiKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := topKpi.procFS.ReadFile("files/osa_kpi_mtd_kabu.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format("20060102")},
	}

	logger.Info("Getting OSA TopKpi data")
	return etlProc.QueryImpalaData[MtdTopKpiKabuData](c, string(buf), params)
}

func (topKpi TopKpiBqProcess) postProcessData(c context.Context, data []MtdTopKpiKabuData) (NewTopKpiReport, error) {
	brandMap := map[string]*TopKpiReportMap{
		"IM3": NewTopKpiReportMap(),
		"3ID": NewTopKpiReportMap(),
	}

	iohData := NewTopKpiReportMap()

	for i := range data {
		d := data[i]
		brand := d.Brand
		flag := strings.ToUpper(d.KabuFlag.String)
		period := strings.ToUpper(d.Period)

		var brandData *TopKpiReportMap

		if _, ok := brandMap[brand]; !ok {
			brandMap[brand] = NewTopKpiReportMap()
		}

		brandData = brandMap[brand]

		if _, ok := brandData.KabuFlagMap[flag]; !ok {
			brandData.KabuFlagMap[flag] = NewRegionalTopKpiData("FLAG", flag)
		}

		if _, ok := iohData.KabuFlagMap[flag]; !ok {
			iohData.KabuFlagMap[flag] = NewRegionalTopKpiData("FLAG", flag)
		}

		var kabData *TopKpi
		var iohKabData *TopKpi
		var natData *TopKpi
		var iohNatData *TopKpi

		if period == "MTD" {
			brandData.KabuFlagMap[flag].AsofDate = d.AsofDate
			kabData = brandData.KabuFlagMap[flag].MTD
			iohData.KabuFlagMap[flag].AsofDate = d.AsofDate
			iohKabData = iohData.KabuFlagMap[flag].MTD
			brandData.NationalMap.AsofDate = d.AsofDate
			natData = brandData.NationalMap.MTD
			iohData.NationalMap.AsofDate = d.AsofDate
			iohNatData = iohData.NationalMap.MTD
		} else {
			kabData = brandData.KabuFlagMap[flag].LMTD
			iohKabData = iohData.KabuFlagMap[flag].LMTD
			natData = brandData.NationalMap.LMTD
			iohNatData = iohData.NationalMap.LMTD
		}

		kabData.RevenueGrossMn += d.RevenueGrossMn
		iohKabData.RevenueGrossMn += d.RevenueGrossMn
		natData.RevenueGrossMn += d.RevenueGrossMn
		iohNatData.RevenueGrossMn += d.RevenueGrossMn

		kabData.DataRevGrossMn += d.DataRevGrossMn
		iohKabData.DataRevGrossMn += d.DataRevGrossMn
		natData.DataRevGrossMn += d.DataRevGrossMn
		iohNatData.DataRevGrossMn += d.DataRevGrossMn

		kabData.TrafficGb += d.TrafficGb
		iohKabData.TrafficGb += d.TrafficGb
		natData.TrafficGb += d.TrafficGb
		iohNatData.TrafficGb += d.TrafficGb

		kabData.Rgu90 += d.Rgu90
		iohKabData.Rgu90 += d.Rgu90
		natData.Rgu90 += d.Rgu90
		iohNatData.Rgu90 += d.Rgu90

		kabData.Rgu30 += d.Rgu30
		iohKabData.Rgu30 += d.Rgu30
		natData.Rgu30 += d.Rgu30
		iohNatData.Rgu30 += d.Rgu30

		kabData.Vlr.Int64 += d.Vlr.Int64
		iohKabData.Vlr.Int64 += d.Vlr.Int64
		natData.Vlr.Int64 += d.Vlr.Int64
		iohNatData.Vlr.Int64 += d.Vlr.Int64

		kabData.GrossAdds += d.GrossAdds
		iohKabData.GrossAdds += d.GrossAdds
		natData.GrossAdds += d.GrossAdds
		iohNatData.GrossAdds += d.GrossAdds

		kabData.PackSubs += d.PackSubs
		iohKabData.PackSubs += d.PackSubs
		natData.PackSubs += d.PackSubs
		iohNatData.PackSubs += d.PackSubs

		kabData.Quro += d.Quro
		iohKabData.Quro += d.Quro
		natData.Quro += d.Quro
		iohNatData.Quro += d.Quro

		kabData.Qsso += d.Qsso
		iohKabData.Qsso += d.Qsso
		natData.Qsso += d.Qsso
		iohNatData.Qsso += d.Qsso

		kabData.OSA += d.OSA
		iohKabData.OSA += d.OSA
		natData.OSA += d.OSA
		iohNatData.OSA += d.OSA

		kabData.DseCount += d.DseCount
		iohKabData.DseCount += d.DseCount
		natData.DseCount += d.DseCount
		iohNatData.DseCount += d.DseCount
	}

	return NewTopKpiReport{
		IM3:   brandMap["IM3"],
		Three: brandMap["3ID"],
		IOH:   iohData,
	}, nil
}

//var (
//	TopKpiCol       = 2
//	TopMtdCol       = 5
//	TopLmtdCol      = 4
//	TopGrowthPctCol = 6
//	TopGrowthAbsCol = 7
//
//	kabuSegments       = []string{"MUST WIN 50", "SUPER 88", "REST MUST WIN", "WINNING"}
//	topKpiKabuColStart = 11
//	topKpiKabuKpiCol   = 9
//)

func (topKpi TopKpiBqProcess) WriteReport(xl *excelize.File, data NewTopKpiReport) error {
	shName := "MTD"
	asof, err := time.Parse("20060102", data.IM3.NationalMap.AsofDate)
	if err != nil {
		asof = time.Now()
	}

	xl.SetCellStr(shName, "C2", asof.Format("02-Jan-2006"))

	if err := topKpi.writeReport(xl, data.IM3, 6, 18); err != nil {
		return err
	}

	if err := topKpi.writeReport(xl, data.Three, 22, 34); err != nil {
		return err
	}

	if err := topKpi.writeReport(xl, data.IOH, 38, 50); err != nil {
		return err
	}

	return nil
}

func (topKpi TopKpiBqProcess) writeReport(xl *excelize.File, data *TopKpiReportMap, startRow, endRow int) error {
	shName := "MTD"

	for i := startRow; i <= endRow; i++ {
		kpi, err := xl.GetCellValue(shName, xlutil.Cell(i, TopKpiCol).Address())
		if err != nil {
			return err
		}

		mtdVal, err := topKpi.getKpiValue(kpi, data.NationalMap.MTD)
		if err != nil {
			return err
		}
		xl.SetCellValue(shName, xlutil.Cell(i, TopMtdCol).Address(), mtdVal)

		lmtdVal, err := topKpi.getKpiValue(kpi, data.NationalMap.LMTD)
		if err != nil {
			return err
		}
		xl.SetCellValue(shName, xlutil.Cell(i, TopLmtdCol).Address(), lmtdVal)

		growthPct := (mtdVal - lmtdVal) / lmtdVal
		xl.SetCellValue(shName, xlutil.Cell(i, TopGrowthPctCol).Address(), growthPct)

		growthAbs := mtdVal - lmtdVal
		xl.SetCellValue(shName, xlutil.Cell(i, TopGrowthAbsCol).Address(), growthAbs)

		for j, seg := range kabuSegments {
			segData, ok := data.KabuFlagMap[seg]
			if !ok {
				continue
			}

			mtdCol := topKpiKabuColStart + ((j * 4) + 1)
			lmtdCol := topKpiKabuColStart + ((j * 4) + 0)
			growthPctCol := topKpiKabuColStart + ((j * 4) + 2)
			growthAbsCol := topKpiKabuColStart + ((j * 4) + 3)

			mtdVal, err := topKpi.getKpiValue(kpi, segData.MTD)
			if err != nil {
				return err
			}
			xl.SetCellValue(shName, xlutil.Cell(i, mtdCol).Address(), mtdVal)

			lmtdVal, err := topKpi.getKpiValue(kpi, segData.LMTD)
			if err != nil {
				return err
			}
			xl.SetCellValue(shName, xlutil.Cell(i, lmtdCol).Address(), lmtdVal)

			growthPct := (mtdVal - lmtdVal) / lmtdVal
			xl.SetCellValue(shName, xlutil.Cell(i, growthPctCol).Address(), growthPct)

			growthAbs := mtdVal - lmtdVal
			xl.SetCellValue(shName, xlutil.Cell(i, growthAbsCol).Address(), growthAbs)
		}
	}
	return nil
}

func (topKpi TopKpiBqProcess) getKpiValue(kpiName string, data *TopKpi) (float64, error) {
	if data == nil {
		return 0, fmt.Errorf("data is nil")
	}

	kpiName = strings.ToLower(kpiName)
	switch kpiName {
	case "rev":
		return data.RevenueGrossMn / 1000, nil
	case "data rev":
		return data.DataRevGrossMn / 1000, nil
	case "traffic":
		return data.TrafficGb / 1000000, nil
	case "arpu":
		return data.RevenueGrossMn * 1_000_000 / float64(data.Rgu90), nil
	case "rgu 90":
		return float64(data.Rgu90) / 1000_000, nil
	case "rgu 30":
		return float64(data.Rgu30) / 1000_000, nil
	case "vlr":
		return float64(data.Vlr.Int64) / 1000_000, nil
	case "gross adds":
		return float64(data.GrossAdds) / 1000_000, nil
	case "pack subs":
		return float64(data.PackSubs / 1000_000), nil
	case "quro":
		return float64(data.Quro) / 1000, nil
	case "qsso":
		return float64(data.Qsso) / 1000, nil
	case "ga/dse":
		val := float64(data.GrossAdds) / float64(data.DseCount)
		return val, nil
	case "osa/dse":
		return float64(data.OSA/1000_000) / float64(data.DseCount), nil
	default:
		return 0, fmt.Errorf("invalid kpi name: %s", kpiName)
	}
}
