with outlet_ref as (
    select
        x.id outlet_id,
        y.circle,
        y.region,
        y.kabkot_nm
    from
        (
            select
                id,
                kec_unik,
                row_number() over (partition by id order by mth desc) rnk
            from
                rdm.spa_tbl_ref_lowlevel_mth
            where
                mth >= from_timestamp(trunc(date_add(to_timestamp(${end_dt_id},'yyyyMMdd'), interval -3 month),'month'),'yyyyMM')
            and mth <= substr(${end_dt_id},1,6)
            and flag = 'OUTLET'
        ) x
        inner join
        biadm.ref_kecamatan y
        on
            x.kec_unik = y.kec_kabkot
    where
        x.rnk = 1
),
pjp_outlets as (
    select distinct
        month month_id,
        id_outlet
    from
        rdm.outlet_cso_mapping a
    where
        month >=from_timestamp(trunc(date_add(to_timestamp(${end_dt_id},'yyyyMMdd'), interval -3 month),'month'),'yyyyMM')
    and month <= substr(${end_dt_id},1,6)
),
outlet_salmo_dly as (
    select
        substr(a.dt_id, 1, 6) month_id,
        a.dt_id,
        cast(substr(a.dt_id, 7, 2) as int) day_int,
        cast((cast(substr(a.dt_id, 7, 2) as int) - 1) / 7 as int) + 1 wk,
        a.organization_id,
        r.circle,
        r.region,
        r.kabkot_nm,
        sum(a.amount) saldo_amount
    from
        rdm.bai_trade_ssd_v1 a
        left join
        outlet_ref r
        on
            a.organization_id = r.outlet_id
    where
        a.dt_id >= from_timestamp(trunc(date_add(to_timestamp(${end_dt_id},'yyyyMMdd'), interval -3 month),'month'),'yyyyMMdd')
    and a.dt_id <= ${end_dt_id}
    and a.type = 'Stock'
    and a.category = 'SALDO MOBO-OUTLET'
    group by 1,2,3,4,5,6,7,8
),
outlet_salmo_avg as (
    select
        a.month_id,
        case when a.wk > 4 then 4 else a.wk end wk,
        a.organization_id,
        a.circle,
        a.region,
        a.kabkot_nm,
        round(avg(a.saldo_amount), 3) saldo_amount,
        ${binIm3Str} slab
    from
        outlet_salmo_dly a
    group by 1,2,3,4,5,6
)
select
    a.month_id,
    a.wk,
    a.circle,
    a.region,
    a.kabkot_nm kabupaten,
    'IM3' brand,
    ${binIm3CntStr}
from
    outlet_salmo_avg a
    inner join
    pjp_outlets p
    on
        p.id_outlet = a.organization_id
    and p.month_id = a.month_id
group by 1,2,3,4,5
order by 1,2,3,4,5