package process

import (
	"math"
	"strconv"
	"strings"
	"time"

	"gopkg.in/guregu/null.v4"
)

type SalmoboData struct {
	DtID           string      `db:"dt_id" dataframe:"dt_id"`
	Brand          string      `db:"brand" dataframe:"brand"`
	OrganizationId string      `db:"organization_id" dataframe:"organization_id"`
	Circle         null.String `db:"circle" dataframe:"circle"`
	Region         null.String `db:"region" dataframe:"region"`
	SaldoAmount    float64     `db:"saldo_amount" dataframe:"saldo_amount"`
}

func (s SalmoboData) GetColumns() []string {
	return []string{"dt_id", "brand", "organization_id", "circle", "region", "saldo_amount"}
}

func (s SalmoboData) GetRowValues() []string {
	return []string{s.DtID, s.Brand, s.OrganizationId, s.Circle.String, s.Region.String, strconv.FormatFloat(s.SaldoAmount, 'f', -1, 64)}
}

type FinalSalmoboData struct {
	Brand            string `db:"brand" dataframe:"brand"`
	OrganizationId   string `db:"organization_id" dataframe:"organization_id"`
	Circle           string `db:"circle" dataframe:"circle"`
	Region           string `db:"region" dataframe:"region"`
	sumSaldoAmount   float64
	countSaldoAmount int
	AvgSaldoAmount   float64  `db:"saldo_amount" dataframe:"saldo_amount"`
	DtIds            []string `db:"dt_ids" dataframe:"dt_ids"`
	Slab             string   `db:"slab" dataframe:"slab"`
}

func (s *FinalSalmoboData) AddSaldoAmount(saldoAmount float64) {
	s.sumSaldoAmount += saldoAmount
	s.countSaldoAmount++
	s.AvgSaldoAmount = math.Round(s.sumSaldoAmount / float64(s.countSaldoAmount))
}

func (s FinalSalmoboData) GetColumns() []string {
	return []string{"brand", "organization_id", "circle", "region", "avg_saldo_amount", "slab", "dates"}
}

func (s FinalSalmoboData) GetRowValues() []string {
	return []string{s.Brand, s.OrganizationId, s.Circle, s.Region, strconv.FormatFloat(s.AvgSaldoAmount, 'f', -1, 64), s.Slab, strings.Join(s.DtIds, ",")}
}

type SalmoboReportData struct {
	Regional map[string]map[string]int
	Circle   map[string]map[string]int
}

type SalmoboReport struct {
	ReportStartDate time.Time
	ReportEndDate   time.Time
	IM3             SalmoboReportData
	Three           SalmoboReportData
}
