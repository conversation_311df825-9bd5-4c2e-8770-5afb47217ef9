with site_ref as (
    select
        site_id,
        branch,
        circle,
        hor,
        kabkot_nm,
        sales_area,
        cnt,
        row_number() over (partition by site_id order by cnt desc) rnk
    from
        (
            select
                site_id,
                gladiator_branch branch,
                sales_area,
                kabkot_nm,
                region_circle hor,
                circle,
                count(*) cnt
            from
                `data-dtptechm-prd-c7ca.ioh_biadm.ref_site_h3i`
            group by 1,2,3,4,5,6
        ) x
)
select
    format_timestamp('%Y%m', dt_id) month_id,
    case when dt_id = ${mtd_dt} then 'MTD' else 'LMTD' end period,
    format_timestamp('%Y%m%d', dt_id) asof_date,
    b.circle,
    b.hor as region,
    c.flag,
    '3ID' as brand,
    count(distinct b.kabkot_nm) kabu_cnt,
    cast(sum(case when kpi_name = 'rgu_ga' then kpi_value else 0 end) as int) ga,
    cast(sum(case when kpi_name = 'm2s_mtd' then kpi_value else 0 end) as int) m2s
from
    `data-dtptechm-prd-c7ca.ioh_biadm.omn_sitewise_smy` a
    left join
    site_ref b
    on
        a.site_id = b.site_id
    and b.rnk = 1
    left join
    `data-commstrexe-prd-565x.csee_pm.nc_kabu_mapping` c
    on
        b.kabkot_nm = c.Kabupaten
where
    kpi_name in ('rgu_ga', 'm2s_mtd')
and dt_id in (timestamp(${mtd_dt}), timestamp(date_add(${mtd_dt}, interval -1 month)))
group by 1,2,3,4,5,6

union all

select
    format_timestamp('%Y%m', dt_id) month_id,
    'FM' period,
    format_timestamp('%Y%m%d', dt_id) asof_date,
    b.circle,
    b.hor as region,
    c.flag,
    '3ID' as brand,
    count(distinct b.kabkot_nm) kabu_cnt,
    cast(sum(case when kpi_name = 'rgu_ga' then kpi_value else 0 end) as int) ga,
    cast(sum(case when kpi_name = 'm2s_mtd' then kpi_value else 0 end) as int) m2s
from
    `data-dtptechm-prd-c7ca.ioh_biadm.omn_sitewise_smy` a
    left join
    site_ref b
    on
        a.site_id = b.site_id
    and b.rnk = 1
    left join
    `data-commstrexe-prd-565x.csee_pm.nc_kabu_mapping` c
    on
        b.kabkot_nm = c.Kabupaten
where
    kpi_name in ('rgu_ga', 'm2s_mtd')
and dt_id >= timestamp(date_add(${mtd_dt}, interval -6 month))
and dt_id < timestamp(date_trunc(${mtd_dt}, month))
and dt_id = timestamp(date_add(date_add(date_trunc(date(a.dt_id), month), interval 1 month), interval -1 day))
group by 1,2,3,4,5,6
