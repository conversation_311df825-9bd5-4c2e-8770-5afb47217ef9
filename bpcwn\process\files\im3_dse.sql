with ga as (
	select
	    a.dt_id,
	    organization_id,
	    sum(net_rev) net_acq_rev,
	    count(distinct a.msisdn) ga
	from
	(
	    select
	        msisdn,
	        actvn_dt,
	        total_rev/1.11 net_rev,
	        site_id,
	        churn_back,
	        recycled,
	        dt_id
		from
		    biadm.umr_rgs_ga_90d_dly_govt
        where
            dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
        and dt_id <= ${mtd_dt_id}
        and (churn_back = 'NO' OR recycled = 'YES')
    ) a
    join
    (
        select
            msisdn,
            organization_id,
            channel_grp,
            channel_alloc,
            cluster_alloc,
            flag_sp,
    	    case
    	        when flag_sp = 'SP OLA' then 'RGUGA-Digital-OLA'
		        when channel_grp = 'TRADITIONAL' then 'RGUGA-Trad-Outlet'
                when channel_grp = 'DSF' then 'RGUGA-Trad-DSF'
                when channel_grp = 'MODERN' and channel like '%ONLINE%' then 'RGUGA-Digital-Online'
                when channel_grp = 'MODERN' then 'RGUGA-Digital-Modern'
		        else
		        case
		            when channel_alloc = 'TRADITIONAL' then 'RGUGA-Trad-Outlet'
		            when channel_alloc = 'DSF' then 'RGUGA-Trad-DSF'
		            else 'RGUGA-Others'
		        end
		    end channel_grp_snd,
    	    ga_dt
	    from
	        biadm.umr_rgs_ga_channel_mth_govt
    	where
    	    ga_dt >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
        and ga_dt <= ${mtd_dt_id}
    ) b
    on
        a.msisdn = b.msisdn
    and a.dt_id = b.ga_dt
    where
        channel_grp_snd like 'RGUGA-Trad%'
    group by 1,2
),
sec as (
    select
        dt_id,
        credit_party_id,
        sum(amount)/1.11 net_amount
    from
        biadm.omn_secondary
    where
        dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
    and dt_id <= ${mtd_dt_id}
    group by 1,2
),
base as (
    select
        coalesce(ga.dt_id, sec.dt_id) dt_id,
        coalesce(ga.organization_id, sec.credit_party_id) organization_id,
        coalesce(ga.ga, 0) ga_dse,
        coalesce(sec.net_amount, 0) net_sec_amt
    from
        ga
        full outer join
        sec
        on
            ga.dt_id = sec.dt_id
        and ga.organization_id = sec.credit_party_id
),
nbs as (
    select
       site_id,
       organization_id,
       org_nm organization_name,
       region,
       circle,
       mth
    from
    (
        select
            site_id,
            organization_id,
            upper(organization_name) org_nm,
            region,
            circle,
            substr(dt_id, 1, 6) mth,
            ROW_NUMBER() OVER (partition by organization_id, substr(dt_id, 1, 6) ORDER BY dt_id desc) AS rn
        from
            biadm.omn_outlet_loc_ns
        where
            dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
        and dt_id <= ${mtd_dt_id}
    ) a
    where rn=1
),
pjp as (
    select
        a.mth,
        id_outlet,
        dse,
        circle,
        region
    from
    (
        select
            `month` mth,
            id_outlet,
            upper(username) dse
        from
            rdm.outlet_cso_mapping
        where
            `month` >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMM')
        and `month` <= substr(${mtd_dt_id}, 1, 6)
    ) a
    left join
    nbs
    on
        a.id_outlet = nbs.organization_id
    and a.mth = nbs.mth
),
circle_ref as
(
    select
        site_id,
        region_circle,
        circle
    from
        biadm.ref_site
    group by 1,2,3
)
select
    pjp.mth month_id,
	case
	    when pjp.mth = substr(${mtd_dt_id}, 1, 6) then 'MTD'
        else 'LMTD'
    end period,
    'IM3' brand,
    circle,
    region,
    pjp.dse,
    max(base.dt_id) asof_date,
    sum(base.ga_dse) ga,
    round(sum(base.net_sec_amt)/1000000, 3) net_secondary_mn,
    cast(substr(${mtd_dt_id}, 7, 2) as int) day_no
from
    pjp
    left join
    base
    on
        pjp.id_outlet = base.organization_id
    and pjp.mth = substr(base.dt_id, 1, 6)
where
    base.dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -1 month), 'month'), 'yyyyMMdd')
and cast(substr(base.dt_id, 7, 2) as int) <= cast(substr(${mtd_dt_id}, 7, 2) as int)
group by 1,2,3,4,5,6

UNION ALL

select
    pjp.mth month_id,
	'FM' period,
    'IM3' brand,
    circle,
    region,
    pjp.dse,
    max(base.dt_id) asof_date,
    sum(base.ga_dse) ga,
    round(sum(base.net_sec_amt)/1000000, 3) net_secondary_mn,
    cast(from_timestamp(date_add(date_add(to_timestamp(concat(pjp.mth, '01'), 'yyyyMMdd'), interval 1 month), interval -1 day), 'dd') as int) day_no
from
    pjp
    left join
    base
    on
        pjp.id_outlet = base.organization_id
    and pjp.mth = substr(base.dt_id, 1, 6)
where
    base.dt_id < from_timestamp(trunc(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), 'month'), 'yyyyMMdd')
group by 1,4,5,6,10