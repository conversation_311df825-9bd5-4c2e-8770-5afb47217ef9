with site_ref as (
    select
		t0.site_id,
		t0.site_nm,
		t0.circle,
		t0.region_circle,
		t0.kabkot_nm,
		t0.kecamatan_nm,
		t0.kecamatan_id,
		t1.rural_urban_kecamatan,
		t0.longitude,
		t0.latitude
	from
	(
		select
			coalesce(a.site_id,b.site_id) site_id,
			coalesce(a.site_nm,b.site_nm) site_nm,
			COALESCE(a.circle, b.circle) circle,
			coalesce(a.region_circle, b.region_circle) region_circle,
			COALESCE(a.kabkot_nm, b.kabkot_nm) kabkot_nm,
			coalesce(a.kecamatan_nm,b.kecamatan_nm) kecamatan_nm,
			concat(coalesce(a.kecamatan_nm,b.kecamatan_nm),'|',coalesce(a.kabkot_nm,b.kabkot_nm)) kecamatan_id,
			coalesce(a.longitude,CAST(b.longitude as double)) longitude,
			coalesce(a.latitude,CAST(b.latitude as DOUBLE)) latitude
		from
			biadm.ref_site a
			full join
			biadm.ref_site_h3i b
			on
				a.site_id = b.site_id
	) t0
	left join
	(
		select
			kec_kabkot,
			kecamatan_nm,
			rural_urban_kecamatan
		from
			biadm.ref_kecamatan
	) t1
	on
		t0.kecamatan_id = t1.kec_kabkot
),
site_kpi_dly as (
    select
		a.dt_id,
		coalesce(r.circle, 'NULL') circle,
		coalesce(r.region_circle, 'NULL') region_circle,
		coalesce(r.kabkot_nm, 'NULL') kabkot_nm,
		case when substr(a.dt_id, 1, 6) = substr(${mtd_dt_id}, 1, 6) then 'MTD' else 'LMTD' end period,
        sum(case when kpi_code = 'acq_ga' then metric else 0 end) acq_ga,
		sum(case when kpi_code = 'acq_qsc_m0' then metric else 0 end) acq_qsc_m0,
		sum(case when kpi_code = 'primary_trad' then metric else 0 end) primary_trad,
		sum(case when kpi_code = 'ret_qsso' then metric else 0 end) ret_qsso,
		sum(case when kpi_code = 'ret_quro' then metric else 0 end) ret_quro,
		sum(case when kpi_code = 'ret_sso_mtd' then metric else 0 end) ret_sso_mtd,
		sum(case when kpi_code = 'ret_uro_mtd' then metric else 0 end) ret_uro_mtd,
		sum(case when kpi_code = 'rgu30_base' then metric else 0 end) rgu30_base,
		sum(case when kpi_code = 'rgu30_churn_back' then metric else 0 end) rgu30_churn_back,
		sum(case when kpi_code = 'rgu30_gross_add' then metric else 0 end) rgu30_gross_add,
		sum(case when kpi_code = 'rgu30_gross_churn' then metric else 0 end) rgu30_gross_churn,
		sum(case when kpi_code = 'rgu30_inflow' then metric else 0 end) rgu30_inflow,
		sum(case when kpi_code = 'rgu90_base' then metric else 0 end) rgu90_base,
		sum(case when kpi_code = 'rgu90_churn_back' then metric else 0 end) rgu90_churn_back,
		sum(case when kpi_code = 'rgu90_gross_add' then metric else 0 end) rgu90_gross_add,
		sum(case when kpi_code = 'rgu90_gross_churn' then metric else 0 end) rgu90_gross_churn,
		sum(case when kpi_code = 'rgu90_inflow' then metric else 0 end) rgu90_inflow,
		sum(case when kpi_code = 'sec_sell_ret_trad' then metric else 0 end) sec_sell_ret_trad,
		sum(case when kpi_code = 'ter_sell_nontrad' then metric else 0 end) ter_sell_nontrad,
		sum(case when kpi_code = 'ter_sell_trad' then metric else 0 end) ter_sell_trad,
		sum(case when kpi_code = 'ter_sell_trad_saldo' then metric else 0 end) ter_sell_trad_saldo,
		sum(case when kpi_code = 'ter_sell_trad_sp' then metric else 0 end) ter_sell_trad_sp,
		sum(case when kpi_code = 'ter_sell_trad_vou' then metric else 0 end) ter_sell_trad_vou,
		sum(case when kpi_code = 'vlr_daily' then metric else 0 end) vlr_daily,
		sum(case when kpi_code = 'rev_data_mobo' then metric else 0 end) rev_data_mobo,
		sum(case when kpi_code = 'rev_data_organic' then metric else 0 end) rev_data_organic,
		sum(case when kpi_code = 'rev_total' then metric else 0 end) total_rev,
		sum(case when kpi_code = 'usg_data_gb' then metric else 0 end) traffic_gb
    from
    	biadm.hg_kpi_site_wise_dly a
        left join
    	site_ref r
        on
            a.site_id = r.site_id
    where
    	substr(a.dt_id, 1, 6) >= from_timestamp(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -1 month), 'yyyyMM')
    and substr(a.dt_id, 1, 6) <= substr(${mtd_dt_id}, 1, 6)
    and cast(substr(a.dt_id, 7, 2) as int) <= cast(substr(${mtd_dt_id}, 7, 2) as int)
    and a.brand = 'IM3'
    group by 1, 2, 3, 4, 5
),
site_kpi_max_date as (
    select
        substr(a.dt_id, 1, 6) month_id,
        max(a.dt_id) last_date
    from
        site_kpi_dly a
    group by 1
),
site_kpi_mth_sum as (
    select
        substr(a.dt_id, 1, 6) month_id,
        a.period,
        a.circle,
        a.region_circle,
        a.kabkot_nm,
        sum(a.acq_qsc_m0) acq_qsc_m0,
        sum(a.primary_trad) primary_trad,
        sum(a.sec_sell_ret_trad) sec_sell_ret_trade,
        sum(a.ter_sell_nontrad) ter_sell_nontrad,
        sum(a.ter_sell_trad) ter_sell_trad,
        sum(a.ter_sell_trad_sp) ter_sell_trad_sp,
        sum(a.ter_sell_trad_vou) ter_sell_trad_vou,
        sum(a.rev_data_mobo) rev_data_mobo,
        sum(a.rev_data_organic) rev_data_organic,
        sum(a.total_rev) total_rev,
        sum(a.traffic_gb) traffic_gb
    from
        site_kpi_dly a
    group by 1, 2, 3, 4, 5
),
site_kpi_mth_snap as (
    select
        substr(a.dt_id, 1, 6) month_id,
        a.period,
        a.circle,
        a.region_circle,
        a.kabkot_nm,
        a.dt_id,
        a.acq_ga,
        a.acq_qsc_m0,
        a.ret_qsso,
        a.ret_quro,
        a.ret_sso_mtd,
        a.ret_uro_mtd,
        a.rgu30_base,
        a.rgu30_churn_back,
        a.rgu30_gross_add,
        a.rgu30_gross_churn,
        a.rgu30_inflow,
        a.rgu90_base,
        a.rgu90_churn_back,
        a.rgu90_gross_add,
        a.rgu90_gross_churn,
        a.rgu90_inflow,
        a.vlr_daily
    from
        site_kpi_dly a
        inner join
        site_kpi_max_date m
        on
            a.dt_id = m.last_date
),
packsub as (
    with packsub_daily as (
        select
            period_data,
            coalesce(r.circle, 'NULL') circle,
            coalesce(r.region_circle, 'NULL') region_circle,
            coalesce(r.kabkot_nm, 'NULL') kabkot_nm,
            'IM3' subs_brand,
            sum(case when a.flag_rgu_30 = 1 then 1 else 0 end) subs_rgu30d,
            sum(case when flag_vsd = 'DATA PACK' then 1 else 0 end) subs_datapack,
            sum(case when a.datavol > 0 then 1 else 0 end) data_uu
        from
            dm.pras_daily_packsubs_mtd_v2 a
            left join
            site_ref r
            on
                a.site_id_rgu30d = r.site_id
        where
            substr(period_data, 1, 6) >= from_timestamp(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -1 month), 'yyyyMM')
        and substr(period_data, 1, 6) <= substr(${mtd_dt_id}, 1, 6)
        and cast(substr(a.period_data, 7, 2) as int) <= cast(substr(${mtd_dt_id}, 7, 2) as int)
        group by 1, 2, 3, 4
    ),
    packsub_dly_rnk as (
        select
            substr(period_data, 1, 6) month_id,
            circle,
            region_circle,
            kabkot_nm,
            subs_brand,
            subs_rgu30d,
            subs_datapack,
            data_uu,
            row_number() over (partition by substr(period_data, 1, 6), region_circle, kabkot_nm order by period_data desc) rnk
        from
            packsub_daily
    )
    select
        a.month_id,
        a.circle,
        a.region_circle,
        a.kabkot_nm,
        a.subs_brand,
        a.subs_rgu30d,
        a.subs_datapack,
        a.data_uu
    from
        packsub_dly_rnk a
    where
        rnk = 1
),
nbs as (
   select site_id,organization_id, org_nm organization_name,region,circle, mth  from
   (select site_id,organization_id, upper(organization_name) org_nm,region,circle,left(dt_id,6)mth,
        ROW_NUMBER() OVER (partition by organization_id, left(dt_id,6) ORDER BY dt_id desc) AS rn
     from biadm.omn_outlet_loc_ns
    where dt_id between from_timestamp(trunc(months_sub(to_timestamp(${mtd_dt_id},'yyyyMMdd'),1),'month'),'yyyyMMdd')  and ${mtd_dt_id}
    ) a where rn=1
),
dse as (
    select
        a.month month_id,
        case when a.month = substr(${mtd_dt_id}, 1, 6) then 'MTD' else 'LMTD' end period,
        b.circle,
        b.region,
        r.kabkot_nm,
        count(distinct a.username) dse_cnt
    from
        rdm.outlet_cso_mapping a
        left join
        nbs b
        on
            a.month = b.mth
        and a.id_outlet = b.organization_id
        left join
        site_ref r
        on
            b.site_id = r.site_id
    where
        a.month between from_timestamp(trunc(months_sub(to_timestamp(${mtd_dt_id},'yyyyMMdd'), 1),'month'),'yyyyMM') and left(${mtd_dt_id}, 6)
    group by
        1,2,3,4,5
),
kab_kpi as (
    select
        a.month_id,
        a.period,
        a.circle,
        a.region_circle region,
        a.kabkot_nm,
        'IM3' brand,
        max(b.dt_id) asof_date,
        round(sum(a.total_rev) / 1000000, 3) total_rev_gross_mn,
        round(sum(a.traffic_gb), 3) traffic_gb,
        sum(b.rgu30_base + b.rgu30_inflow) rgs30d,
        sum(b.rgu90_base + b.rgu90_inflow) rgs90d,
        sum(b.acq_ga) rgu_ga,
        sum(b.ret_sso_mtd) sso,
        sum(b.ret_qsso) qsso,
        sum(b.ret_uro_mtd) uro,
        sum(b.ret_quro) quro,
        sum(c.data_uu) data_uu30d,
        sum(b.vlr_daily) vlr_daily,
        sum(c.subs_datapack) packsub
    from
        site_kpi_mth_sum a
        left join
        site_kpi_mth_snap b
        on
            a.month_id = b.month_id
        and a.circle = b.circle
        and a.region_circle = b.region_circle
        and a.kabkot_nm = b.kabkot_nm
        left join
        packsub c
        on
            a.month_id = c.month_id
        and a.circle = c.circle
        and a.region_circle = c.region_circle
        and a.kabkot_nm = c.kabkot_nm
    group by 1, 2, 3, 4, 5
)
select
    a.month_id,
    a.period,
    m.last_date asof_date,
    a.circle,
    a.region,
    a.kabkot_nm kabupaten,
    a.brand,
    a.total_rev_gross_mn,
    a.traffic_gb,
    cast(coalesce(a.rgs30d, 0) as int) rgs30d,
    cast(coalesce(a.rgs90d, 0) as int) rgs90d,
    cast(coalesce(a.rgu_ga, 0) as int) rgu_ga,
    cast(coalesce(a.qsso, 0) as int) qsso,
    cast(coalesce(a.quro, 0) as int) quro,
    cast(coalesce(a.vlr_daily, 0) as int) vlr_daily,
    cast(coalesce(a.packsub, 0) as int) packsub,
    cast(coalesce(b.dse_cnt, 0) as int) dse_cnt
from
    kab_kpi a
    left join
    site_kpi_max_date m
    on
        a.month_id = m.month_id
    left join
    dse b
    on
        a.month_id = b.month_id
    and a.circle = b.circle
    and a.region = b.region
    and a.kabkot_nm = b.kabkot_nm
