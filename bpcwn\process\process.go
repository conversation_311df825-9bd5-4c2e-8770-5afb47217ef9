package process

import (
	"bytes"
	"context"
	"embed"
	"fmt"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/spf13/viper"

	gam2s "github.com/csee-pm/etl/bpcwn/process/ga-m2s"
	"github.com/csee-pm/etl/bpcwn/process/salmobo"

	"github.com/csee-pm/etl/bpcwn/process/distrib"
	"github.com/csee-pm/etl/bpcwn/process/pst"
	pst_kabu "github.com/csee-pm/etl/bpcwn/process/pst-kabu"
	"github.com/csee-pm/etl/bpcwn/process/sdpRpt"
	"github.com/csee-pm/etl/bpcwn/process/topKpi"

	cfg "github.com/csee-pm/etl/bpcwn/config"
	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	prc "github.com/csee-pm/etl/shared/process"
	"github.com/xuri/excelize/v2"
)

//go:embed all:files
var procFS embed.FS

func cloneViper(v *viper.Viper) *viper.Viper {
	newV := viper.New()
	err := newV.MergeConfigMap(v.AllSettings())
	if err != nil {
		panic(err)
	}
	return newV
}

func RunAll(c context.Context, options ...prc.ProcessOption) error {
	cCancel, cancel := context.WithCancel(c)
	defer cancel()
	logger := ctx.ExtractLogger(c)
	conf := ctx.ExtractConfig(c)

	pstProc := channel.RunAsyncE(func() error {
		logger.Info("running PST")
		newC := context.WithValue(c, ctx.ContextKeyConfig, cloneViper(conf))
		return RunCombinedPST(newC)
	})

	distribProc := channel.RunAsyncE(func() error {
		logger.Info("running Distrib")
		newC := context.WithValue(c, ctx.ContextKeyConfig, cloneViper(conf))
		return RunDistrib(newC)
	})

	topKpiProc := channel.RunAsyncE(func() error {
		logger.Info("running Top KPI")
		newC := context.WithValue(c, ctx.ContextKeyConfig, cloneViper(conf))
		return RunNewTopKpi(newC)
	})

	sdpProc := channel.RunAsyncE(func() error {
		logger.Info("running SDP")
		newC := context.WithValue(c, ctx.ContextKeyConfig, cloneViper(conf))
		return RunSDP(newC)
	})

	salmoProc := channel.RunAsyncE(func() error {
		logger.Info("running Salmobo")
		newC := context.WithValue(c, ctx.ContextKeyConfig, cloneViper(conf))
		return RunSalmobo(newC)
	})

	//gaM2sProc := channel.RunAsyncContextE(cCancel, func() error {
	//	logger.Info("running GA-M2S")
	//	return RunGaM2s(c)
	//})

	//errs := channel.MergeChan(cCancel, pstProc, distribProc, topKpiProc, sdpProc, salmoProc, gaM2sProc)
	errs := channel.MergeChan(cCancel, pstProc, distribProc, topKpiProc, sdpProc, salmoProc)
	for err := range errs {
		if err != nil {
			logger.Error("a process has failed", "error", err)
		}
	}
	return nil
}

func RunCombinedPST(c context.Context, options ...prc.ProcessOption) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	pstProc := pst.NewPstProcess(procFS)
	pstKabuProc := pst_kabu.NewPstKabuProcess(procFS)

	var pstReport pst.PSTReport
	var pstKabuReport pst_kabu.PstKabuReport

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var err error

	//var wg sync.WaitGroup
	//wg.Add(1)
	g := new(errgroup.Group)

	pstResult := channel.RunAsyncContext(cCancel, func() (pst.PSTReport, error) {
		return pstProc.GetReportData(c, opt.WorkDate)
	})

	g.Go(func() error {
		var errg error
		for res := range pstResult {
			res.Map(func(d pst.PSTReport) {
				pstReport = d
			}).MapErr(func(er error) {
				errg = er
				//logger.Debug("calling cancel", "caller", "pstProc.GetReportData")
				cancel()
			})
		}
		return errg
	})

	//go func() {
	//	defer wg.Done()
	//	for res := range pstResult {
	//		res.Map(func(d pst.PSTReport) {
	//			pstReport = d
	//		}).MapErr(func(er error) {
	//			err = er
	//			logger.Debug("failed to get PST data", "caller", "pstProc.GetReportData", "error", er)
	//			cancel()
	//		})
	//	}
	//}()
	//
	//wg.Add(1)

	pstKabuResult := channel.RunAsyncContext(cCancel, func() (pst_kabu.PstKabuReport, error) {
		return pstKabuProc.GetReportData(c, opt.WorkDate)
	})

	g.Go(func() error {
		var errg error
		for res := range pstKabuResult {
			res.Map(func(d pst_kabu.PstKabuReport) {
				pstKabuReport = d
			}).MapErr(func(er error) {
				errg = er
				//logger.Debug("calling cancel", "caller", "pstKabuProc.GetReportData")
				cancel()
			})
		}
		return errg
	})

	//go func() {
	//	defer wg.Done()
	//	for res := range pstKabuResult {
	//		res.Map(func(d pst_kabu.PstKabuReport) {
	//			pstKabuReport = d
	//		}).MapErr(func(er error) {
	//			err = er
	//			logger.Debug("calling cancel", "caller", "pstKabuProc.GetReportData")
	//			cancel()
	//		})
	//	}
	//}()
	//
	//wg.Wait()
	//if err != nil {
	//	return err
	//}

	if err := g.Wait(); err != nil {
		return err
	}

	pstReportFile := fmt.Sprintf("%s/PST_Report_%s.xlsx", conf.GetString("work_dir"), time.Now().Format("200601021504"))
	fpst, err := procFS.Open("files/PST_Report.xlsx")
	if err != nil {
		return err
	}
	defer fpst.Close()

	xlPst, err := excelize.OpenReader(fpst)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}
	defer xlPst.Close()

	if err := pstProc.WriteReport(xlPst, &pstReport); err != nil {
		return fmt.Errorf("failed to write PST report. %s", err)
	}

	if err := pstKabuProc.WriteReport(xlPst, pstKabuReport); err != nil {
		return fmt.Errorf("failed to write PST Kabu report. %s", err)
	}

	if err := xlPst.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update PST linked values. %s", err)
	}

	if err := xlPst.SaveAs(pstReportFile); err != nil {
		return fmt.Errorf("failed to save PST report. %s", err)
	}

	buf, err := xlPst.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write PST report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "pst")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !cfg.UseNoMailer && opt.SendEmail {
		if err := SendReportEmailWithRecipients(c, "pst", recipients, Attachment{FileName: pstReportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			logger.Error("failed to send PST report email", "error", err)
		}
	}

	return nil
}

func RunPstKabu(c context.Context, options ...prc.ProcessOption) error {
	logger := ctx.ExtractLogger(c)
	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	pstKabuProc := pst_kabu.NewPstKabuProcess(procFS)

	data, err := pstKabuProc.GetReportData(c, opt.WorkDate)
	if err != nil {
		logger.Error("failed to run PST Kabu", "error", err)
		return err
	}

	workDir := ctx.ExtractWorkDir(c)

	pstKabuReportFile := fmt.Sprintf("%s/PST_Kabu_Report_%s.xlsx", workDir, time.Now().Format("200601021504"))
	fpstkabu, err := procFS.Open("files/PST_Kabu_Report.xlsx")
	if err != nil {
		return err
	}
	defer fpstkabu.Close()

	xlPstKabu, err := excelize.OpenReader(fpstkabu)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}
	defer xlPstKabu.Close()

	if err := pstKabuProc.WriteReport(xlPstKabu, data); err != nil {
		return fmt.Errorf("failed to write PST Kabu report. %s", err)
	}

	if err := xlPstKabu.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update PST Kabu linked values. %s", err)
	}

	if err := xlPstKabu.SaveAs(pstKabuReportFile); err != nil {
		return fmt.Errorf("failed to save PST Kabu report. %s", err)
	}

	buf, err := xlPstKabu.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write PST Kabu report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "pst-kabu")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !cfg.UseNoMailer && opt.SendEmail {
		if err := SendReportEmailWithRecipients(c, "pst-kabu", recipients, Attachment{FileName: pstKabuReportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			logger.Error("failed to send PST Kabu report email", "error", err)
		}
	}

	return nil
}

func RunDistrib(c context.Context, options ...prc.ProcessOption) error {
	logger := ctx.ExtractLogger(c)

	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	dst := distrib.NewDistribProcess(procFS)

	data, err := dst.RunDistrib(c, opt.WorkDate)
	if err != nil {
		logger.Error("failed to run Distrib", "error", err)
		return err
	}

	workDir := ctx.ExtractWorkDir(c)

	distribReportFile := fmt.Sprintf("%s/Distribution_Tracker_%s.xlsx", workDir, time.Now().Format("200601021504"))
	fdistrib, err := procFS.Open("files/Distribution_Tracker.xlsx")
	if err != nil {
		return err
	}
	defer fdistrib.Close()

	xlDistrib, err := excelize.OpenReader(fdistrib)
	if err != nil {
		return fmt.Errorf("failed to open distrib template excel file. %s", err)
	}
	defer xlDistrib.Close()

	if err := dst.WriteMtdReport(c, xlDistrib, data); err != nil {
		return fmt.Errorf("failed to write mtd distrib report. %s", err)
	}

	if err := dst.WriteFmReport(c, xlDistrib, data); err != nil {
		return fmt.Errorf("failed to write fm distrib report. %s", err)
	}

	if err := xlDistrib.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update distrib linked values. %s", err)
	}

	if err := xlDistrib.SaveAs(distribReportFile); err != nil {
		return fmt.Errorf("failed to save distrib report. %s", err)
	}

	buf, err := xlDistrib.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write distrib report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "distrib")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !cfg.UseNoMailer && opt.SendEmail {
		if err := SendReportEmailWithRecipients(c, "distrib", recipients, Attachment{FileName: distribReportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			logger.Error("failed to send distrib report email", "error", err)
		}
	}

	return nil
}

func RunTopKpi(c context.Context, options ...prc.ProcessOption) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	topKpiProc := topKpi.NewTopKpiProcess(procFS)

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var data topKpi.TopKpiReport
	var kabuData topKpi.TopKpiKabuReport
	var err error

	//var wg sync.WaitGroup
	//
	//wg.Add(1)
	g := new(errgroup.Group)

	dataResult := channel.RunAsyncContext(cCancel, func() (topKpi.TopKpiReport, error) {
		return topKpiProc.GetReportData(c, opt.WorkDate)
	})

	g.Go(func() error {
		var errg error
		for res := range dataResult {
			res.Map(func(d topKpi.TopKpiReport) {
				data = d
			}).MapErr(func(er error) {
				errg = er
				cancel()
			})
		}
		return errg
	})

	//go func() {
	//	defer wg.Done()
	//	for res := range dataResult {
	//		res.Map(func(d topKpi.TopKpiReport) {
	//			data = d
	//		}).MapErr(func(er error) {
	//			err = er
	//			logger.Debug("calling cancel", "caller", "topKpiProc.GetReportData")
	//			cancel()
	//		})
	//	}
	//}()

	//wg.Add(1)
	kabuResult := channel.RunAsyncContext(cCancel, func() (topKpi.TopKpiKabuReport, error) {
		return topKpiProc.GetKabuReportData(c, opt.WorkDate)
	})

	g.Go(func() error {
		var errg error
		for res := range kabuResult {
			res.Map(func(d topKpi.TopKpiKabuReport) {
				kabuData = d
			}).MapErr(func(er error) {
				errg = er
				cancel()
			})
		}
		return errg
	})

	//go func() {
	//	defer wg.Done()
	//	for res := range kabuResult {
	//		res.Map(func(d topKpi.TopKpiKabuReport) {
	//			kabuData = d
	//		}).MapErr(func(er error) {
	//			err = er
	//			logger.Debug("calling cancel", "caller", "topKpiProc.GetKabuReportData")
	//			cancel()
	//		})
	//	}
	//}()

	//wg.Wait()
	//
	//if err != nil {
	//	return err
	//}

	if err := g.Wait(); err != nil {
		return err
	}

	topKpiReportFile := fmt.Sprintf("%s/TOP_KPI_Tracking_%s.xlsx", conf.GetString("work_dir"), time.Now().Format("200601021504"))
	ftopKpi, err := procFS.Open("files/TOP_KPI_Tracking.xlsx")
	if err != nil {
		return err
	}
	defer ftopKpi.Close()

	xlTopKpi, err := excelize.OpenReader(ftopKpi)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}
	defer xlTopKpi.Close()

	if err := topKpiProc.WriteReport(xlTopKpi, data); err != nil {
		return fmt.Errorf("failed to write Top KPI report. %s", err)
	}

	if err := topKpiProc.WriteKabuReport(xlTopKpi, kabuData); err != nil {
		return fmt.Errorf("failed to write Top KPI Kabu report. %s", err)
	}

	if err := xlTopKpi.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update Top KPI linked values. %s", err)
	}

	if err := xlTopKpi.SaveAs(topKpiReportFile); err != nil {
		return fmt.Errorf("failed to save Top KPI report. %s", err)
	}

	buf, err := xlTopKpi.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write Top KPI report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "topkpi")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !cfg.UseNoMailer && opt.SendEmail {
		if err := SendReportEmailWithRecipients(c, "topkpi", recipients, Attachment{FileName: topKpiReportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			logger.Error("failed to send Top KPI report email", "error", err)
		}
	}

	return nil
}

func RunNewTopKpi(c context.Context, options ...prc.ProcessOption) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	topKpiProc := topKpi.NewTopKpiBqProcess(procFS)

	data, err := topKpiProc.GetReportData(c, opt.WorkDate)
	if err != nil {
		return err
	}

	topKpiReportFile := fmt.Sprintf("%s/TOP_KPI_Tracking_%s.xlsx", conf.GetString("work_dir"), time.Now().Format("200601021504"))
	ftopKpi, err := procFS.Open("files/NEW_TOP_KPI_Tracking.xlsx")
	if err != nil {
		return err
	}
	defer ftopKpi.Close()

	xlTopKpi, err := excelize.OpenReader(ftopKpi)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}
	defer xlTopKpi.Close()

	if err := topKpiProc.WriteReport(xlTopKpi, data); err != nil {
		return fmt.Errorf("failed to write Top KPI report. %s", err)
	}

	if err := xlTopKpi.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update Top KPI linked values. %s", err)
	}

	if err := xlTopKpi.SaveAs(topKpiReportFile); err != nil {
		return fmt.Errorf("failed to save Top KPI report. %s", err)
	}

	buf, err := xlTopKpi.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write Top KPI report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "topkpi")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !cfg.UseNoMailer && opt.SendEmail {
		if err := SendReportEmailWithRecipients(c, "topkpi", recipients, Attachment{FileName: topKpiReportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			logger.Error("failed to send Top KPI report email", "error", err)
		}
	}

	return nil
}

func RunSDP(c context.Context, options ...prc.ProcessOption) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	sdpProc := sdpRpt.NewSdpProcess(procFS)

	data, err := sdpProc.GetReportData(c, opt.WorkDate)
	if err != nil {
		logger.Error("failed to run SDP", "error", err)
		return err
	}

	sdpReportFile := fmt.Sprintf("%s/SDP_DSE_Report_%s.xlsx", conf.GetString("work_dir"), time.Now().Format("200601021504"))
	fsdp, err := procFS.Open("files/SDP_DSE_Report.xlsx")
	if err != nil {
		return err
	}
	defer fsdp.Close()

	xlSdp, err := excelize.OpenReader(fsdp)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}
	defer xlSdp.Close()

	if err := sdpProc.WriteSdpReport(xlSdp, data.Sdp); err != nil {
		return fmt.Errorf("failed to write SDP report. %s", err)
	}

	if err := sdpProc.WriteDseReport(xlSdp, data.Dse); err != nil {
		return fmt.Errorf("failed to write DSE report. %s", err)
	}

	if err := xlSdp.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update SDP linked values. %s", err)
	}

	if err := xlSdp.SaveAs(sdpReportFile); err != nil {
		return fmt.Errorf("failed to save SDP report. %s", err)
	}

	buf, err := xlSdp.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write SDP report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "sdp")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !cfg.UseNoMailer && opt.SendEmail {
		attachments := []Attachment{
			{FileName: sdpReportFile, Content: bytes.NewReader(buf.Bytes())},
			{FileName: "SDP_DSE_RawData.zip", Content: bytes.NewReader(data.RawDataZipContent)},
		}

		if err := SendReportEmailWithRecipients(c, "sdp", recipients, attachments...); err != nil {
			logger.Error("failed to send SDP report email", "error", err)
		}
	}

	return nil
}

func RunSalmobo(c context.Context, options ...prc.ProcessOption) error {
	logger := ctx.ExtractLogger(c)

	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	salProc := salmobo.NewSalmoboProcess(procFS)

	//im3qry, err := salProc.CreateIm3SalmoboQuery()
	//if err != nil {
	//	return err
	//}
	//
	//threeqry, err := salProc.Create3idSalmoboQuery()
	//if err != nil {
	//	return err
	//}

	//fmt.Printf("IM3_Query:\n%s\n\n", im3qry)
	//fmt.Printf("3ID_Query:\n%s\n\n", threeqry)

	data, err := salProc.GetReportData(c, opt.WorkDate)
	if err != nil {
		logger.Error("failed to run Salmobo", "error", err)
		return err
	}

	workDir := ctx.ExtractWorkDir(c)

	salmoboReportFile := fmt.Sprintf("%s/OUTLET_SALMOBO_%s.xlsx", workDir, time.Now().Format("200601021504"))
	fsalmobo, err := procFS.Open("files/OUTLET_SALMOBO_KABU.xlsx")
	if err != nil {
		return err
	}
	defer fsalmobo.Close()

	xlSalmobo, err := excelize.OpenReader(fsalmobo)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}
	defer xlSalmobo.Close()

	if err := salProc.WriteSalmoReport(c, xlSalmobo, data); err != nil {
		return fmt.Errorf("failed to write Salmobo report. %s", err)
	}

	if err := xlSalmobo.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update Salmobo linked values. %s", err)
	}

	if err := xlSalmobo.SaveAs(salmoboReportFile); err != nil {
		return fmt.Errorf("failed to save Salmobo report. %s", err)
	}

	buf, err := xlSalmobo.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write Salmobo report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "salmobo")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !cfg.UseNoMailer && opt.SendEmail {
		if err := SendReportEmailWithRecipients(c, "salmobo", recipients, Attachment{FileName: salmoboReportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			logger.Error("failed to send Salmobo report email", "error", err)
		}
	}

	return nil
}

func RunGaM2s(c context.Context, options ...prc.ProcessOption) error {
	logger := ctx.ExtractLogger(c)

	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	gaM2sProc := gam2s.NewGaM2sProcess(procFS)

	data, err := gaM2sProc.GetReportData(c, opt.WorkDate)
	if err != nil {
		logger.Error("failed to run GaM2s", "error", err)
		return err
	}

	workDir := ctx.ExtractWorkDir(c)

	gaM2sReportFile := fmt.Sprintf("%s/GA_M2S_Report_%s.xlsx", workDir, time.Now().Format("200601021504"))
	fgaM2s, err := procFS.Open("files/GA_M2S_Report.xlsx")
	if err != nil {
		return err
	}
	defer fgaM2s.Close()

	xlGaM2s, err := excelize.OpenReader(fgaM2s)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}
	defer xlGaM2s.Close()

	if err := gaM2sProc.WriteReport(xlGaM2s, data); err != nil {
		return fmt.Errorf("failed to write GaM2s report. %s", err)
	}

	if err := xlGaM2s.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update GaM2s linked values. %s", err)
	}

	if err := xlGaM2s.SaveAs(gaM2sReportFile); err != nil {
		return fmt.Errorf("failed to save GaM2s report. %s", err)
	}

	buf, err := xlGaM2s.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write GaM2s report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "ga-m2s")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !cfg.UseNoMailer && opt.SendEmail {
		if err := SendReportEmailWithRecipients(c, "ga-m2s", recipients, Attachment{FileName: gaM2sReportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			logger.Error("failed to send GA-M2S report email", "error", err)
		}
	}

	return nil
}
