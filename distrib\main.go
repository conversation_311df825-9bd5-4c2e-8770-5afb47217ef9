package main

import (
	"os"

	cfg "github.com/csee-pm/etl/distrib/config"
	"github.com/csee-pm/etl/distrib/process"
	"github.com/csee-pm/etl/shared/cmd"
)

func main() {
	root := cmd.InitCommonRootCommand(
		"distrib",
		cfg.SetNewConfig,
		cmd.WithRootProcess("distrib", process.RunETL),
		cmd.WithDescription("ETL job for getting distribution report"),
	)

	root.Flags().StringVar(&cfg.UseDistribFromFile, "distrib-from-file", "", "Use Distrib data from file instead of querying from database")
	root.Flags().BoolVar(&cfg.UseNoMailer, "no-mailer", false, "Do not send email")
	root.Flags().BoolVar(&cfg.UseNotInteractive, "not-interactive", false, "Do not prompt for configuration")

	err := root.Execute()
	if err != nil {
		os.Exit(1)
	}
}
