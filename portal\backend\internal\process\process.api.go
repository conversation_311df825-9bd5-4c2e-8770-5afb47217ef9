package process

import (
	"bytes"
	"context"
	"fmt"
	bpcwnPrc "github.com/csee-pm/etl/bpcwn/process"
	"github.com/csee-pm/etl/portal/backend/pkg/dto"
	"github.com/csee-pm/etl/register"
	prc "github.com/csee-pm/etl/shared/process"
	utype "github.com/likearthian/types"
)

var processMap = map[string]*ProcessItem{
	"pst":     WithEtlProcess("pst", bpcwnPrc.RunCombinedPST),
	"distrib": WithEtlProcess("distrib", bpcwnPrc.RunDistrib),
	"topkpi":  WithEtlProcess("topkpi", bpcwnPrc.RunTopKpi),
	"sdp":     WithEtlProcess("sdp", bpcwnPrc.RunSDP),
	"salmobo": WithEtlProcess("salmobo", bpcwnPrc.RunSalmobo),
	"ga-m2s":  WithEtlProcess("ga-m2s", bpcwnPrc.RunGaM2s),
}

var logBuffers = map[string]*bytes.Buffer{
	"pst":     new(bytes.Buffer),
	"distrib": new(bytes.Buffer),
	"topkpi":  new(bytes.Buffer),
	"sdp":     new(bytes.Buffer),
	"salmobo": new(bytes.Buffer),
	"ga-m2s":  new(bytes.Buffer),
}

type ProcessAPI struct {
	c        context.Context
	register register.JobService
}

func NewProcessAPI() *ProcessAPI {
	return &ProcessAPI{c: context.Background()}
}

func (pa *ProcessAPI) SetContext(c context.Context) {
	pa.c = c
}

func (pa *ProcessAPI) RunProcess(id string, req dto.RunProcessRequestDTO) (int64, error) {
	pi, ok := processMap[id]
	if !ok {
		return 0, fmt.Errorf("process %s not found", id)
	}

	if pa.register == nil {
		return 0, fmt.Errorf("register not set")
	}

	runningJobs, err := pa.register.GetJob(pa.c, register.GetJobRequestDTO{EtlID: []string{id}, IsRunning: utype.BooleanFrom(true)})
	if err != nil {
		return 0, err
	}

	if len(runningJobs) > 0 {
		return 0, fmt.Errorf("etl job %s is already running", id)
	}

	logBuffer := logBuffers[id]
	if logBuffer == nil {
		logBuffer = new(bytes.Buffer)
		logBuffers[id] = logBuffer
	}

	logBuffer.Reset()
	options = append(options, prc.WithLogBuffer(logBuffer))

	return 0, pi.Execute(pa.c, options...)
}
