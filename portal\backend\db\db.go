package db

import (
	"database/sql"
	"fmt"
	"time"

	_ "modernc.org/sqlite"
)

// DB represents the database connection
type DB struct {
	*sql.DB
}

// Process represents an ETL process
type Process struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	Status    string    `json:"status"` // "pending", "running", "completed", "failed"
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time,omitempty"`
	Command   string    `json:"command"`
	Args      string    `json:"args"`
	Output    string    `json:"output,omitempty"`
	ExitCode  int       `json:"exit_code"`
}

// File represents a file generated by an ETL process
type File struct {
	ID        int64     `json:"id"`
	ProcessID int64     `json:"process_id"`
	Path      string    `json:"path"`
	Type      string    `json:"type"` // "csv", "xlsx", etc.
	CreatedAt time.Time `json:"created_at"`
	Size      int64     `json:"size"`
}

// Initialize initializes the database
func Initialize(dbPath string) (*DB, error) {
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Create tables if they don't exist
	if err := createTables(db); err != nil {
		return nil, fmt.Errorf("failed to create tables: %w", err)
	}

	return &DB{db}, nil
}

// createTables creates the necessary tables if they don't exist
func createTables(db *sql.DB) error {
	// Create processes table
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS processes (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL,
			status TEXT NOT NULL,
			start_time TIMESTAMP NOT NULL,
			end_time TIMESTAMP,
			command TEXT NOT NULL,
			args TEXT,
			output TEXT,
			exit_code INTEGER
		)
	`)
	if err != nil {
		return err
	}

	// Create files table
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS files (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			process_id INTEGER,
			path TEXT NOT NULL,
			type TEXT NOT NULL,
			created_at TIMESTAMP NOT NULL,
			size INTEGER NOT NULL,
			FOREIGN KEY (process_id) REFERENCES processes (id)
		)
	`)
	return err
}

// CreateProcess creates a new process record
func (db *DB) CreateProcess(process *Process) error {
	query := `
		INSERT INTO processes (name, status, start_time, command, args)
		VALUES (?, ?, ?, ?, ?)
	`
	result, err := db.Exec(query, process.Name, process.Status, process.StartTime, process.Command, process.Args)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	process.ID = id
	return nil
}

// UpdateProcess updates a process record
func (db *DB) UpdateProcess(process *Process) error {
	query := `
		UPDATE processes
		SET status = ?, end_time = ?, output = ?, exit_code = ?
		WHERE id = ?
	`
	_, err := db.Exec(query, process.Status, process.EndTime, process.Output, process.ExitCode, process.ID)
	return err
}

// GetProcess retrieves a process by ID
func (db *DB) GetProcess(id int64) (*Process, error) {
	query := `SELECT id, name, status, start_time, end_time, command, args, output, exit_code FROM processes WHERE id = ?`

	var process Process
	var endTime sql.NullTime
	var output sql.NullString

	err := db.QueryRow(query, id).Scan(
		&process.ID, &process.Name, &process.Status, &process.StartTime, &endTime,
		&process.Command, &process.Args, &output, &process.ExitCode,
	)

	if err != nil {
		return nil, err
	}

	if endTime.Valid {
		process.EndTime = endTime.Time
	}

	if output.Valid {
		process.Output = output.String
	}

	return &process, nil
}

// ListProcesses retrieves all processes
func (db *DB) ListProcesses() ([]*Process, error) {
	query := `SELECT id, name, status, start_time, end_time, command, args, output, exit_code FROM processes ORDER BY start_time DESC`

	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var processes []*Process

	for rows.Next() {
		var process Process
		var endTime sql.NullTime
		var output sql.NullString

		err := rows.Scan(
			&process.ID, &process.Name, &process.Status, &process.StartTime, &endTime,
			&process.Command, &process.Args, &output, &process.ExitCode,
		)

		if err != nil {
			return nil, err
		}

		if endTime.Valid {
			process.EndTime = endTime.Time
		}

		if output.Valid {
			process.Output = output.String
		}

		processes = append(processes, &process)
	}

	return processes, nil
}
