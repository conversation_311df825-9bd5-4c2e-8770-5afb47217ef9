package main

var mtdQry = `
select
    a.mth month_id,
    UPPER(a.mthf) period,
    CAST(CAST(a.mth as int) * 100 + a.as_of_dt as string) asof_date,
    coalesce(b.circle, 'Null') circle,
    coalesce(b.region, 'Null') region,
    a.brand,
    sum (case when a.parameter in ('Primary', 'primary') then a.amount else 0 end) "primary",
    sum (case when a.parameter in ('Secondary', 'secondary') then a.amount else 0 end) secondary,
    sum (case when a.parameter in ('Tertiary B#', 'tertiary') then a.amount else 0 end) tertiary
from
    rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
    left join
    biadm.ref_kecamatan b
    on
        a.kec_unik = b.kec_kabkot
where 1=1
  and a.dt_id = ${dt_id}
  and a.parameter in
      (
       'Primary', --im3
       'primary', -- 3id,
       'Secondary', --im3
       'secondary', --3id
       'Tertiary B#', --im3
       'tertiary' --3id
          )
    and a.mthf in ('mtd', 'lmtd')
group by 1,2,3,4,5,6

`
