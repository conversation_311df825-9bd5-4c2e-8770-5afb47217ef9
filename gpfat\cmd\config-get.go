/*
Copyright © 2024 <PERSON>iska Zarkasyi <<EMAIL>>
*/
package cmd

import (
	"bytes"
	"fmt"

	ctx "github.com/csee-pm/etl/shared/context"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"
)

// configSetCmd represents the cmd command
var configGetCmd = &cobra.Command{
	Use:   "config-get",
	Short: "get config value",
	Run:   configGetAction,
}

func initConfigGetCmd(v *viper.Viper) *cobra.Command {
	return configGetCmd
}

func configGetAction(cmd *cobra.Command, args []string) {
	beforeExec(cmd)
	conf := ctx.ExtractConfig(cmd.Context())
	setmap := conf.AllSettings()

	logger := ctx.ExtractLogger(cmd.Context())

	if len(args) == 0 {
		if err := printConfigMap(setmap); err != nil {
			logger.Error(err.Error())
		}
		return
	}

	setmap = make(map[string]any)
	for _, key := range args {
		setmap[key] = conf.Get(key)
	}

	if err := printConfigMap(setmap); err != nil {
		logger.Error(err.Error())
	}

}

func printConfigMap(setmap map[string]any) error {
	var b bytes.Buffer
	encoder := yaml.NewEncoder(&b)
	encoder.SetIndent(2)
	if err := encoder.Encode(setmap); err != nil {
		return err
	}

	fmt.Println(b.String())
	return nil
}
