package idm

import (
	"context"
	"fmt"
	"github.com/csee-pm/etl/shared/utils/apiutil"

	"github.com/csee-pm/etl/portal/backend/pkg/dto"
	"github.com/jmoiron/sqlx"
	"github.com/spf13/viper"
	"gopkg.in/guregu/null.v4"
)

type IdmAPI struct {
	userDAO     *UserDAO
	ldapService *LdapService
}

func CreateIdmAPI(conf *viper.Viper, db *sqlx.DB, ldapService *LdapService) (*IdmAPI, error) {
	userDAO, err := NewUserDAO(db)
	if err != nil {
		return nil, fmt.Errorf("failed to create user dao. %s", err)
	}

	return &IdmAPI{userDAO: userDAO, ldapService: ldapService}, nil
}

func (a *IdmAPI) GetUsersEndpoint(ctx context.Context, req dto.GetUsersRequestDTO) ([]User, error) {
	_, err := apiutil.CheckAuthFromContext(ctx, false)
	if err != nil {
		return nil, err
	}

	users, err := a.userDAO.GetUsers(req)
	if err != nil {
		return nil, err
	}

	return users, nil
}

func (a *IdmAPI) GetUserEndpoint(ctx context.Context, req dto.GetUserRequestDTO) (*User, error) {
	_, err := apiutil.CheckAuthFromContext(ctx, false)
	if err != nil {
		return nil, err
	}

	if req.Username.ValueOrZero() == "" {
		return nil, nil
	}

	user, err := a.userDAO.GetUser(req.Username.ValueOrZero())
	if err != nil {
		return nil, err
	}

	return user, nil
}

func (a *IdmAPI) CreateUserEndpoint(ctx context.Context, req dto.CreateUserRequestDTO) (*dto.CreateUserRequestDTO, error) {
	_, err := apiutil.CheckAuthFromContext(ctx, true)
	if err != nil {
		return nil, err
	}

	user := &User{
		Username: req.Username.ValueOrZero(),
		Fullname: req.Fullname.ValueOrZero(),
		IsAdmin:  req.IsAdmin.ValueOrZero(),
		Email:    req.Email.ValueOrZero(),
		IsLdap:   req.IsLdap.ValueOrZero(),
	}

	if !req.IsLdap.ValueOrZero() {
		hashedPassword := apiutil.PasswordHash(req.Password.ValueOrZero())
		user.HashedPassword = null.StringFrom(hashedPassword)
	}

	if req.IsLdap.ValueOrZero() {
		ldapUser, err := a.ldapService.GetByUsername(ctx, req.Username.ValueOrZero())
		if err != nil {
			return nil, err
		}

		user.Fullname = ldapUser.Fullname
		user.Email = ldapUser.Email
	}

	if err := a.userDAO.CreateUser(user); err != nil {
		return nil, err
	}

	return &req, nil
}
