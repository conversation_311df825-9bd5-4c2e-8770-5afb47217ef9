package process

import (
	"fmt"
	"time"
)

type ImpalaGaProdData struct {
	Brand       string  `db:"brand" parquet:"brand"`
	GaDate      string  `db:"ga_date" parquet:"ga_date"`
	FlagQuality string  `db:"flag_quality" parquet:"flag_quality"`
	ProductName string  `db:"product_name" parquet:"product_name"`
	PackageType string  `db:"package_type_ng" parquet:"package_type_ng"`
	Family      string  `db:"family_ng" parquet:"family_ng"`
	Inject      string  `db:"inject" parquet:"inject"`
	SiteFlag    string  `db:"site_flag" parquet:"site_flag"`
	SeaFlag     string  `db:"sea_flag" parquet:"sea_flag"`
	Channel     string  `db:"channel" parquet:"channel"`
	Msisdn      string  `db:"msisdn" parquet:"msisdn"`
	Revenue     float64 `db:"revenue" parquet:"revenue"`
	MthId       string  `db:"mth_id" parquet:"mth_id"`
}

func (d ImpalaGaProdData) ToBQGaProdData() (BQGaProdData, error) {
	//mthID is the last date of the mthid month
	mthId, err := time.Parse("200601", d.MthId)
	if err != nil {
		return BQGaProdData{}, fmt.Errorf("failed to parse mth_id: %q. %s", d.MthId, err)
	}
	mthId = mthId.AddDate(0, 1, -1)

	gaDate, err := time.Parse("20060102", d.GaDate)
	if err != nil {
		return BQGaProdData{}, fmt.Errorf("failed to parse ga_date: %q. %s", d.GaDate, err)
	}
	return BQGaProdData{
		Brand:       d.Brand,
		GaDate:      gaDate,
		FlagQuality: d.FlagQuality,
		ProductName: d.ProductName,
		PackageType: d.PackageType,
		Family:      d.Family,
		Inject:      d.Inject,
		SiteFlag:    d.SiteFlag,
		SeaFlag:     d.SeaFlag,
		Channel:     d.Channel,
		Msisdn:      d.Msisdn,
		Revenue:     d.Revenue,
		MthId:       mthId,
	}, nil
}

type BQGaProdData struct {
	Brand       string    `parquet:"brand"`
	GaDate      time.Time `parquet:"ga_date"`
	FlagQuality string    `parquet:"flag_quality"`
	ProductName string    `parquet:"product_name"`
	PackageType string    `parquet:"package_type_ng"`
	Family      string    `parquet:"family_ng"`
	Inject      string    `parquet:"inject"`
	SiteFlag    string    `parquet:"site_flag"`
	SeaFlag     string    `parquet:"sea_flag"`
	Channel     string    `parquet:"channel"`
	Msisdn      string    `parquet:"msisdn"`
	Revenue     float64   `parquet:"revenue"`
	MthId       time.Time `parquet:"mth_id"`
}
